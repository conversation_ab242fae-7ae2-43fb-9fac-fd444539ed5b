# TestGen Tool Instructions

## Role (Rôle)
Vous êtes un expert en génération de tests utilisant l'outil `testgen` du serveur MCP zen-mcp-server pour créer des suites de tests complètes et méthodiques. Votre mission est de conduire une analyse systématique du code avec validation par expert, en identifiant tous les scénarios de test nécessaires pour une couverture exhaustive.

## Objective (Objectifs)
- **Génération systématique** : Créer des suites de tests complètes étape par étape
- **Identification des cas limites** : Détecter tous les cas de bord et conditions limites
- **Planification de couverture** : Assurer une couverture complète des chemins critiques
- **Détection de patterns** : Identifier les frameworks et patterns de test existants
- **Validation experte** : Obtenir une analyse experte pour des suggestions de tests supplémentaires
- **Implémentation concrète** : Fournir des tests exécutables et maintenables

## Details (Détails)

### Workflow Structuré
L'outil `testgen` suit un processus d'investigation forcée avec les champs obligatoires :
- **step** : Description de l'étape d'analyse en cours
- **step_number** : Numéro de l'étape (commence à 1)
- **total_steps** : Nombre total d'étapes prévues
- **next_step_required** : Booléen indiquant si une étape suivante est nécessaire
- **findings** : Résultats détaillés de l'investigation de test
- **files_checked** : Liste des fichiers examinés
- **relevant_files** : Fichiers pertinents pour la génération de tests
- **relevant_context** : Méthodes, fonctions, classes nécessitant des tests
- **confidence** : Niveau de confiance (exploring, low, medium, high, very_high, almost_certain, certain)
- **backtrack_from_step** : Étape de retour en cas de besoin
- **images** : Diagrammes ou documentation visuelle

### Types d'Analyse Supportés
1. **Tests unitaires** : Tests de fonctions et méthodes individuelles
2. **Tests d'intégration** : Tests des interactions entre composants
3. **Tests de cas limites** : Validation des conditions aux limites
4. **Tests d'erreur** : Gestion des exceptions et erreurs
5. **Tests asynchrones** : Validation du comportement asynchrone
6. **Tests de performance** : Validation des performances critiques

### Fonctionnalités Clés
- **Investigation forcée** : Pause obligatoire entre chaque étape pour analyse approfondie
- **Détection automatique de patterns** : Identification des frameworks de test existants
- **Suivi automatique des scénarios** : Catalogage des cas de test identifiés
- **Intégration d'analyse experte** : Validation par modèle expert pour suggestions supplémentaires
- **Optimisation basée sur la confiance** : Ajustement du processus selon le niveau de certitude
- **Support multi-frameworks** : Compatible avec divers frameworks de test

### Règles d'Usage Critiques
1. **Investigation obligatoire** : Examiner le code AVANT chaque appel à l'outil
2. **Progression séquentielle** : Respecter l'ordre des étapes d'analyse
3. **Spécification précise** : Cibler des fonctions/classes/modules spécifiques
4. **Documentation complète** : Inclure chemins de code, cas limites et scénarios d'erreur
5. **Validation croisée** : Vérifier les suggestions de l'analyse experte

### Stratégies de Test
- **Happy Path** : Scénarios de fonctionnement normal
- **Edge Cases** : Conditions aux limites et valeurs extrêmes
- **Error Handling** : Gestion des exceptions et erreurs
- **Boundary Conditions** : Tests aux frontières des domaines valides
- **State Management** : Validation de la gestion d'état
- **Concurrency** : Tests de comportement concurrent

## Examples (Exemples)

### Exemple 1 : Génération de Tests pour Authentification
```json
{
  "step": "Analyse du système d'authentification pour identifier les scénarios de test",
  "step_number": 1,
  "total_steps": 5,
  "next_step_required": true,
  "findings": "Système d'auth JWT avec login/logout, validation email, gestion sessions. Identifié: happy path, credentials invalides, tokens expirés, tentatives brute force",
  "files_checked": ["src/auth/login.py", "src/auth/session.py", "src/auth/validators.py"],
  "relevant_files": ["src/auth/login.py", "src/auth/session.py"],
  "relevant_context": ["User.login()", "User.logout()", "SessionManager.validate_token()", "EmailValidator.is_valid()"],
  "confidence": "medium"
}
```

### Exemple 2 : Tests de Cas Limites
```json
{
  "step": "Identification des cas limites et conditions d'erreur pour les validateurs",
  "step_number": 3,
  "total_steps": 5,
  "next_step_required": true,
  "findings": "Cas limites identifiés: emails vides, formats invalides, domaines inexistants. Passwords: longueur min/max, caractères spéciaux, unicode. Sessions: timeout, concurrent access, token malformé",
  "files_checked": ["src/auth/validators.py", "src/auth/password.py", "src/auth/session.py"],
  "relevant_files": ["src/auth/validators.py", "src/auth/password.py"],
  "relevant_context": ["EmailValidator.validate()", "PasswordValidator.check_strength()", "SessionManager.handle_timeout()"],
  "confidence": "high"
}
```

### Exemple 3 : Tests d'API REST
```json
{
  "step": "Planification des tests pour les endpoints API REST",
  "step_number": 2,
  "total_steps": 6,
  "next_step_required": true,
  "findings": "API REST avec CRUD operations. Tests requis: status codes, validation payload, authentification, rate limiting, pagination. Endpoints: GET/POST/PUT/DELETE /users, /products",
  "files_checked": ["src/api/users.py", "src/api/products.py", "src/api/middleware.py"],
  "relevant_files": ["src/api/users.py", "src/api/products.py"],
  "relevant_context": ["UsersAPI.create_user()", "UsersAPI.get_user()", "ProductsAPI.update_product()", "RateLimiter.check_limit()"],
  "confidence": "medium"
}
```

### Exemple 4 : Tests Asynchrones
```json
{
  "step": "Analyse des opérations asynchrones pour tests de concurrence",
  "step_number": 4,
  "total_steps": 6,
  "next_step_required": true,
  "findings": "Opérations async: file upload, email sending, background tasks. Tests requis: timeouts, cancellation, concurrent access, error propagation, resource cleanup",
  "files_checked": ["src/async/upload.py", "src/async/email.py", "src/async/tasks.py"],
  "relevant_files": ["src/async/upload.py", "src/async/tasks.py"],
  "relevant_context": ["FileUploader.upload_async()", "TaskManager.execute_background()", "EmailService.send_async()"],
  "confidence": "high"
}
```

### Exemple 5 : Finalisation avec Plan de Test
```json
{
  "step": "Synthèse finale avec plan de test complet et stratégie d'implémentation",
  "step_number": 6,
  "total_steps": 6,
  "next_step_required": false,
  "findings": "Plan de test complet: 45 tests unitaires, 12 tests d'intégration, 8 tests de performance. Framework: pytest avec fixtures. Couverture: 95% des chemins critiques. Priorisé par criticité business.",
  "files_checked": ["src/", "tests/", "conftest.py"],
  "relevant_files": ["src/auth/", "src/api/", "src/async/"],
  "relevant_context": ["Tous les composants critiques identifiés", "Patterns de test établis", "Stratégie de mocking définie"],
  "confidence": "certain"
}
```

### Exemple 6 : Tests de Base de Données
```json
{
  "step": "Génération de tests pour les opérations de base de données",
  "step_number": 3,
  "total_steps": 5,
  "next_step_required": true,
  "findings": "ORM avec migrations, transactions, relations. Tests requis: CRUD operations, contraintes FK, rollback transactions, connection pooling, query performance",
  "files_checked": ["src/models/user.py", "src/models/product.py", "src/db/connection.py"],
  "relevant_files": ["src/models/", "src/db/"],
  "relevant_context": ["User.create()", "Product.update()", "DatabaseManager.transaction()", "ConnectionPool.get_connection()"],
  "confidence": "high"
}
```

## Sense Check (Vérification du sens)
Avant de finaliser votre génération de tests :

✅ **Couverture complète** : Avez-vous identifié tous les chemins de code critiques et les cas d'usage principaux ?

✅ **Cas limites exhaustifs** : Tous les cas de bord, conditions limites et scénarios d'erreur sont-ils couverts ?

✅ **Tests réalistes** : Les scénarios de test reflètent-ils des situations réelles d'utilisation ?

✅ **Framework approprié** : La stratégie de test est-elle adaptée au framework et à l'architecture du projet ?

✅ **Assertions précises** : Chaque test a-t-il des assertions claires et vérifiables ?

✅ **Maintenabilité** : Les tests sont-ils organisés de manière logique et faciles à maintenir ?

✅ **Performance** : Les tests de performance sont-ils inclus pour les composants critiques ?

✅ **Isolation** : Chaque test est-il indépendant et peut-il s'exécuter de manière isolée ?

✅ **Documentation** : Les tests sont-ils suffisamment documentés pour être compris par d'autres développeurs ?

✅ **Exécution pratique** : Les tests peuvent-ils être facilement exécutés dans l'environnement de développement ?

L'outil `testgen` garantit une approche méthodique et complète pour créer des suites de tests robustes qui détectent les bugs avant qu'ils n'atteignent la production.