# Template Product - Documentation Produit

## Instructions Intégrées pour l'Agent IA

### Objectif
Créer une documentation produit complète qui établit la vision, les objectifs stratégiques, et le contexte métier pour guider toutes les décisions de développement du projet.

### Processus Obligatoire
1. **Génération initiale** : Créer product.md basé sur le brief projet SANS questions séquentielles préalables
2. **Structure complète** : Utiliser toutes les sections du template avec analyse approfondie
3. **Méthodologie banque de mémoire** : Structurer l'information pour faciliter la réutilisation
4. **Validation utilisateur** : Cycle feedback-révision jusqu'à approbation explicite
5. **Documentation de référence** : Créer un document qui servira de source de vérité

### Contraintes Techniques
- Créer le fichier dans `sdd/project/product.md`
- Analyser le contexte métier et utilisateurs en profondeur
- Définir des métriques mesurables et des objectifs SMART
- Demander validation avec l'outil approprié
- Continuer les révisions jusqu'à approbation explicite

---

# Vision Produit et Objectifs - {PROJECT_NAME}

## Vision

[Décrire la vision produit - Une déclaration claire et inspirante de ce que le produit vise à accomplir et de l'impact qu'il aura sur les utilisateurs et l'organisation]

### Mission
[Définir la mission du produit - Pourquoi ce produit existe et quelle valeur fondamentale il apporte]

### Vision à Long Terme
[Décrire où vous voulez que le produit soit dans 2-3 ans]

## Objectifs Stratégiques

### 1. [Objectif Stratégique Principal]
- **Mesure de succès** : [Métrique principale]
- **Timeline** : [Délai pour atteindre l'objectif]
- **Impact attendu** : [Bénéfice concret]

### 2. [Objectif Stratégique Secondaire]
- **Mesure de succès** : [Métrique associée]
- **Timeline** : [Délai]
- **Impact attendu** : [Bénéfice]

### 3. [Objectif Stratégique Tertiaire]
- **Mesure de succès** : [Métrique associée]
- **Timeline** : [Délai]
- **Impact attendu** : [Bénéfice]

### Objectifs SMART
- **S**pécifiques : [Objectifs précis et bien définis]
- **M**esurables : [Métriques et KPIs clairement définis]
- **A**tteignables : [Objectifs réalistes selon les ressources]
- **R**elevants : [Alignés avec la stratégie globale]
- **T**emporels : [Échéances claires pour chaque objectif]

## Analyse du Marché et Contexte

### Problème Résolu
- **Problème principal** : [Description détaillée du problème majeur]
- **Problèmes secondaires** : [Autres problèmes adressés]
- **Impact du problème** : [Coût/conséquence de ne pas résoudre]

### Opportunité de Marché
- **Taille du marché** : [TAM, SAM, SOM si applicable]
- **Tendances du marché** : [Évolutions favorables]
- **Timing** : [Pourquoi maintenant est le bon moment]

### Analyse Concurrentielle
- **Concurrents directs** : [Solutions similaires existantes]
- **Concurrents indirects** : [Alternatives que les utilisateurs utilisent actuellement]
- **Avantages concurrentiels** : [Ce qui nous différencie]

## Public Cible et Utilisateurs

### Segmentation Utilisateurs

#### Utilisateurs Primaires
- **Segment 1** : [Description du segment principal]
  - Besoins principaux : [Liste des besoins]
  - Frustrations actuelles : [Points de douleur]
  - Comportements : [Comment ils utilisent les solutions actuelles]

#### Utilisateurs Secondaires
- **Segment 2** : [Description du segment secondaire]
  - Besoins principaux : [Liste des besoins]
  - Influence sur la décision : [Leur rôle dans l'adoption]

### Personas Détaillés

#### {Persona_1_Name} - {Titre/Rôle}
- **Démographie** : [Âge, localisation, expérience professionnelle]
- **Objectifs** : [Ce qu'ils veulent accomplir]
- **Frustrations** : [Obstacles actuels]
- **Comportements** : [Comment ils travaillent/vivent]
- **Canaux préférés** : [Comment ils découvrent et utilisent les produits]
- **Citation représentative** : "[Une phrase qui résume leur perspective]"

#### {Persona_2_Name} - {Titre/Rôle}
- **Démographie** : [Informations démographiques]
- **Objectifs** : [Objectifs principaux]
- **Frustrations** : [Points de douleur spécifiques]
- **Comportements** : [Habitudes et préférences]
- **Canaux préférés** : [Préférences de communication]
- **Citation représentative** : "[Phrase caractéristique]"

### Journey Mapping
- **Découverte** : [Comment les utilisateurs découvrent le besoin]
- **Évaluation** : [Processus de sélection des solutions]
- **Adoption** : [Processus d'intégration et première utilisation]
- **Utilisation** : [Utilisation quotidienne/régulière]
- **Advocacy** : [Promotion et recommandation]

## Proposition de Valeur

### Proposition de Valeur Unique (UVP)
[Une phrase claire qui explique la valeur unique que votre produit apporte]

### Bénéfices Clés

#### Pour les Utilisateurs Primaires
1. **[Bénéfice 1]** : [Description détaillée]
   - Avant : [Situation actuelle]
   - Après : [Situation avec le produit]
   - Mesure : [Comment mesurer l'amélioration]

2. **[Bénéfice 2]** : [Description détaillée]
   - Avant : [Situation actuelle]
   - Après : [Situation améliorée]
   - Mesure : [Métrique d'amélioration]

#### Pour l'Organisation
1. **[Bénéfice Business 1]** : [Impact sur l'organisation]
2. **[Bénéfice Business 2]** : [Valeur économique]
3. **[Bénéfice Business 3]** : [Avantage stratégique]

### Différenciateurs Concurrentiels
1. **[Différenciateur 1]** : [En quoi nous sommes uniques]
   - Comparaison : [vs concurrence]
   - Avantage : [Pourquoi c'est important]

2. **[Différenciateur 2]** : [Autre point de différenciation]
   - Comparaison : [vs alternatives]
   - Avantage : [Valeur ajoutée]

### Value Proposition Canvas
- **Customer Jobs** : [Tâches que les clients essaient d'accomplir]
- **Pains** : [Problèmes et frustrations]
- **Gains** : [Bénéfices et résultats désirés]
- **Pain Relievers** : [Comment nous soulageons les problèmes]
- **Gain Creators** : [Comment nous créons de la valeur]
- **Products & Services** : [Nos solutions concrètes]

## Fonctionnalités Stratégiques

### Fonctionnalités Core (Must-Have)
1. **[Fonctionnalité 1]** : [Description et justification]
   - Valeur utilisateur : [Bénéfice direct]
   - Priorité : [Critique/Haute/Moyenne]
   - Complexité : [Estimation]

2. **[Fonctionnalité 2]** : [Description et importance]
   - Valeur utilisateur : [Impact]
   - Priorité : [Niveau]
   - Complexité : [Effort estimé]

### Fonctionnalités Différenciatrices (Should-Have)
1. **[Fonctionnalité Unique 1]** : [Ce qui nous distingue]
2. **[Fonctionnalité Unique 2]** : [Avantage concurrentiel]

### Fonctionnalités Futures (Could-Have)
1. **[Fonctionnalité Future 1]** : [Vision long terme]
2. **[Fonctionnalité Future 2]** : [Extension possible]

## Métriques de Succès

### Métriques Produit Clés

#### Adoption et Engagement
- **MAU/DAU** : [Utilisateurs actifs mensuels/quotidiens]
- **Taux d'adoption** : [% d'utilisateurs qui adoptent le produit]
- **Temps d'engagement** : [Durée moyenne d'utilisation]
- **Fréquence d'utilisation** : [Combien de fois par période]

#### Performance Business
- **Revenus** : [Impact financier direct]
- **Coût d'acquisition** : [CAC - Customer Acquisition Cost]
- **Valeur vie client** : [LTV - Lifetime Value]
- **ROI** : [Retour sur investissement]

#### Satisfaction Utilisateur
- **NPS** : [Net Promoter Score cible]
- **CSAT** : [Customer Satisfaction Score]
- **Taux de rétention** : [% d'utilisateurs qui continuent à utiliser]
- **Churn rate** : [Taux d'abandon]

### Métriques de Validation

#### Métriques de Développement
- **Time to Market** : [Délai de mise sur le marché]
- **Vélocité équipe** : [Points d'histoire par sprint]
- **Qualité** : [Nombre de bugs, temps de résolution]
- **Performance technique** : [Temps de réponse, uptime]

#### Métriques d'Apprentissage
- **Hypothèses validées** : [% d'hypothèses confirmées]
- **Feedback loops** : [Fréquence des retours utilisateurs]
- **Pivots/ajustements** : [Changements basés sur les données]

### Tableaux de Bord
- **Dashboard exécutif** : [Métriques pour la direction]
- **Dashboard produit** : [Métriques pour l'équipe produit]
- **Dashboard technique** : [Métriques pour les développeurs]

## Roadmap Produit Stratégique

### Vision Timeline (3 ans)

#### Année 1 : Foundation
- **Q1** : [Objectifs premier trimestre]
  - Milestone 1 : [Étape importante]
  - Milestone 2 : [Autre étape]
- **Q2** : [Objectifs deuxième trimestre]
- **Q3** : [Objectifs troisième trimestre]
- **Q4** : [Objectifs quatrième trimestre]

#### Année 2 : Growth
- **Objectifs annuels** : [Focus principal année 2]
- **Nouvelles fonctionnalités** : [Développements majeurs]
- **Expansion** : [Nouveaux segments/marchés]

#### Année 3 : Scale
- **Vision mature** : [Où nous voulons être]
- **Leadership marché** : [Position concurrentielle]
- **Innovation** : [Prochaines disruptions]

### Phases de Développement

#### Phase 1 : MVP (Minimum Viable Product)
- **Durée** : [Timeline]
- **Fonctionnalités** : [Core features pour valider]
- **Objectif** : [Apprentissage principal]
- **Critères de succès** : [Métriques de validation]

#### Phase 2 : Product-Market Fit
- **Durée** : [Timeline]
- **Fonctionnalités** : [Développements pour l'adoption]
- **Objectif** : [Atteindre PMF]
- **Critères de succès** : [Métriques PMF]

#### Phase 3 : Scale
- **Durée** : [Timeline]
- **Fonctionnalités** : [Features pour scaler]
- **Objectif** : [Croissance massive]
- **Critères de succès** : [Métriques de croissance]

## Contraintes et Considérations

### Contraintes Business
- **Budget** : [Limitations financières]
- **Timeline** : [Contraintes temporelles]
- **Ressources** : [Limitations d'équipe]
- **Réglementations** : [Conformité requise]

### Contraintes Techniques
- **Infrastructure** : [Limitations techniques]
- **Intégrations** : [Systèmes existants à respecter]
- **Performance** : [Exigences de performance]
- **Sécurité** : [Standards de sécurité]

### Contraintes Utilisateurs
- **Adoption** : [Résistance au changement]
- **Formation** : [Besoin d'apprentissage]
- **Migration** : [Transition depuis solutions existantes]

### Considérations Éthiques
- **Vie privée** : [Protection des données]
- **Accessibilité** : [Inclusion et accessibilité]
- **Impact social** : [Conséquences sociétales]
- **Transparence** : [Explicabilité des décisions]

## Analyse des Risques

### Risques Produit
- **Risque 1** : [Description]
  - Probabilité : [Faible/Moyenne/Élevée]
  - Impact : [Faible/Moyen/Élevé]
  - Mitigation : [Plan de réduction]

- **Risque 2** : [Description]
  - Probabilité : [Niveau]
  - Impact : [Niveau]
  - Mitigation : [Stratégie]

### Risques Marché
- **Concurrence** : [Nouveaux entrants, évolution concurrentielle]
- **Adoption** : [Résistance du marché]
- **Timing** : [Fenêtre d'opportunité]

### Risques Techniques
- **Scalabilité** : [Limites techniques]
- **Sécurité** : [Vulnérabilités potentielles]
- **Intégration** : [Complexité d'intégration]

## Écosystème et Partenariats

### Stakeholders Internes
- **Équipe produit** : [Rôles et responsabilités]
- **Équipe technique** : [Implication dans les décisions]
- **Marketing/Ventes** : [Collaboration sur le go-to-market]
- **Support client** : [Feedback et amélioration continue]

### Partenaires Externes
- **Fournisseurs technologiques** : [Partenaires clés]
- **Canaux de distribution** : [Comment atteindre les utilisateurs]
- **Intégrateurs** : [Partenaires d'implémentation]

### Écosystème Produit
- **Intégrations** : [Produits complémentaires]
- **APIs** : [Ouverture vers l'écosystème]
- **Marketplace** : [Plateforme d'extensions]

---

## Instructions de Validation pour l'Agent

### Questions de Validation Suggérées
1. "La vision produit est-elle claire et inspirante ?"
2. "Les personas reflètent-ils fidèlement les utilisateurs cibles ?"
3. "La proposition de valeur est-elle différenciée et convaincante ?"
4. "Les métriques de succès sont-elles mesurables et pertinentes ?"
5. "La roadmap est-elle réaliste et alignée avec les objectifs ?"
6. "Les contraintes et risques sont-ils bien identifiés ?"

### Format de Validation
```
**Validation Documentation Produit :**
J'ai terminé le document product.md avec :
- Vision et objectifs stratégiques clairs
- [X] personas détaillés et journey mapping
- Proposition de valeur unique et différenciée
- Métriques de succès mesurables
- Roadmap stratégique sur 3 ans
- Analyse complète des contraintes et risques

**Cette documentation produit vous convient-elle ? Elle servira de référence pour toutes les décisions de développement.**
```

### Cycle de Révision
1. Présenter la documentation initiale complète
2. Demander validation explicite avec l'outil approprié
3. Si modifications demandées : réviser les sections concernées
4. Répéter jusqu'à approbation explicite
5. Utiliser comme référence pour les spécifications techniques
