# Document de Design - User Authentication

## Vue d'ensemble

### Résumé Exécutif
Système d'authentification basé sur JWT avec architecture stateless, protection CSRF et gestion sécurisée des sessions pour une application web moderne.

### Objectifs du Design
- **Objectif principal** : Implémenter une authentification sécurisée et performante
- **Objectifs secondaires** : Faciliter l'intégration frontend, maintenir la scalabilité
- **Contraintes respectées** : Performance < 500ms, sécurité OWASP, support 1000 utilisateurs simultanés

### Décisions de Design Clés
1. **JWT stateless** : Évite le stockage serveur des sessions - *Justification : Scalabilité et performance*
2. **httpOnly cookies** : Protection contre XSS - *Justification : Sécurité renforcée*
3. **bcrypt avec salt 12** : Chiffrement robuste - *Justification : Standard industrie*
4. **Middleware Express** : Protection routes centralisée - *Justification : Réutilisabilité et maintenabilité*

### Recherches Effectuées
- **JWT vs Sessions** : JWT choisi pour scalabilité horizontale et API stateless
- **Stockage tokens** : httpOnly cookies préférés aux localStorage pour sécurité
- **Algorithmes chiffrement** : bcrypt validé comme standard actuel vs scrypt/argon2
- **Sources principales** : OWASP, RFC 7519, Node.js Security Best Practices

## Architecture

### Architecture Globale

```mermaid
graph TB
    Client["Client Browser"] --> LB["Load Balancer"]
    LB --> API["Express API Server"]
    API --> Auth["Auth Middleware"]
    Auth --> JWT["JWT Service"]
    Auth --> DB[("PostgreSQL")]
    API --> Routes["Protected Routes"]
    
    subgraph "Authentication Flow"
        Login["Login Endpoint"]
        Validate["Token Validation"]
        Logout["Logout Endpoint"]
    end
    
    API --> Login
    Auth --> Validate
    API --> Logout
```

### Patterns Architecturaux
- **Pattern principal** : Middleware Pattern pour authentification
- **Justification** : Séparation des préoccupations, réutilisabilité, testabilité
- **Patterns secondaires** : Repository Pattern pour accès données, Factory Pattern pour JWT

### Stack Technique

#### Backend
- **Runtime** : Node.js 18+ - *Justification : Performance, écosystème mature*
- **Framework** : Express.js 4.18+ - *Justification : Simplicité, middleware ecosystem*
- **Base de données** : PostgreSQL 14+ - *Justification : ACID, performance, sécurité*
- **ORM** : Prisma - *Justification : Type safety, migrations automatiques*

#### Sécurité
- **JWT Library** : jsonwebtoken 9.0+ - *Justification : Standard de facto*
- **Password Hashing** : bcrypt 5.1+ - *Justification : Résistance aux attaques*
- **Validation** : joi 17.0+ - *Justification : Validation robuste des inputs*

#### Infrastructure
- **Hébergement** : Docker containers sur cloud provider
- **Reverse Proxy** : nginx pour terminaison SSL
- **Monitoring** : Winston pour logs, Prometheus pour métriques

### Diagramme de Déploiement

```mermaid
deployment
    node "Client Browser" {
        component "React App" as ReactApp
    }
    
    node "Load Balancer" {
        component "nginx" as nginx
    }
    
    node "App Server" {
        component "Express API" as API
        component "Auth Service" as AuthService
        component "JWT Service" as JWTService
    }
    
    node "Database Server" {
        database "PostgreSQL" as DB
    }
    
    ReactApp --> nginx : HTTPS
    nginx --> API : HTTP
    API --> AuthService
    AuthService --> JWTService
    AuthService --> DB
```

## Composants et Interfaces

### Composants Principaux

#### 1. AuthController
- **Responsabilité** : Gestion des endpoints d'authentification
- **Technologies** : Express.js, joi validation
- **Dépendances** : AuthService, JWTService
- **Interfaces exposées** : POST /auth/login, POST /auth/logout, GET /auth/me

#### 2. AuthService
- **Responsabilité** : Logique métier d'authentification
- **Technologies** : bcrypt, business logic
- **Dépendances** : UserRepository, JWTService
- **Interfaces exposées** : authenticate(), createSession(), validateSession()

#### 3. JWTService
- **Responsabilité** : Gestion des tokens JWT
- **Technologies** : jsonwebtoken library
- **Dépendances** : Configuration secrets
- **Interfaces exposées** : generateToken(), verifyToken(), refreshToken()

#### 4. AuthMiddleware
- **Responsabilité** : Protection des routes
- **Technologies** : Express middleware
- **Dépendances** : JWTService
- **Interfaces exposées** : requireAuth(), optionalAuth()

#### 5. UserRepository
- **Responsabilité** : Accès aux données utilisateur
- **Technologies** : Prisma ORM
- **Dépendances** : PostgreSQL database
- **Interfaces exposées** : findByEmail(), updateLastLogin(), incrementFailedAttempts()

### Diagramme de Composants

```mermaid
graph TB
    subgraph "Controllers Layer"
        AuthController["AuthController"]
    end
    
    subgraph "Services Layer"
        AuthService["AuthService"]
        JWTService["JWTService"]
    end
    
    subgraph "Middleware Layer"
        AuthMiddleware["AuthMiddleware"]
        ValidationMiddleware["ValidationMiddleware"]
    end
    
    subgraph "Repository Layer"
        UserRepository["UserRepository"]
    end
    
    subgraph "Database Layer"
        PostgreSQL[("PostgreSQL")]
    end
    
    AuthController --> AuthService
    AuthController --> ValidationMiddleware
    AuthService --> JWTService
    AuthService --> UserRepository
    AuthMiddleware --> JWTService
    UserRepository --> PostgreSQL
```

### APIs et Interfaces

#### API REST

```yaml
# Endpoints d'authentification
POST /api/auth/login:
  body: { email: string, password: string }
  response: { user: User, token: string } | { error: string }
  
POST /api/auth/logout:
  headers: { Cookie: jwt_token }
  response: { message: string }
  
GET /api/auth/me:
  headers: { Cookie: jwt_token }
  response: { user: User } | { error: string }
  
POST /api/auth/refresh:
  headers: { Cookie: jwt_token }
  response: { token: string } | { error: string }
```

#### Interfaces de Données

```typescript
// Interfaces TypeScript principales
interface User {
  id: string;
  email: string;
  password_hash: string;
  created_at: Date;
  updated_at: Date;
  last_login?: Date;
  failed_attempts: number;
  locked_until?: Date;
}

interface JWTPayload {
  userId: string;
  email: string;
  iat: number;
  exp: number;
}

interface AuthRequest extends Request {
  user?: User;
}

interface LoginRequest {
  email: string;
  password: string;
}

interface AuthResponse {
  user: Omit<User, 'password_hash'>;
  token: string;
}
```

## Modèles de Données

### Schéma de Base de Données

```mermaid
erDiagram
    USERS {
        uuid id PK
        varchar email UK
        varchar password_hash
        timestamp created_at
        timestamp updated_at
        timestamp last_login
        integer failed_attempts
        timestamp locked_until
    }
    
    SESSIONS {
        uuid id PK
        uuid user_id FK
        varchar token_hash
        timestamp created_at
        timestamp expires_at
        boolean is_active
    }
    
    USERS ||--o{ SESSIONS : has
```

### Entités Principales

#### Users Table
```sql
CREATE TABLE users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  email VARCHAR(255) UNIQUE NOT NULL,
  password_hash VARCHAR(255) NOT NULL,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW(),
  last_login TIMESTAMP,
  failed_attempts INTEGER DEFAULT 0,
  locked_until TIMESTAMP,
  
  CONSTRAINT email_format CHECK (email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$')
);
```

#### Sessions Table (pour blacklist tokens)
```sql
CREATE TABLE sessions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  token_hash VARCHAR(255) NOT NULL,
  created_at TIMESTAMP DEFAULT NOW(),
  expires_at TIMESTAMP NOT NULL,
  is_active BOOLEAN DEFAULT true,
  
  INDEX idx_token_hash (token_hash),
  INDEX idx_user_sessions (user_id, is_active)
);
```

### Index et Performance
```sql
-- Index principaux pour la performance
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_locked ON users(locked_until) WHERE locked_until IS NOT NULL;
CREATE INDEX idx_sessions_active ON sessions(token_hash, is_active) WHERE is_active = true;
CREATE INDEX idx_sessions_cleanup ON sessions(expires_at) WHERE is_active = true;
```

### Migrations
- **Migration 001** : Création table users avec contraintes
- **Migration 002** : Création table sessions pour token blacklist
- **Migration 003** : Index de performance

## Flux de Données

### Flux Connexion Utilisateur

```mermaid
sequenceDiagram
    participant C as Client
    participant API as Express API
    participant Auth as AuthService
    participant JWT as JWTService
    participant DB as PostgreSQL
    
    C->>API: POST /auth/login {email, password}
    API->>Auth: authenticate(email, password)
    Auth->>DB: findUserByEmail(email)
    DB-->>Auth: user data
    Auth->>Auth: bcrypt.compare(password, hash)
    Auth->>JWT: generateToken(userId)
    JWT-->>Auth: jwt token
    Auth->>DB: updateLastLogin(userId)
    Auth-->>API: {user, token}
    API->>API: set httpOnly cookie
    API-->>C: 200 {user} + Set-Cookie
```

### Flux Validation Token

```mermaid
sequenceDiagram
    participant C as Client
    participant MW as AuthMiddleware
    participant JWT as JWTService
    participant DB as PostgreSQL
    participant Route as Protected Route
    
    C->>MW: Request with Cookie
    MW->>MW: extract token from cookie
    MW->>JWT: verifyToken(token)
    JWT-->>MW: decoded payload
    MW->>DB: checkTokenBlacklist(tokenHash)
    DB-->>MW: token status
    MW->>Route: request with user context
    Route-->>C: protected resource
```

### États et Transitions

```mermaid
stateDiagram-v2
    [*] --> Anonymous
    Anonymous --> Authenticating : login attempt
    Authenticating --> Authenticated : valid credentials
    Authenticating --> Locked : too many failures
    Authenticating --> Anonymous : invalid credentials
    Authenticated --> Anonymous : logout
    Authenticated --> Anonymous : token expired
    Locked --> Anonymous : lock timeout
    Authenticated --> Authenticated : token refresh
```

## Gestion des Erreurs

### Stratégie Globale
- **Logging** : Winston avec niveaux (error, warn, info, debug)
- **Monitoring** : Prometheus métriques + Grafana dashboards
- **Alerting** : Alertes sur taux d'erreur > 5%, tentatives brute force

### Types d'Erreurs

#### Erreurs Utilisateur (4xx)
- **400 Bad Request** : Données invalides, validation échouée
- **401 Unauthorized** : Token invalide, expiré, ou absent
- **403 Forbidden** : Compte verrouillé, permissions insuffisantes
- **429 Too Many Requests** : Rate limiting dépassé

#### Erreurs Système (5xx)
- **500 Internal Server Error** : Erreur base de données, service indisponible
- **503 Service Unavailable** : Maintenance, surcharge système

### Récupération et Résilience
- **Retry Logic** : 3 tentatives avec backoff exponentiel pour DB
- **Circuit Breaker** : Protection contre cascade failures
- **Fallback** : Mode dégradé sans fonctionnalités non-critiques

### Format des Erreurs
```json
{
  "error": {
    "code": "AUTH_INVALID_CREDENTIALS",
    "message": "Identifiants invalides",
    "details": {
      "field": "email",
      "reason": "user_not_found"
    },
    "timestamp": "2024-01-01T00:00:00Z",
    "requestId": "req_123456789"
  }
}
```

## Sécurité

### Authentification
- **Méthode** : JWT avec signature HMAC SHA-256
- **Durée** : 24h par défaut, refresh automatique si activité < 2h
- **Stockage** : httpOnly, Secure, SameSite=Strict cookies

### Autorisation
- **Modèle** : Simple authenticated/anonymous
- **Extension future** : RBAC avec rôles user/admin
- **Middleware** : Vérification token sur routes protégées

### Protection des Données
- **Chiffrement en transit** : TLS 1.3 obligatoire
- **Chiffrement au repos** : PostgreSQL encryption at rest
- **Données sensibles** : Mots de passe hachés bcrypt, tokens signés

### Validation et Sanitisation
- **Input validation** : joi schemas pour tous les endpoints
- **Output encoding** : Échappement automatique des réponses JSON
- **SQL Injection** : Prisma ORM avec requêtes préparées

### Protection Attaques
```typescript
// Rate limiting
const loginLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 5, // 5 tentatives par IP
  message: 'Trop de tentatives de connexion'
});

// Account lockout
const LOCK_TIME = 15 * 60 * 1000; // 15 minutes
const MAX_ATTEMPTS = 5;
```

## Performance

### Objectifs de Performance
- **Temps de réponse login** : < 500ms (95e percentile)
- **Validation token** : < 100ms (99e percentile)
- **Concurrence** : 1000 utilisateurs simultanés
- **Débit** : 10,000 requêtes/minute

### Stratégies d'Optimisation

#### Backend
- **Connection Pooling** : Pool PostgreSQL 20 connexions max
- **Caching** : Redis pour blacklist tokens, sessions actives
- **Index Database** : Index optimisés sur email, token_hash

#### Monitoring Performance
- **Métriques clés** : Response time, error rate, throughput
- **Outils** : Prometheus + Grafana, APM monitoring
- **Alertes** : Latence > 1s, error rate > 5%

## Stratégie de Test

### Pyramide de Tests

#### Tests Unitaires (70%)
- **Framework** : Jest + Supertest
- **Couverture cible** : 90%+
- **Composants testés** : AuthService, JWTService, UserRepository

#### Tests d'Intégration (20%)
- **Framework** : Jest + Test Database
- **APIs testées** : Tous les endpoints auth
- **Base de données** : PostgreSQL test instance

#### Tests E2E (10%)
- **Framework** : Playwright
- **Scénarios** : Flux complet login/logout
- **Environnement** : Staging environment

### Tests de Sécurité
- **OWASP** : Tests injection, XSS, CSRF
- **Penetration Testing** : Tests brute force, token manipulation
- **Dependency Scanning** : npm audit, Snyk

## Plan d'Implémentation

### Phase 1 : Foundation (Semaine 1)
- [ ] Setup projet Express + TypeScript
- [ ] Configuration PostgreSQL + Prisma
- [ ] Modèles de données et migrations
- [ ] Configuration JWT et bcrypt

### Phase 2 : Core Auth (Semaine 2)
- [ ] AuthService avec login/logout
- [ ] JWTService pour token management
- [ ] UserRepository avec Prisma
- [ ] Tests unitaires services

### Phase 3 : API Endpoints (Semaine 3)
- [ ] AuthController avec endpoints
- [ ] Validation middleware avec joi
- [ ] Gestion d'erreurs globale
- [ ] Tests d'intégration API

### Phase 4 : Security & Polish (Semaine 4)
- [ ] AuthMiddleware pour protection routes
- [ ] Rate limiting et account lockout
- [ ] Logging et monitoring
- [ ] Tests E2E et sécurité

### Dépendances et Risques

#### Dépendances Critiques
- **PostgreSQL** : Base de données opérationnelle - *Mitigation : Docker compose*
- **Secrets Management** : Clés JWT sécurisées - *Mitigation : Variables environnement*

#### Risques Identifiés
- **Performance DB** : Latence élevée - *Mitigation : Index optimisés, connection pooling*
- **Sécurité JWT** : Compromission clé - *Mitigation : Rotation clés, monitoring*

### Critères de Succès
- [ ] Tous les requirements implémentés et testés
- [ ] Performance < 500ms pour login
- [ ] Sécurité validée (OWASP top 10)
- [ ] Tests passent avec 90%+ couverture
- [ ] Documentation API complète

## Évolutions Futures

### Version 2.0
- **2FA** : Authentification à deux facteurs
- **OAuth** : Connexion sociale (Google, GitHub)
- **RBAC** : Système de rôles et permissions
- **SSO** : Single Sign-On enterprise

### Scalabilité
- **Limites actuelles** : 1000 utilisateurs simultanés
- **Plans de scaling** : Load balancing, Redis cluster
- **Refactoring futur** : Microservices auth séparé

---

*Ce design respecte tous les requirements et fournit une base solide pour l'implémentation d'un système d'authentification sécurisé et performant.*