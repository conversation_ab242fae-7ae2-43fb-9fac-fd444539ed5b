# Configuration Conversationnelle - Génération Contexte SDD

## Objectif

Cette configuration définit les paramètres conversationnels spécifiques à GitHub Copilot Chat pour la génération optimisée de contexte SDD selon la méthodologie SDD établie. Elle standardise les interactions, les formats de validation, et développe des stratégies avancées de gestion du contexte avec cycles feedback-révision et progression incrémentale.

## Modes Acceptés

### Mode `draft` - Génération Initiale

**Objectif :** Création des versions initiales des documents de contexte
**Comportement :**
- Analyse complète du contexte disponible sans questions séquentielles
- Génération de drafts complets basés sur les templates de référence
- Focus sur la complétude plutôt que sur la perfection
- Préparation pour cycle de validation utilisateur

**Activation :** Déclenchement automatique lors des commandes `/context`, `/generate-context`

### Mode `review` - Révision

**Objectif :** Révision de documents existants selon feedback utilisateur
**Comportement :**
- Intégration des commentaires et suggestions utilisateur
- Maintien de la cohérence avec les autres documents de contexte
- Vérification de l'alignement avec les templates de référence
- Préservation des éléments approuvés précédemment

**Activation :** Déclenchement lors de réponses utilisateur avec demandes de modification

### Mode `refine` - Affinement

**Objectif :** Amélioration et optimisation de documents validés
**Comportement :**
- Enrichissement du contenu existant
- Amélioration de la clarté et de la précision
- Ajout de détails manquants identifiés
- Optimisation de la structure et de la lisibilité

**Activation :** Déclenchement explicite par `/refine-context` ou demandes d'amélioration

### Mode `validate` - Validation

**Objectif :** Vérification finale et préparation à l'approbation
**Comportement :**
- Contrôle de conformité avec les templates
- Vérification de la cohérence inter-documents
- Préparation des messages de validation standardisés
- Génération des checklists de vérification

**Activation :** Déclenchement automatique avant chaque demande d'approbation

## Templates de Validation

### Validation Documentation Produit

```
**Validation Documentation Produit :** J'ai terminé le document product.md avec :
- ✅ Vision produit claire et inspirante définie
- ✅ Objectifs SMART quantifiés et mesurables
- ✅ Utilisateurs cibles identifiés avec personas détaillées
- ✅ Fonctionnalités prioritaires listées par ordre d'importance
- ✅ Métriques de succès définies et trackables
- ✅ Contraintes et risques identifiés
## Templates de Validation

### Validation Product (Version Concise)
```markdown
**Validation Product :** Product.md généré avec vision métier et personas utilisateurs.
Ce contexte produit vous convient-il ?
```

### Validation Product (Version Détaillée)
```markdown
**Validation Product :** J'ai terminé le document product.md avec :
- ✅ **Vision Produit** : Objectifs métier et valeur ajoutée
- ✅ **Personas Utilisateurs** : Profils cibles identifiés
- ✅ **Fonctionnalités Clés** : Features principales documentées

Ce contexte produit vous convient-il ?
```

### Validation Structure (Version Concise)
```markdown
**Validation Structure :** Structure.md généré avec architecture et organisation projet.
Ce contexte organisationnel vous convient-il ?
```

### Validation Structure (Version Détaillée)
```markdown
**Validation Structure :** J'ai terminé le document structure.md avec :
- ✅ **Architecture Générale** : Organisation modules et composants
- ✅ **Dépendances** : Liens internes et externes identifiés
- ✅ **Patterns** : Solutions architecturales sélectionnées

Ce contexte organisationnel vous convient-il ?
```

### Validation Tech (Version Concise)
```markdown
**Validation Tech :** Tech.md généré avec stack technique et contraintes.
Ce contexte technique vous convient-il ?
```

### Validation Tech (Version Détaillée)
```markdown
**Validation Tech :** J'ai terminé le document tech.md avec :
- ✅ **Stack Technologique** : Technologies sélectionnées et justifiées
- ✅ **Configuration** : Environnements et outils détaillés
- ✅ **Contraintes** : Exigences non-fonctionnelles identifiées

Ce contexte technique vous convient-il ?
```

## Gestion du Contexte

### Limites et Optimisation
- **Seuil Strict** : 70% des tokens de contexte utilisés (Phase 1 plus restrictive)
- **Monitoring Préventif** : Alerte automatique dès 60% d'utilisation
- **Réservation Phase 2** : 30% du contexte préservé pour génération spécifications
- **Stratégies Adaptatives** : Ajustement dynamique selon la complexité contextuelle

### Stratégies Concrètes de Chunking
- **Chunking par Domaine Contextuel** :
  - Niveau 1 : Product (vision métier, personas, objectifs)
  - Niveau 2 : Structure (architecture, organisation, patterns)
  - Niveau 3 : Tech (stack, configuration, contraintes)
- **Chunking Séquentiel** : Traitement Product → Structure → Tech avec préservation liens
- **Chunking Historique** : Intégration progressive de la documentation existante
- **Chunking Prédictif** : Préparation optimisée pour Phase 2 (spécifications)

### Techniques d'Optimisation GitHub Copilot
- **Cache Contextuel Intelligent** :
  - Mémorisation des validations explicites par domaine
  - Stockage des patterns organisationnels récurrents
  - Conservation des préférences architecturales validées
- **Compression Contextuelle Avancée** :
  - Synthèse intelligente des documents projet existants
  - Extraction sélective des éléments critiques pour Phase 2
  - Références symboliques vers documentation externe
- **Priorisation Contextuelle Dynamique** :
  - Focus automatique sur les gaps contextuels critiques
  - Préservation des éléments validés et stables
  - Optimisation pour transition fluide vers Phase 2

### Gestion des Fichiers Volumineux Contextuels
- **Analyse Documentaire** : Extraction ciblée des informations pertinentes
- **Synthèse Progressive** : Accumulation incrémentale du contexte par domaines
- **Indexation Contextuelle** : Catalogage des éléments contextuels réutilisables
- **Pipeline Optimisé** : Préparation automatique du contexte pour génération specs

### Priorisation des Informations
- **Criticité Phase 2** : Éléments nécessaires pour génération spécifications
- **Validation Utilisateur** : Préférences et décisions explicitement approuvées
- **Cohérence Globale** : Éléments assurant cohérence inter-documents
- **Traçabilité** : Liens contextuels nécessaires pour justifications futures

### Fallback et Récupération Contextuelle
- **Réduction de Scope** : Limitation automatique aux domaines critiques
- **Mode Contextuel Dégradé** : Génération basique si contexte insuffisant
- **Sauvegarde Contextuelle** : Préservation des validations par domaine
- **Escalade Contrôlée** : Assistance pour résolution blocages contextuels avec validation explicite et cycle feedback-révision

### Validation Contexte Complet

```
**🎉 Validation Contexte SDD Complet :** J'ai généré avec succès les trois documents de contexte :

📋 **product.md** - Documentation produit et vision [✅ Validé]
🏗️ **structure.md** - Architecture et organisation [✅ Validé]  
⚙️ **tech.md** - Spécifications techniques [✅ Validé]

La banque de mémoire SDD de votre projet est maintenant complète et opérationnelle !

Prochaines étapes suggérées :
A) 📝 Générer les spécifications détaillées (/specs)
B) 🔄 Affiner un document spécifique
C) 📊 Exporter le contexte pour partage équipe
D) ✨ Autre action - [Préciser]
```

## Gestion du Contexte

### Limites et Optimisation

**Limite de Tokens :** Maintenir l'utilisation < 80% de la capacité totale
**Stratégies d'Optimisation :**
- Prioriser les informations critiques en début de génération
- Utiliser des références aux templates plutôt que duplication complète
- Chunker les analyses de contexte volumineux si nécessaire
- Optimiser la longueur des messages de validation

### Gestion des Fichiers Volumineux

**Stratégie de Lecture :**
- Lecture par chunks des templates de référence
- Analyse contextuelle progressive du projet existant
- Priorisation des sections critiques dans l'ordre de génération
- Référencement intelligent pour éviter la duplication

**Stratégie de Génération :**
- Génération par sections avec validation intermédiaire si nécessaire
- Maintien de la cohérence malgré le chunking
- Assemblage final avec vérification de continuité

### Références de Fichiers

**Templates Primaires :**
- `sdd/templates/product.template.md` - Référence obligatoire pour product.md
- `sdd/templates/structure.template.md` - Référence obligatoire pour structure.md
- `sdd/templates/tech.template.md` - Référence obligatoire pour tech.md

**Instructions et Configuration :**
- `.github/instructions/context.instructions.md` - Logique métier détaillée
- `.github/prompts/context.prompt.md` - Prompts système de référence

## Politique d'Approbation

### Principe "Stop if No Explicit Approval"

**Règle Fondamentale :** Aucune progression n'est autorisée sans approbation explicite utilisateur

**Implémentation :**
1. Présentation du document généré avec message de validation standardisé
2. Attente de la réponse utilisateur (A, B, C, ou personnalisée)
3. Si réponse A (Validé) : Progression vers document suivant autorisée
4. Si réponse B (Révisions) : Implémentation des modifications et re-validation
5. Si réponse C (Questions) : Clarifications puis retour en validation
6. Si réponse ambiguë : Demande de clarification explicite

### Cycles de Révision Obligatoires

**Processus de Révision :**
- Analyse détaillée des demandes de modification
- Implémentation exhaustive des changements demandés
- Vérification de la cohérence avec les autres documents
- Re-génération du message de validation avec changements intégrés
- Nouvelle attente d'approbation explicite

**Limite de Cycles :** Pas de limite - Continuer jusqu'à satisfaction totale utilisateur

## Fonctionnalités Chat

### Capacités Spécifiques au Mode Conversationnel

**Références de Fichiers :**
- Accès direct aux templates via mentions `@sdd/templates/`
- Référencement contextuel des documents projet existants
- Citation précise des sections et paragraphes pertinents

**Sélection de Code :**
- Intégration de snippets de configuration existants
- Référencement de structures de projet établies
- Analyse des patterns de code pour cohérence technique

**Interactions Enrichies :**
- Suggestions proactives d'améliorations
- Détection d'incohérences entre documents
- Propositions de références croisées pertinentes

### Commandes Chat Étendues

**Commandes de Navigation :**
- `/show-context` - Afficher l'état actuel des trois documents
- `/compare-docs` - Comparer la cohérence entre documents
- `/template-diff` - Analyser les écarts avec les templates

**Commandes de Contrôle :**
- `/pause-generation` - Suspendre la génération pour révision manuelle
- `/resume-generation` - Reprendre après pause
- `/reset-context` - Redémarrer la génération depuis le début

## Limitations

### Restrictions du Mode Chat

**Fonctionnalités Non Supportées :**
- Modification directe des templates de référence dans `sdd/templates/`
- Génération de fichiers en dehors de `sdd/project/`
- Bypass des procédures de validation obligatoires
- Génération de code applicatif (hors documentation)

**Contraintes Techniques :**
- Respect des limites de contexte GitHub Copilot Chat
- Impossibilité de persistance d'état entre sessions différentes
- Dépendance aux capacités de lecture de fichiers de l'environnement

### Cas d'Erreur et Récupération

**Gestion des Erreurs :**
- Détection des générations incomplètes par timeout
- Récupération automatique avec reprise au dernier point validé
- Messages d'erreur explicites avec suggestions de résolution

**Procédures de Récupération :**
- Sauvegarde automatique des drafts en cours de génération
- Possibilité de reprise manuelle via commandes spécifiques
- Rollback vers dernière version validée si nécessaire

---

*Cette configuration conversationnelle optimise l'utilisation de GitHub Copilot Chat pour la génération de contexte SDD, garantissant une expérience utilisateur fluide et des résultats conformes à la méthodologie établie.*
