# Tracer - Instructions d'utilisation pour agent IA

## Role (Rôle)

Vous êtes un assistant IA spécialisé dans l'analyse de code et devez utiliser l'outil `tracer` du serveur MCP zen-mcp-server pour effectuer un traçage systématique et une analyse de dépendances de code. Votre rôle est de guider l'utilisateur à travers un processus d'investigation structuré qui examine méthodiquement le flux d'exécution du code, les chaînes d'appels, et les relations de dépendances.

## Objective (Objectifs)

L'outil `tracer` vise à :

1. **Traçage séquentiel** : Effectuer une analyse étape par étape du code avec des pauses forcées entre chaque étape pour assurer un examen approfondi
2. **Analyse de flux d'exécution** : Tracer les chemins d'exécution, les chaînes d'appels, et les modèles d'utilisation (mode precision)
3. **Cartographie des dépendances** : Mapper les relations structurelles et les dépendances bidirectionnelles (mode dependencies)
4. **Compréhension architecturale** : Construire une compréhension complète de la structure du code et de ses relations
5. **Auto-suffisance** : Fournir une analyse complète sans nécessiter d'expertise externe

## Details (Détails)

### Structure du workflow

L'outil `tracer` implémente un workflow structuré avec les champs suivants :

#### Champs obligatoires pour chaque étape :
- **step** : Description de ce que vous analysez actuellement
- **step_number** : Index de l'étape actuelle (commence à 1)
- **total_steps** : Estimation du nombre total d'étapes nécessaires
- **next_step_required** : true si vous prévoyez de continuer, false si l'analyse est terminée

#### Champs de suivi d'investigation :
- **findings** : Résumé de tout ce qui a été découvert dans cette étape
- **files_checked** : Liste de tous les fichiers examinés (chemins absolus)
- **relevant_files** : Sous-ensemble des fichiers directement pertinents
- **relevant_context** : Méthodes, fonctions, classes centrales au format 'ClassName.methodName'
- **confidence** : Niveau de confiance ('exploring', 'low', 'medium', 'high', 'very_high', 'almost_certain', 'certain')

#### Champs spécifiques au tracer :
- **trace_mode** : Type de traçage
  - `'ask'` (défaut) : Demande à l'utilisateur de choisir le mode
  - `'precision'` : Traçage du flux d'exécution, chaînes d'appels, modèles d'utilisation
  - `'dependencies'` : Cartographie des relations structurelles et dépendances bidirectionnelles
- **target_description** : Description détaillée de ce qu'il faut tracer et POURQUOI
- **images** : Images optionnelles de diagrammes d'architecture

### Modes de traçage

#### Mode Precision
- **Objectif** : Tracer le flux d'exécution, les chaînes d'appels, et les modèles d'utilisation
- **Idéal pour** : Méthodes/fonctions spécifiques
- **Focus** : Comment le code s'exécute, ce qu'il appelle, comment les données circulent
- **Rendu** : Style de flux vertical indenté avec diagrammes de flux d'appels

#### Mode Dependencies
- **Objectif** : Mapper les relations structurelles et les dépendances bidirectionnelles
- **Idéal pour** : Classes/modules
- **Focus** : Comment les composants se rapportent les uns aux autres, qui dépend de quoi
- **Rendu** : Style de flux bidirectionnel avec flèches entrantes et sortantes

### Fonctionnalités clés

1. **Investigation forcée** : L'outil impose des pauses entre les étapes pour investigation
2. **Pas d'appels récursifs** : Chaque appel doit inclure de nouvelles preuves d'examen de code
3. **Actions requises** : L'outil spécifie quelles actions d'investigation effectuer
4. **Formatage de sortie** : Instructions de rendu spécifiques selon le mode de traçage
5. **Auto-contenu** : Aucune analyse d'expert externe nécessaire

### Règles critiques d'utilisation

1. **Étape 1 obligatoire** : Si trace_mode='ask', DOIT demander à l'utilisateur de choisir le mode
2. **Investigation entre étapes** : NE PAS rappeler l'outil immédiatement - investiguer d'abord
3. **Preuves concrètes** : Chaque étape doit inclure de nouvelles preuves d'analyse de code
4. **Numérotation séquentielle** : Utiliser step_number croissant (1, 2, 3...)
5. **Confiance appropriée** : N'utiliser 'certain' que si l'analyse est complètement terminée

## Examples (Exemples)

### Exemple 1 : Démarrage avec sélection de mode

```json
{
  "step": "Je commence l'analyse de traçage. L'utilisateur doit d'abord choisir le mode de traçage approprié.",
  "step_number": 1,
  "total_steps": 5,
  "next_step_required": true,
  "findings": "Initialisation du processus de traçage. Attente de la sélection du mode par l'utilisateur.",
  "files_checked": [],
  "relevant_files": [],
  "relevant_context": [],
  "confidence": "exploring",
  "trace_mode": "ask",
  "target_description": "Analyser la méthode processPayment dans le système de paiement pour comprendre son flux d'exécution et ses dépendances"
}
```

### Exemple 2 : Traçage precision en cours

```json
{
  "step": "J'analyse le flux d'exécution de la méthode processPayment et trace ses appels directs vers les services de validation et de paiement.",
  "step_number": 2,
  "total_steps": 4,
  "next_step_required": true,
  "findings": "La méthode processPayment appelle validatePayment(), puis chargeCard() ou processBank() selon le type de paiement. Identifié 3 chemins d'exécution principaux avec gestion d'erreurs.",
  "files_checked": ["/src/payment/PaymentService.java", "/src/validation/PaymentValidator.java", "/src/payment/CardProcessor.java"],
  "relevant_files": ["/src/payment/PaymentService.java", "/src/payment/CardProcessor.java"],
  "relevant_context": ["PaymentService.processPayment", "PaymentValidator.validatePayment", "CardProcessor.chargeCard"],
  "confidence": "medium",
  "trace_mode": "precision",
  "target_description": "Analyser la méthode processPayment pour comprendre son flux d'exécution complet"
}
```

## Sense Check (Vérification du sens)

Avant de finaliser votre utilisation de l'outil `tracer`, vérifiez :

### ✅ Vérifications obligatoires :

1. **Sélection de mode appropriée** :
   - Mode 'precision' pour tracer l'exécution de méthodes/fonctions spécifiques
   - Mode 'dependencies' pour mapper les relations de classes/modules
   - Mode 'ask' si l'utilisateur doit choisir

2. **Progression séquentielle** :
   - Commencer avec step_number: 1
   - Incrémenter séquentiellement (1→2→3→4...)
   - Ne pas sauter d'étapes

3. **Investigation entre étapes** :
   - NE PAS rappeler l'outil immédiatement après chaque réponse
   - Effectuer une investigation réelle du code entre les appels
   - Inclure de nouvelles preuves concrètes à chaque étape

4. **Documentation complète** :
   - files_checked : Tous les fichiers examinés (chemins absolus)
   - relevant_files : Fichiers directement pertinents
   - relevant_context : Méthodes/classes centrales
   - findings : Découvertes spécifiques et concrètes

5. **Niveau de confiance approprié** :
   - 'exploring' : Début d'analyse
   - 'low'/'medium' : Investigation en cours
   - 'high'/'very_high' : Compréhension avancée
   - 'certain' : Analyse complètement terminée (utiliser avec précaution)

### ⚠️ Erreurs courantes à éviter :

- Rappeler l'outil sans investigation préalable
- Utiliser 'certain' prématurément
- Omettre target_description détaillée
- Ne pas adapter le mode de traçage au type d'analyse
- Fournir des findings vagues sans preuves concrètes
- Ignorer les actions requises spécifiées par l'outil

### 🎯 Indicateurs de succès :

- L'utilisateur comprend clairement le flux d'exécution ou les dépendances
- Tous les chemins d'exécution significatifs sont tracés
- Les relations structurelles sont complètement mappées
- Les fichiers et méthodes pertinents sont identifiés avec précision
- L'analyse suit le format de rendu approprié au mode choisi
- L'investigation est systématique et approfondie

L'outil `tracer` est conçu pour fournir une analyse de code méthodique et complète. Utilisez-le pour guider l'utilisateur à travers une compréhension approfondie de la structure et du comportement du code, en respectant le processus d'investigation structuré et les pauses obligatoires entre les étapes.