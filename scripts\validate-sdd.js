#!/usr/bin/env node

/**
 * Script de validation du système SDD (Spec-Driven Development)
 * Vérifie l'intégrité de la structure et des fichiers selon Spec-Standard.md
 */

const fs = require('fs');
const path = require('path');

class SDDValidator {
  constructor(rootPath = process.cwd()) {
    this.rootPath = rootPath;
    this.sddPath = path.join(rootPath, 'sdd');
    this.errors = [];
    this.warnings = [];
    this.info = [];
  }

  /**
   * Validation principale du système SDD
   */
  async validate() {
    console.log('🔍 Validation du système SDD...');
    console.log(`📁 Répertoire racine: ${this.rootPath}`);
    console.log(`📁 Répertoire SDD: ${this.sddPath}`);
    console.log('');

    // Vérifications de structure
    this.validateSDDStructure();
    this.validateProjectContext();
    this.validateTemplates();
    this.validateSpecs();

    // Vérifications de contenu
    this.validateFileContents();
    this.validateReferences();

    // Rapport final
    this.generateReport();

    return this.errors.length === 0;
  }

  /**
   * Vérifie la structure de base du système SDD
   */
  validateSDDStructure() {
    this.info.push('Validation de la structure SDD...');

    // Vérifier l'existence du dossier sdd/
    if (!fs.existsSync(this.sddPath)) {
      this.errors.push('❌ Dossier sdd/ manquant à la racine du projet');
      return;
    }

    // Vérifier les dossiers obligatoires
    const requiredDirs = ['project', 'templates', 'specs'];
    requiredDirs.forEach(dir => {
      const dirPath = path.join(this.sddPath, dir);
      if (!fs.existsSync(dirPath)) {
        this.errors.push(`❌ Dossier sdd/${dir}/ manquant`);
      } else {
        this.info.push(`✅ Dossier sdd/${dir}/ présent`);
      }
    });

    // Vérifier README.md
    const readmePath = path.join(this.sddPath, 'README.md');
    if (!fs.existsSync(readmePath)) {
      this.warnings.push('⚠️  Fichier sdd/README.md manquant (recommandé)');
    } else {
      this.info.push('✅ Fichier sdd/README.md présent');
    }
  }

  /**
   * Vérifie les fichiers de contexte projet
   */
  validateProjectContext() {
    this.info.push('Validation du contexte projet...');

    const projectPath = path.join(this.sddPath, 'project');
    if (!fs.existsSync(projectPath)) {
      return; // Déjà signalé dans validateSDDStructure
    }

    // Fichiers obligatoires selon Spec-Standard.md
    const requiredFiles = [
      'product.md',
      'structure.md', 
      'tech.md'
    ];

    // Fichiers optionnels créés
    const optionalFiles = [
      'context.md',
      'guidelines.md'
    ];

    requiredFiles.forEach(file => {
      const filePath = path.join(projectPath, file);
      if (!fs.existsSync(filePath)) {
        this.errors.push(`❌ Fichier sdd/project/${file} manquant`);
      } else {
        this.info.push(`✅ Fichier sdd/project/${file} présent`);
      }
    });

    optionalFiles.forEach(file => {
      const filePath = path.join(projectPath, file);
      if (fs.existsSync(filePath)) {
        this.info.push(`✅ Fichier sdd/project/${file} présent (optionnel)`);
      }
    });
  }

  /**
   * Vérifie les templates
   */
  validateTemplates() {
    this.info.push('Validation des templates...');

    const templatesPath = path.join(this.sddPath, 'templates');
    if (!fs.existsSync(templatesPath)) {
      return; // Déjà signalé dans validateSDDStructure
    }

    // Templates obligatoires selon Spec-Standard.md
    const requiredTemplates = [
      'requirements.template.md',
      'design.template.md',
      'tasks.template.md'
    ];

    // Templates optionnels créés
    const optionalTemplates = [
      'system-prompt.template.md',
      'task-execution.template.md'
    ];

    requiredTemplates.forEach(template => {
      const templatePath = path.join(templatesPath, template);
      if (!fs.existsSync(templatePath)) {
        this.errors.push(`❌ Template sdd/templates/${template} manquant`);
      } else {
        this.info.push(`✅ Template sdd/templates/${template} présent`);
      }
    });

    optionalTemplates.forEach(template => {
      const templatePath = path.join(templatesPath, template);
      if (fs.existsSync(templatePath)) {
        this.info.push(`✅ Template sdd/templates/${template} présent (optionnel)`);
      }
    });
  }

  /**
   * Vérifie les spécifications
   */
  validateSpecs() {
    this.info.push('Validation des spécifications...');

    const specsPath = path.join(this.sddPath, 'specs');
    if (!fs.existsSync(specsPath)) {
      return; // Déjà signalé dans validateSDDStructure
    }

    const specDirs = fs.readdirSync(specsPath, { withFileTypes: true })
      .filter(dirent => dirent.isDirectory())
      .map(dirent => dirent.name);

    if (specDirs.length === 0) {
      this.warnings.push('⚠️  Aucune spécification trouvée dans sdd/specs/');
      return;
    }

    specDirs.forEach(specDir => {
      this.validateSpecDirectory(specDir);
    });
  }

  /**
   * Vérifie un dossier de spécification individuel
   */
  validateSpecDirectory(specName) {
    const specPath = path.join(this.sddPath, 'specs', specName);
    
    // Vérifier le format kebab-case
    if (!/^[a-z0-9]+(-[a-z0-9]+)*$/.test(specName)) {
      this.warnings.push(`⚠️  Nom de spec '${specName}' ne respecte pas le format kebab-case`);
    }

    // Fichiers obligatoires pour chaque spec
    const requiredSpecFiles = [
      'requirements.md',
      'design.md',
      'tasks.md'
    ];

    requiredSpecFiles.forEach(file => {
      const filePath = path.join(specPath, file);
      if (!fs.existsSync(filePath)) {
        this.errors.push(`❌ Fichier sdd/specs/${specName}/${file} manquant`);
      } else {
        this.info.push(`✅ Fichier sdd/specs/${specName}/${file} présent`);
      }
    });
  }

  /**
   * Vérifie le contenu des fichiers
   */
  validateFileContents() {
    this.info.push('Validation du contenu des fichiers...');

    // Vérifier les templates
    this.validateTemplateContents();
    
    // Vérifier les spécifications
    this.validateSpecContents();
  }

  /**
   * Vérifie le contenu des templates
   */
  validateTemplateContents() {
    const templatesPath = path.join(this.sddPath, 'templates');
    
    // Vérifier requirements.template.md
    const reqTemplatePath = path.join(templatesPath, 'requirements.template.md');
    if (fs.existsSync(reqTemplatePath)) {
      const content = fs.readFileSync(reqTemplatePath, 'utf8');
      if (!content.includes('EARS')) {
        this.warnings.push('⚠️  Template requirements ne mentionne pas le format EARS');
      }
      if (!content.includes('REQ-')) {
        this.warnings.push('⚠️  Template requirements ne mentionne pas les IDs REQ-X');
      }
    }

    // Vérifier design.template.md
    const designTemplatePath = path.join(templatesPath, 'design.template.md');
    if (fs.existsSync(designTemplatePath)) {
      const content = fs.readFileSync(designTemplatePath, 'utf8');
      if (!content.includes('mermaid')) {
        this.warnings.push('⚠️  Template design ne mentionne pas les diagrammes Mermaid');
      }
    }

    // Vérifier tasks.template.md
    const tasksTemplatePath = path.join(templatesPath, 'tasks.template.md');
    if (fs.existsSync(tasksTemplatePath)) {
      const content = fs.readFileSync(tasksTemplatePath, 'utf8');
      if (!content.includes('checklist')) {
        this.warnings.push('⚠️  Template tasks ne mentionne pas le format checklist');
      }
    }
  }

  /**
   * Vérifie le contenu des spécifications
   */
  validateSpecContents() {
    const specsPath = path.join(this.sddPath, 'specs');
    if (!fs.existsSync(specsPath)) return;

    const specDirs = fs.readdirSync(specsPath, { withFileTypes: true })
      .filter(dirent => dirent.isDirectory())
      .map(dirent => dirent.name);

    specDirs.forEach(specDir => {
      this.validateSpecFileContents(specDir);
    });
  }

  /**
   * Vérifie le contenu des fichiers d'une spécification
   */
  validateSpecFileContents(specName) {
    const specPath = path.join(this.sddPath, 'specs', specName);

    // Vérifier requirements.md
    const reqPath = path.join(specPath, 'requirements.md');
    if (fs.existsSync(reqPath)) {
      const content = fs.readFileSync(reqPath, 'utf8');
      
      // Vérifier format EARS
      if (!content.includes('WHEN') || !content.includes('THE SYSTEM') || !content.includes('SHALL')) {
        this.warnings.push(`⚠️  ${specName}/requirements.md ne semble pas utiliser le format EARS complet`);
      }
      
      // Vérifier IDs requirements
      const reqMatches = content.match(/REQ-\d+/g);
      if (!reqMatches || reqMatches.length === 0) {
        this.warnings.push(`⚠️  ${specName}/requirements.md ne contient pas d'IDs REQ-X`);
      }
    }

    // Vérifier design.md
    const designPath = path.join(specPath, 'design.md');
    if (fs.existsSync(designPath)) {
      const content = fs.readFileSync(designPath, 'utf8');
      
      // Vérifier sections obligatoires
      const requiredSections = ['Vue d\'ensemble', 'Architecture', 'Composants'];
      requiredSections.forEach(section => {
        if (!content.includes(section)) {
          this.warnings.push(`⚠️  ${specName}/design.md manque la section '${section}'`);
        }
      });
    }

    // Vérifier tasks.md
    const tasksPath = path.join(specPath, 'tasks.md');
    if (fs.existsSync(tasksPath)) {
      const content = fs.readFileSync(tasksPath, 'utf8');
      
      // Vérifier format checklist
      if (!content.includes('- [ ]')) {
        this.warnings.push(`⚠️  ${specName}/tasks.md ne semble pas utiliser le format checklist`);
      }
      
      // Vérifier références aux requirements
      if (!content.includes('REQ-')) {
        this.warnings.push(`⚠️  ${specName}/tasks.md ne référence pas les requirements`);
      }
    }
  }

  /**
   * Vérifie les références croisées entre documents
   */
  validateReferences() {
    this.info.push('Validation des références croisées...');
    
    const specsPath = path.join(this.sddPath, 'specs');
    if (!fs.existsSync(specsPath)) return;

    const specDirs = fs.readdirSync(specsPath, { withFileTypes: true })
      .filter(dirent => dirent.isDirectory())
      .map(dirent => dirent.name);

    specDirs.forEach(specDir => {
      this.validateSpecReferences(specDir);
    });
  }

  /**
   * Vérifie les références dans une spécification
   */
  validateSpecReferences(specName) {
    const specPath = path.join(this.sddPath, 'specs', specName);
    
    // Collecter tous les IDs REQ-X du requirements.md
    const reqPath = path.join(specPath, 'requirements.md');
    let requirementIds = [];
    
    if (fs.existsSync(reqPath)) {
      const reqContent = fs.readFileSync(reqPath, 'utf8');
      const matches = reqContent.match(/REQ-\d+/g);
      if (matches) {
        requirementIds = [...new Set(matches)];
      }
    }

    // Vérifier les références dans design.md
    const designPath = path.join(specPath, 'design.md');
    if (fs.existsSync(designPath)) {
      const designContent = fs.readFileSync(designPath, 'utf8');
      const designRefs = designContent.match(/REQ-\d+/g) || [];
      
      designRefs.forEach(ref => {
        if (!requirementIds.includes(ref)) {
          this.warnings.push(`⚠️  ${specName}/design.md référence ${ref} qui n'existe pas dans requirements.md`);
        }
      });
    }

    // Vérifier les références dans tasks.md
    const tasksPath = path.join(specPath, 'tasks.md');
    if (fs.existsSync(tasksPath)) {
      const tasksContent = fs.readFileSync(tasksPath, 'utf8');
      const tasksRefs = tasksContent.match(/REQ-\d+/g) || [];
      
      tasksRefs.forEach(ref => {
        if (!requirementIds.includes(ref)) {
          this.warnings.push(`⚠️  ${specName}/tasks.md référence ${ref} qui n'existe pas dans requirements.md`);
        }
      });
    }
  }

  /**
   * Génère le rapport final
   */
  generateReport() {
    console.log('\n📊 RAPPORT DE VALIDATION SDD');
    console.log('=' .repeat(50));
    
    // Statistiques
    console.log(`\n📈 Statistiques:`);
    console.log(`   ✅ Validations réussies: ${this.info.length}`);
    console.log(`   ⚠️  Avertissements: ${this.warnings.length}`);
    console.log(`   ❌ Erreurs: ${this.errors.length}`);

    // Erreurs
    if (this.errors.length > 0) {
      console.log('\n❌ ERREURS CRITIQUES:');
      this.errors.forEach(error => console.log(`   ${error}`));
    }

    // Avertissements
    if (this.warnings.length > 0) {
      console.log('\n⚠️  AVERTISSEMENTS:');
      this.warnings.forEach(warning => console.log(`   ${warning}`));
    }

    // Informations (mode verbose)
    if (process.argv.includes('--verbose') || process.argv.includes('-v')) {
      console.log('\n✅ VALIDATIONS RÉUSSIES:');
      this.info.forEach(info => console.log(`   ${info}`));
    }

    // Conclusion
    console.log('\n' + '=' .repeat(50));
    if (this.errors.length === 0) {
      console.log('🎉 VALIDATION RÉUSSIE - Le système SDD est conforme!');
      if (this.warnings.length > 0) {
        console.log('💡 Considérez les avertissements pour améliorer la qualité.');
      }
    } else {
      console.log('💥 VALIDATION ÉCHOUÉE - Corrigez les erreurs critiques.');
    }
    
    console.log('\n💡 Utilisez --verbose pour voir tous les détails.');
  }
}

// Exécution du script
if (require.main === module) {
  const validator = new SDDValidator();
  validator.validate().then(success => {
    process.exit(success ? 0 : 1);
  }).catch(error => {
    console.error('❌ Erreur lors de la validation:', error.message);
    process.exit(1);
  });
}

module.exports = SDDValidator;