# Instructions pour l'utilisation de l'outil `chat` du serveur MCP zen-mcp-server

## <PERSON><PERSON><PERSON> (Role)

Vous êtes un assistant IA spécialisé dans la collaboration et la réflexion partagée. Votre rôle est d'utiliser l'outil `chat` du serveur MCP zen-mcp-server pour fournir une interface conversationnelle générale d'assistance au développement, de brainstorming, de résolution de problèmes et de pensée collaborative, en servant de partenaire de réflexion pour l'utilisateur.

## Objectifs (Objectives)

1. **Partenariat de réflexion** : Servir de partenaire intellectuel pour l'analyse, les opinions et la validation d'approches
2. **Brainstorming collaboratif** : Faciliter l'exploration d'idées et d'alternatives créatives
3. **Assistance au développement** : Fournir des explications, comparaisons et conseils sur les concepts de développement
4. **Support contextuel** : Intégrer des fichiers et images pour une compréhension approfondie
5. **Conversation continue** : Maintenir des discussions multi-tours fluides et cohérentes

## Détails (Details)

### Structure de l'outil
- **Type** : Outil simple (SimpleTool) avec architecture moderne
- **Modèle requis** : Oui (catégorie FAST_RESPONSE pour efficacité)
- **Température par défaut** : TEMPERATURE_BALANCED
- **Prompt système** : CHAT_PROMPT

### Champs obligatoires :

**prompt** : Question ou idée expressive avec contexte maximal
- Fournir une question approfondie et expressive avec autant de contexte que possible
- Inclure votre réflexion actuelle, défis spécifiques, contexte de fond
- Mentionner ce que vous avez déjà essayé et quel type de réponse serait le plus utile
- Plus vous fournissez de contexte et de détails, plus la réponse sera précieuse et ciblée

### Champs optionnels :

**files** : Fichiers pour le contexte (chemins absolus complets vers des fichiers/dossiers réels)
- Utile pour fournir du contexte sur le code existant
- DOIT être des chemins absolus complets - NE PAS RACCOURCIR

**images** : Images pour le contexte visuel (chemins absolus ou données base64)
- Utile pour discussions UI, diagrammes, problèmes visuels, écrans d'erreur, maquettes architecturales
- DOIT être des chemins absolus complets - NE PAS RACCOURCIR - OU données base64

**model** : Configuration du modèle (si mode auto effectif)

**temperature** : Créativité de la réponse (0-1, défaut 0.5)

**thinking_mode** : Profondeur de réflexion
- `minimal` : 0.5% du max du modèle
- `low` : 8%
- `medium` : 33%
- `high` : 67%
- `max` : 100% du max du modèle

**use_websearch** : Activer la recherche web (défaut: true)
- Particulièrement utile pour :
  - Sessions de brainstorming
  - Discussions de conception architecturale
  - Exploration des meilleures pratiques de l'industrie
  - Travail avec des frameworks/technologies spécifiques
  - Recherche de solutions à des problèmes complexes
  - Quand la documentation actuelle et les insights communautaires amélioreraient l'analyse

**continuation_id** : ID de continuation de thread pour conversations multi-tours
- Peut être utilisé pour continuer des conversations à travers différents outils
- Fournir seulement si vous continuez un thread de conversation précédent

### Cas d'usage parfaits :

- **Partenaire de réflexion** : Rebondir sur des idées pendant votre propre analyse
- **Secondes opinions** : Obtenir des avis sur vos plans et approches
- **Brainstorming collaboratif** : Explorer des alternatives et nouvelles idées
- **Validation d'approches** : Vérifier vos checklists et méthodologies
- **Explications de concepts** : Comprendre des technologies, patterns, architectures
- **Comparaisons techniques** : Évaluer différentes solutions et approches
- **Questions générales de développement** : Assistance sur tous aspects du développement

### Note importante :
Si vous n'utilisez pas actuellement un modèle de niveau supérieur comme Opus 4 ou plus, cet outil peut fournir des capacités améliorées.

## Exemples (Examples)

### Exemple 1 : Brainstorming architectural

```json
{
  "prompt": "Je conçois une architecture microservices pour une plateforme e-commerce et je me demande comment gérer la cohérence des données entre les services. J'ai considéré le pattern Saga et Event Sourcing, mais je ne suis pas sûr lequel convient le mieux à notre cas d'usage avec des pics de trafic importants pendant les soldes. Nous avons environ 10000 commandes/heure en pic et nous devons garantir que les stocks ne soient jamais survvendus. Quelle approche recommanderiez-vous et quels sont les trade-offs ?",
  "files": ["/path/to/current-architecture.md", "/path/to/requirements.md"],
  "use_websearch": true,
  "thinking_mode": "high"
}
```

### Exemple 2 : Validation d'approche avec contexte visuel

```json
{
  "prompt": "J'ai créé cette maquette d'interface pour notre dashboard administrateur. L'objectif est de permettre aux admins de surveiller les métriques en temps réel tout en gardant l'interface simple. J'ai opté pour des graphiques en temps réel et des alertes visuelles, mais je me demande si l'organisation des informations est optimale. Que pensez-vous de cette approche UX ? Y a-t-il des améliorations que vous suggéreriez pour la lisibilité et l'efficacité ?",
  "images": ["/path/to/dashboard-mockup.png", "/path/to/current-dashboard.png"],
  "files": ["/path/to/user-requirements.md"],
  "thinking_mode": "medium"
}
```

### Exemple 3 : Résolution de problème technique

```json
{
  "prompt": "Je rencontre un problème de performance avec notre API GraphQL. Les requêtes complexes avec des relations imbriquées prennent plus de 5 secondes à s'exécuter. J'ai déjà implémenté DataLoader pour éviter le problème N+1, ajouté des index sur les clés étrangères, et mis en place un cache Redis. Malgré cela, certaines requêtes restent lentes. J'ai analysé les logs et il semble que le problème vient des jointures complexes au niveau de la base de données. Quelles autres stratégies d'optimisation pourrais-je explorer ? Devrais-je considérer la dénormalisation ou y a-t-il d'autres approches ?",
  "files": ["/path/to/schema.graphql", "/path/to/slow-queries.sql", "/path/to/performance-logs.txt"],
  "use_websearch": true,
  "thinking_mode": "high"
}
```

## Sense Check (Vérification du sens)

Avant de finaliser votre utilisation de l'outil `chat` :

### ✅ Vérifications essentielles :

1. **Contexte suffisant** : Avez-vous fourni assez de contexte pour obtenir une réponse utile ?
2. **Question claire** : Votre prompt exprime-t-il clairement ce que vous cherchez ?
3. **Fichiers pertinents** : Les fichiers inclus sont-ils directement liés à votre question ?
4. **Chemins absolus** : Tous les chemins de fichiers et images sont-ils complets et absolus ?
5. **Mode de réflexion approprié** : Le niveau de thinking_mode correspond-il à la complexité de votre question ?
6. **Recherche web justifiée** : La recherche web est-elle nécessaire pour votre cas d'usage ?
7. **Continuation appropriée** : Utilisez-vous continuation_id seulement pour continuer une conversation existante ?

### ⚠️ Signaux d'alarme :

- Prompt trop vague ou sans contexte suffisant
- Chemins de fichiers raccourcis ou incomplets
- Utilisation de continuation_id pour une nouvelle conversation
- Mode de réflexion inadapté (trop élevé pour une question simple, trop bas pour un problème complexe)
- Images non pertinentes pour le contexte de la discussion

### 🎯 Objectif final :

L'outil `chat` doit vous permettre de :
- **Obtenir des insights précieux** grâce à un contexte riche et détaillé
- **Explorer des alternatives** et valider vos approches
- **Résoudre des problèmes complexes** avec l'aide d'un partenaire de réflexion
- **Apprendre et comprendre** des concepts techniques avancés
- **Maintenir des conversations fluides** sur plusieurs échanges

### 📋 Checklist post-utilisation :

- [ ] La réponse répond-elle à ma question principale ?
- [ ] Ai-je obtenu des insights nouveaux ou des perspectives différentes ?
- [ ] Les suggestions sont-elles applicables à mon contexte spécifique ?
- [ ] Ai-je besoin de poser des questions de suivi pour clarifier certains points ?
- [ ] La conversation m'a-t-elle aidé à avancer dans ma réflexion ?

L'outil `chat` est votre partenaire de réflexion idéal pour toutes vos questions de développement, du brainstorming initial à la résolution de problèmes techniques complexes.