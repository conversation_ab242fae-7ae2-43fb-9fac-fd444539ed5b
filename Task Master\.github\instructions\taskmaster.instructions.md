---
description: <PERSON><PERSON><PERSON><PERSON><PERSON>ce complète des outils MCP Taskmaster et des commandes CLI.
applyTo: "**/*"
---

# Référence des outils et commandes Taskmaster

Ce document fournit une référence détaillée pour interagir avec Taskmaster, couvrant à la fois les outils MCP recommandés, adaptés aux intégrations comme VS Code, et les commandes CLI `task-master`, conçues pour une interaction directe ou en secours.

**Note :** Pour une utilisation programmatique ou via des outils intégrés, il est fortement recommandé d'utiliser les **outils MCP** pour de meilleures performances, des échanges structurés et une gestion avancée des erreurs. Les commandes CLI servent d'alternative conviviale et de solution de repli.

**Important :** Plusieurs outils MCP font appel à l'IA... Les outils utilisant l'IA incluent `parse_prd`, `analyze_project_complexity`, `update_subtask`, `update_task`, `update`, `expand_all`, `expand_task` et `add_task`.

**🏷️ Système de listes de tâches taguées :** Taskmaster prend désormais en charge les **listes de tâches taguées** pour la gestion multi-contexte. Cela vous permet de maintenir des listes de tâches séparées et isolées pour différentes fonctionnalités, branches ou expérimentations. Les projets existants sont migrés de façon transparente vers un tag "master" par défaut. La plupart des commandes supportent désormais le flag `--tag <nom>` pour spécifier le contexte. Si omis, la commande utilise le tag actif.

---

## Initialisation et configuration

### 1. Initialiser le projet (`init`)

*   **Outil MCP :** `initialize_project`
*   **Commande CLI :** `task-master init [options]`
*   **Description :** `Configurer la structure de fichiers et la configuration de base de Taskmaster dans le répertoire actuel pour un nouveau projet.`
*   **Options CLI clés :**
    *   `--name <nom>` : `Définir le nom de votre projet dans la configuration de Taskmaster.`
    *   `--description <texte>` : `Fournir une brève description de votre projet.`
    *   `--version <version>` : `Définir la version initiale de votre projet, par ex. '0.1.0'.`
    *   `-y, --yes` : `Initialiser Taskmaster rapidement en utilisant les paramètres par défaut sans invites interactives.`
*   **Utilisation :** Exécutez ceci une fois au début d'un nouveau projet.
*   **Description de la variante MCP :** `Configurer la structure de fichiers et la configuration de base de Taskmaster dans le répertoire actuel pour un nouveau projet en exécutant la commande 'task-master init'.`
*   **Paramètres/Options clés MCP :**
    *   `projectName` : `Définir le nom de votre projet.` (CLI : `--name <nom>`)
    *   `projectDescription` : `Fournir une brève description de votre projet.` (CLI : `--description <texte>`)
    *   `projectVersion` : `Définir la version initiale de votre projet, par ex. '0.1.0'.` (CLI : `--version <version>`)
    *   `authorName` : `Nom de l'auteur.` (CLI : `--author <auteur>`)
    *   `skipInstall` : `Ignorer l'installation des dépendances. La valeur par défaut est false.` (CLI : `--skip-install`)
    *   `addAliases` : `Ajouter des alias shell tm et taskmaster. La valeur par défaut est false.` (CLI : `--aliases`)
    *   `yes` : `Ignorer les invites et utiliser les valeurs par défaut/arguments fournis. La valeur par défaut est false.` (CLI : `-y, --yes`)
*   **Utilisation :** Exécutez ceci une fois au début d'un nouveau projet, généralement via un outil intégré comme VS Code. Fonctionne dans le répertoire de travail actuel du serveur MCP.
*   **Important :** Une fois terminé, vous devez analyser un prd afin de générer des tâches. Il n'y aura pas de fichiers de tâches d'ici là. L'étape suivante après l'initialisation doit consister à créer un PRD en utilisant le PRD exemple dans .taskmaster/templates/example_prd.txt.
*   **Tagging :** Utilisez l'option `--tag` pour analyser le PRD dans un contexte de tag spécifique, non par défaut. Si le tag n'existe pas, il sera créé automatiquement. Exemple : `task-master parse-prd spec.txt --tag=new-feature`.

### 2. Analyser le PRD (`parse_prd`)

*   **Outil MCP :** `parse_prd`
*   **Commande CLI :** `task-master parse-prd [fichier] [options]`
*   **Description :** `Analyser un document de spécifications produit, PRD, ou un fichier texte avec Taskmaster pour générer automatiquement un ensemble initial de tâches dans tasks.json.`
*   **Paramètres/Options clés :**
    *   `input` : `Chemin vers votre PRD ou fichier texte de spécifications que Taskmaster doit analyser pour les tâches.` (CLI : `[fichier]` positionnel ou `-i, --input <fichier>`)
    *   `output` : `Spécifiez où Taskmaster doit enregistrer le fichier 'tasks.json' généré. Par défaut, c'est '.taskmaster/tasks/tasks.json'.` (CLI : `-o, --output <fichier>`)
    *   `numTasks` : `Nombre approximatif de tâches principales que Taskmaster doit viser à générer à partir du document.` (CLI : `-n, --num-tasks <nombre>`)
    *   `force` : `Utilisez ceci pour permettre à Taskmaster d'écraser un 'tasks.json' existant sans demander de confirmation.` (CLI : `-f, --force`)
*   **Utilisation :** Utile pour démarrer un projet à partir d'un document de spécifications existant.
*   **Remarques :** Task Master respectera strictement toutes les exigences spécifiques mentionnées dans le PRD, telles que les bibliothèques, les schémas de base de données, les frameworks, les piles technologiques, etc., tout en comblant les lacunes lorsque le PRD n'est pas entièrement spécifié. Les tâches sont conçues pour fournir le chemin d'implémentation le plus direct tout en évitant le sur-développement.
*   **Important :** Cet outil MCP effectue des appels IA et peut prendre jusqu'à une minute pour se compléter. Veuillez informer les utilisateurs de patienter pendant que l'opération est en cours. Si l'utilisateur n'a pas de PRD, suggérez de discuter de son idée puis d'utiliser le PRD exemple dans `.taskmaster/templates/example_prd.txt` comme modèle pour créer le PRD basé sur son idée, pour utilisation avec `parse-prd`.

---

## Configuration du modèle IA

### 2. Gérer les modèles (`models`)
*   **Outil MCP :** `models`
*   **Commande CLI :** `task-master models [options]`
*   **Description :** `Afficher la configuration actuelle du modèle IA ou définir des modèles spécifiques pour différents rôles (principal, recherche, secours). Permet de définir des ID de modèle personnalisés pour Ollama et OpenRouter.`
*   **Paramètres/Options clés MCP :**
    *   `setMain <model_id>` : `Définir l'ID du modèle principal pour la génération/mise à jour des tâches.` (CLI : `--set-main <model_id>`)
    *   `setResearch <model_id>` : `Définir l'ID du modèle pour les opérations basées sur la recherche.` (CLI : `--set-research <model_id>`)
    *   `setFallback <model_id>` : `Définir l'ID du modèle à utiliser si le principal échoue.` (CLI : `--set-fallback <model_id>`)
    *   `ollama <boolean>` : `Indique que l'ID du modèle défini est un modèle Ollama personnalisé.` (CLI : `--ollama`)
    *   `openrouter <boolean>` : `Indique que l'ID du modèle défini est un modèle OpenRouter personnalisé.` (CLI : `--openrouter`)
    *   `listAvailableModels <boolean>` : `Si vrai, liste les modèles disponibles non actuellement assignés à un rôle.` (CLI : Pas d'équivalent direct ; CLI liste disponibles automatiquement)
    *   `projectRoot <string>` : `Facultatif. Chemin absolu vers le répertoire racine du projet.` (CLI : Déterminé automatiquement)
*   **Options clés CLI :**
    *   `--set-main <model_id>` : `Définir le modèle principal.`
    *   `--set-research <model_id>` : `Définir le modèle de recherche.`
    *   `--set-fallback <model_id>` : `Définir le modèle de secours.`
    *   `--ollama` : `Spécifier que l'ID du modèle fourni est pour Ollama (à utiliser avec --set-*).`
    *   `--openrouter` : `Spécifier que l'ID du modèle fourni est pour OpenRouter (à utiliser avec --set-*). Valide contre l'API OpenRouter.`
    *   `--bedrock` : `Spécifier que l'ID du modèle fourni est pour AWS Bedrock (à utiliser avec --set-*).`
    *   `--setup` : `Exécuter la configuration interactive pour configurer les modèles, y compris les ID personnalisés Ollama/OpenRouter.`
*   **Utilisation (MCP) :** Appelez sans les drapeaux de définition pour obtenir la configuration actuelle. Utilisez `setMain`, `setResearch` ou `setFallback` avec un ID de modèle valide pour mettre à jour la configuration. Utilisez `listAvailableModels: true` pour obtenir une liste des modèles non assignés. Pour définir un modèle personnalisé, fournissez l'ID du modèle et définissez `ollama: true` ou `openrouter: true`.
*   **Utilisation (CLI) :** Exécutez sans drapeaux pour afficher la configuration actuelle et les modèles disponibles. Utilisez les drapeaux de définition pour mettre à jour des rôles spécifiques. Utilisez `--setup` pour une configuration guidée, y compris des modèles personnalisés. Pour définir un modèle personnalisé via des drapeaux, utilisez `--set-<role>=<model_id>` avec soit `--ollama` soit `--openrouter`.
*   **Remarques :** La configuration est stockée dans `.taskmaster/config.json` dans la racine du projet. Cette commande/outil modifie ce fichier. Utilisez `listAvailableModels` ou `task-master models` pour voir les modèles pris en charge en interne. Les modèles personnalisés OpenRouter sont validés par rapport à leur API en direct. Les modèles personnalisés Ollama ne sont pas validés en direct.
*   **Remarque API :** Les clés API pour les fournisseurs IA sélectionnés (en fonction de leur modèle) doivent exister dans le fichier mcp.json pour être accessibles dans le contexte MCP. Les clés API doivent être présentes dans le fichier .env local pour que la CLI puisse les lire.
*   **Coûts des modèles :** Les coûts des modèles pris en charge sont exprimés en dollars. Une valeur d'entrée/sortie de 3 est de 3,00 $. Une valeur de 0,8 est de 0,80 $.
*   **Avertissement :** NE MODIFIEZ PAS MANUELLEMENT LE FICHIER .taskmaster/config.json. Utilisez les commandes incluses soit dans le format MCP soit dans le format CLI selon les besoins. Priorisez toujours les outils MCP lorsque cela est possible et utilisez la CLI comme solution de repli.

---

## Liste et affichage des tâches

### 3. Obtenir les tâches (`get_tasks`)

*   **Outil MCP :** `get_tasks`
*   **Commande CLI :** `task-master list [options]`
*   **Description :** `Lister vos tâches Taskmaster, en filtrant éventuellement par statut et en affichant les sous-tâches.`
*   **Paramètres/Options clés :**
    *   `status` : `Afficher uniquement les tâches Taskmaster correspondant à ce statut (ou plusieurs statuts, séparés par des virgules), par ex. 'pending' ou 'done,in-progress'.` (CLI : `-s, --status <status>`)
    *   `withSubtasks` : `Inclure les sous-tâches indentées sous leurs tâches parentes dans la liste.` (CLI : `--with-subtasks`)
    *   `tag` : `Spécifiez de quel contexte de tag lister les tâches. Par défaut, c'est le tag actif actuel.` (CLI : `--tag <nom>`)
    *   `file` : `Chemin vers votre fichier Taskmaster 'tasks.json'. Par défaut, repose sur la détection automatique.` (CLI : `-f, --file <fichier>`)
*   **Utilisation :** Obtenez un aperçu de l'état du projet, souvent utilisé au début d'une session de travail.

### 4. Obtenir la prochaine tâche (`next_task`)

*   **Outil MCP :** `next_task`
*   **Commande CLI :** `task-master next [options]`
*   **Description :** `Demander à Taskmaster de montrer la prochaine tâche disponible sur laquelle vous pouvez travailler, en fonction du statut et des dépendances complètes.`
*   **Paramètres/Options clés :**
    *   `file` : `Chemin vers votre fichier Taskmaster 'tasks.json'. Par défaut, repose sur la détection automatique.` (CLI : `-f, --file <fichier>`)
    *   `tag` : `Spécifiez quel contexte de tag utiliser. Par défaut, c'est le tag actif actuel.` (CLI : `--tag <nom>`)
*   **Utilisation :** Identifiez sur quoi travailler ensuite selon le plan.

### 5. Obtenir les détails de la tâche (`get_task`)

*   **Outil MCP :** `get_task`
*   **Commande CLI :** `task-master show [id] [options]`
*   **Description :** `Afficher les informations détaillées pour une ou plusieurs tâches ou sous-tâches Taskmaster spécifiques par ID.`
*   **Paramètres/Options clés :**
    *   `id` : `Requis. L'ID de la tâche Taskmaster (par ex. '15'), de la sous-tâche (par ex. '15.2'), ou une liste d'IDs séparés par des virgules ('1,5,10.2') que vous souhaitez afficher.` (CLI : `[id]` positionnel ou `-i, --id <id>`)
    *   `tag` : `Spécifiez de quel contexte de tag obtenir la ou les tâches. Par défaut, c'est le tag actif actuel.` (CLI : `--tag <nom>`)
    *   `file` : `Chemin vers votre fichier Taskmaster 'tasks.json'. Par défaut, repose sur la détection automatique.` (CLI : `-f, --file <fichier>`)
*   **Utilisation :** Comprendre tous les détails d'une tâche spécifique. Lorsque plusieurs IDs sont fournies, un tableau récapitulatif est affiché.
*   **INFORMATION CRITIQUE** Si vous devez collecter des informations à partir de plusieurs tâches, utilisez des IDs séparés par des virgules (c'est-à-dire 1,2,3) pour recevoir un tableau de tâches. Ne demandez pas inutilement des tâches une à une si vous devez en obtenir beaucoup, car cela serait inefficace.

---

## Création et modification de tâches

### 6. Ajouter une tâche (`add_task`)

*   **Outil MCP :** `add_task`
*   **Commande CLI :** `task-master add-task [options]`
*   **Description :** `Ajouter une nouvelle tâche à Taskmaster en la décrivant ; l'IA s'en chargera.`
*   **Paramètres/Options clés :**
    *   `prompt` : `Requis. Décrivez la nouvelle tâche que vous souhaitez que Taskmaster crée, par ex. "Implémenter l'authentification utilisateur avec JWT".` (CLI : `-p, --prompt <texte>`)
    *   `dependencies` : `Spécifiez les IDs de toutes les tâches Taskmaster qui doivent être complétées avant que celle-ci ne puisse commencer, par ex. '12,14'.` (CLI : `-d, --dependencies <ids>`)
    *   `priority` : `Définir la priorité de la nouvelle tâche : 'haute', 'moyenne' ou 'basse'. La valeur par défaut est 'moyenne'.` (CLI : `--priority <priorité>`)
    *   `research` : `Activer l'utilisation du rôle recherche par Taskmaster pour une création de tâche potentiellement plus informée.` (CLI : `-r, --research`)
    *   `tag` : `Spécifiez à quel contexte de tag ajouter la tâche. Par défaut, c'est le tag actif actuel.` (CLI : `--tag <nom>`)
    *   `file` : `Chemin vers votre fichier Taskmaster 'tasks.json'. Par défaut, repose sur la détection automatique.` (CLI : `-f, --file <fichier>`)
*   **Utilisation :** Ajoutez rapidement des tâches nouvellement identifiées pendant le développement.
*   **Important :** Cet outil MCP effectue des appels IA et peut prendre jusqu'à une minute pour se compléter. Veuillez informer les utilisateurs de patienter pendant que l'opération est en cours.

### 7. Ajouter une sous-tâche (`add_subtask`)

*   **Outil MCP :** `add_subtask`
*   **Commande CLI :** `task-master add-subtask [options]`
*   **Description :** `Ajouter une nouvelle sous-tâche à une tâche parente Taskmaster, ou convertir une tâche existante en sous-tâche.`
*   **Paramètres/Options clés :**
    *   `id` / `parent` : `Requis. L'ID de la tâche parente Taskmaster.` (MCP : `id`, CLI : `-p, --parent <id>`)
    *   `taskId` : `Utilisez ceci si vous souhaitez convertir une tâche Taskmaster de niveau supérieur existante en sous-tâche de la parente spécifiée.` (CLI : `-i, --task-id <id>`)
    *   `title` : `Requis si taskId n'est pas utilisé. Le titre de la nouvelle sous-tâche que Taskmaster doit créer.` (CLI : `-t, --title <titre>`)
    *   `description` : `Une brève description pour la nouvelle sous-tâche.` (CLI : `-d, --description <texte>`)
    *   `details` : `Fournir des notes ou des détails d'implémentation pour la nouvelle sous-tâche.` (CLI : `--details <texte>`)
    *   `dependencies` : `Spécifiez les IDs d'autres tâches ou sous-tâches, par ex. '15' ou '16.1', qui doivent être effectuées avant que cette nouvelle sous-tâche ne puisse être commencée.` (CLI : `--dependencies <ids>`)
    *   `status` : `Définir le statut initial de la nouvelle sous-tâche. La valeur par défaut est 'pending'.` (CLI : `-s, --status <status>`)
    *   `generate` : `Activer Taskmaster pour régénérer les fichiers de tâches markdown après l'ajout de la sous-tâche.` (CLI : `--generate`)
    *   `tag` : `Spécifiez sur quel contexte de tag opérer. Par défaut, c'est le tag actif actuel.` (CLI : `--tag <nom>`)
    *   `file` : `Chemin vers votre fichier Taskmaster 'tasks.json'. Par défaut, repose sur la détection automatique.` (CLI : `-f, --file <fichier>`)
*   **Utilisation :** Décomposer manuellement des tâches ou réorganiser des tâches existantes.

### 8. Mettre à jour les tâches (`update`)

*   **Outil MCP :** `update`
*   **Commande CLI :** `task-master update [options]`
*   **Description :** `Mettre à jour plusieurs tâches à venir dans Taskmaster en fonction d'un nouveau contexte ou de changements, à partir d'un ID de tâche spécifique.`
*   **Paramètres/Options clés :**
    *   `from` : `Requis. L'ID de la première tâche que Taskmaster doit mettre à jour. Toutes les tâches avec cet ID ou un ID supérieur qui ne sont pas 'done' seront prises en compte.` (CLI : `--from <id>`)
    *   `prompt` : `Requis. Expliquer le changement ou le nouveau contexte que Taskmaster doit appliquer aux tâches, par ex. "Nous utilisons maintenant React Query au lieu de Redux Toolkit pour la récupération de données".` (CLI : `-p, --prompt <texte>`)
    *   `research` : `Activer l'utilisation du rôle recherche par Taskmaster pour des mises à jour plus informées. Nécessite une clé API appropriée.` (CLI : `-r, --research`)
    *   `tag` : `Spécifiez quel contexte de tag opérer. Par défaut, c'est le tag actif actuel.` (CLI : `--tag <nom>`)
    *   `file` : `Chemin vers votre fichier Taskmaster 'tasks.json'. Par défaut, repose sur la détection automatique.` (CLI : `-f, --file <fichier>`)
*   **Utilisation :** Gérer des changements d'implémentation significatifs ou des pivots qui affectent plusieurs tâches futures. Exemple CLI : `task-master update --from='18' --prompt='Changement vers React Query.\nBesoin de refactoriser la récupération de données...'`
*   **Important :** Cet outil MCP effectue des appels IA et peut prendre jusqu'à une minute pour se compléter. Veuillez informer les utilisateurs de patienter pendant que l'opération est en cours.

### 9. Mettre à jour une tâche (`update_task`)

*   **Outil MCP :** `update_task`
*   **Commande CLI :** `task-master update-task [options]`
*   **Description :** `Modifier une tâche Taskmaster spécifique par ID, en incorporant de nouvelles informations ou des changements. Par défaut, cela remplace les détails de la tâche existante.`
*   **Paramètres/Options clés :**
    *   `id` : `Requis. L'ID spécifique de la tâche Taskmaster, par ex. '15', que vous souhaitez mettre à jour.` (CLI : `-i, --id <id>`)
    *   `prompt` : `Requis. Expliquer les changements spécifiques ou fournir les nouvelles informations que Taskmaster doit incorporer dans cette tâche.` (CLI : `-p, --prompt <texte>`)
    *   `append` : `Si vrai, ajoute le contenu de l'invite aux détails de la tâche avec un horodatage, plutôt que de les remplacer. Comporte comme update-subtask.` (CLI : `--append`)
    *   `research` : `Activer l'utilisation du rôle recherche par Taskmaster pour des mises à jour plus informées. Nécessite une clé API appropriée.` (CLI : `-r, --research`)
    *   `tag` : `Spécifiez à quel contexte de tag appartient la tâche. Par défaut, c'est le tag actif actuel.` (CLI : `--tag <nom>`)
    *   `file` : `Chemin vers votre fichier Taskmaster 'tasks.json'. Par défaut, repose sur la détection automatique.` (CLI : `-f, --file <fichier>`)
*   **Utilisation :** Affiner une tâche spécifique en fonction de nouvelles compréhensions. Utilisez `--append` pour enregistrer les progrès sans créer de sous-tâches.
*   **Important :** Cet outil MCP effectue des appels IA et peut prendre jusqu'à une minute pour se compléter. Veuillez informer les utilisateurs de patienter pendant que l'opération est en cours.

### 10. Mettre à jour une sous-tâche (`update_subtask`)

*   **Outil MCP :** `update_subtask`
*   **Commande CLI :** `task-master update-subtask [options]`
*   **Description :** `Ajouter des notes ou des détails horodatés à une sous-tâche Taskmaster spécifique sans écraser le contenu existant. Destiné à l'enregistrement itératif de l'implémentation.`
*   **Paramètres/Options clés :**
    *   `id` : `Requis. L'ID de la sous-tâche Taskmaster, par ex. '5.2', à mettre à jour avec de nouvelles informations.` (CLI : `-i, --id <id>`)
    *   `prompt` : `Requis. Les informations, conclusions ou notes de progrès à ajouter aux détails de la sous-tâche avec un horodatage.` (CLI : `-p, --prompt <texte>`)
    *   `research` : `Activer l'utilisation du rôle recherche par Taskmaster pour des mises à jour plus informées. Nécessite une clé API appropriée.` (CLI : `-r, --research`)
    *   `tag` : `Spécifiez à quel contexte de tag appartient la sous-tâche. Par défaut, c'est le tag actif actuel.` (CLI : `--tag <nom>`)
    *   `file` : `Chemin vers votre fichier Taskmaster 'tasks.json'. Par défaut, repose sur la détection automatique.` (CLI : `-f, --file <fichier>`)
*   **Utilisation :** Enregistrer les progrès de l'implémentation, les conclusions et les découvertes pendant le développement de la sous-tâche. Chaque mise à jour est horodatée et ajoutée pour préserver le parcours de l'implémentation.
*   **Important :** Cet outil MCP effectue des appels IA et peut prendre jusqu'à une minute pour se compléter. Veuillez informer les utilisateurs de patienter pendant que l'opération est en cours.

### 11. Définir le statut de la tâche (`set_task_status`)

*   **Outil MCP :** `set_task_status`
*   **Commande CLI :** `task-master set-status [options]`
*   **Description :** `Mettre à jour le statut d'une ou plusieurs tâches ou sous-tâches Taskmaster, par ex. 'pending', 'in-progress', 'done'.`
*   **Paramètres/Options clés :**
    *   `id` : `Requis. L'ID(s) de la ou des tâches Taskmaster ou sous-tâche(s), par ex. '15', '15.2', ou '16,17.1', à mettre à jour.` (CLI : `-i, --id <id>`)
    *   `status` : `Requis. Le nouveau statut à définir, par ex. 'done', 'pending', 'in-progress', 'review', 'cancelled'.` (CLI : `-s, --status <status>`)
    *   `tag` : `Spécifiez quel contexte de tag opérer. Par défaut, c'est le tag actif actuel.` (CLI : `--tag <nom>`)
    *   `file` : `Chemin vers votre fichier Taskmaster 'tasks.json'. Par défaut, repose sur la détection automatique.` (CLI : `-f, --file <fichier>`)
*   **Utilisation :** Marquer les progrès à mesure que les tâches avancent dans le cycle de développement.

### 12. Supprimer une tâche (`remove_task`)

*   **Outil MCP :** `remove_task`
*   **Commande CLI :** `task-master remove-task [options]`
*   **Description :** `Supprimer définitivement une tâche ou une sous-tâche de la liste des tâches Taskmaster.`
*   **Paramètres/Options clés :**
    *   `id` : `Requis. L'ID de la tâche Taskmaster, par ex. '5', ou de la sous-tâche, par ex. '5.2', à supprimer définitivement.` (CLI : `-i, --id <id>`)
    *   `yes` : `Ignorer l'invite de confirmation et supprimer immédiatement la tâche.` (CLI : `-y, --yes`)
    *   `tag` : `Spécifiez quel contexte de tag opérer. Par défaut, c'est le tag actif actuel.` (CLI : `--tag <nom>`)
    *   `file` : `Chemin vers votre fichier Taskmaster 'tasks.json'. Par défaut, repose sur la détection automatique.` (CLI : `-f, --file <fichier>`)
*   **Utilisation :** Supprimer définitivement des tâches ou sous-tâches qui ne sont plus nécessaires dans le projet.
*   **Remarques :** À utiliser avec précaution car cette opération ne peut pas être annulée. Envisagez d'utiliser les statuts 'bloqué', 'annulé' ou 'différé' si vous souhaitez simplement exclure une tâche de la planification active mais la conserver pour référence. La commande nettoie automatiquement les références de dépendance dans d'autres tâches.

---

## Structure et décomposition des tâches

### 13. Développer une tâche (`expand_task`)

*   **Outil MCP :** `expand_task`
*   **Commande CLI :** `task-master expand [options]`
*   **Description :** `Utiliser l'IA de Taskmaster pour décomposer une tâche complexe en sous-tâches plus petites et gérables. Ajoute des sous-tâches par défaut.`
*   **Paramètres/Options clés :**
    *   `id` : `L'ID de la tâche Taskmaster spécifique que vous souhaitez décomposer en sous-tâches.` (CLI : `-i, --id <id>`)
    *   `num` : `Facultatif : Suggère combien de sous-tâches Taskmaster doit viser à créer. Utilise l'analyse de complexité/par défaut sinon.` (CLI : `-n, --num <nombre>`)
    *   `research` : `Activer l'utilisation du rôle recherche par Taskmaster pour une génération de sous-tâches plus informée. Nécessite une clé API appropriée.` (CLI : `-r, --research`)
    *   `prompt` : `Facultatif : Fournir un contexte ou des instructions supplémentaires à Taskmaster pour générer les sous-tâches.` (CLI : `-p, --prompt <texte>`)
    *   `force` : `Facultatif : Si vrai, efface les sous-tâches existantes avant de générer de nouvelles. La valeur par défaut est false (ajout).` (CLI : `--force`)
    *   `tag` : `Spécifiez à quel contexte de tag appartient la tâche. Par défaut, c'est le tag actif actuel.` (CLI : `--tag <nom>`)
    *   `file` : `Chemin vers votre fichier Taskmaster 'tasks.json'. Par défaut, repose sur la détection automatique.` (CLI : `-f, --file <fichier>`)
*   **Utilisation :** Générer un plan d'implémentation détaillé pour une tâche complexe avant de commencer le codage. Utilise automatiquement les recommandations du rapport de complexité si disponibles et si `num` n'est pas spécifié.
*   **Important :** Cet outil MCP effectue des appels IA et peut prendre jusqu'à une minute pour se compléter. Veuillez informer les utilisateurs de patienter pendant que l'opération est en cours.

### 14. Développer toutes les tâches (`expand_all`)

*   **Outil MCP :** `expand_all`
*   **Commande CLI :** `task-master expand --all [options]` (Remarque : la CLI utilise la commande `expand` avec le drapeau `--all`)
*   **Description :** `Demander à Taskmaster d'élargir automatiquement toutes les tâches éligibles en attente/en cours en fonction de l'analyse de complexité ou des valeurs par défaut. Ajoute des sous-tâches par défaut.`
*   **Paramètres/Options clés :**
    *   `num` : `Facultatif : Suggère combien de sous-tâches Taskmaster doit viser à créer par tâche.` (CLI : `-n, --num <nombre>`)
    *   `research` : `Activer le rôle de recherche pour une génération de sous-tâches plus informée. Nécessite une clé API appropriée.` (CLI : `-r, --research`)
    *   `prompt` : `Facultatif : Fournir un contexte supplémentaire à Taskmaster à appliquer généralement lors de l'expansion.` (CLI : `-p, --prompt <texte>`)
    *   `force` : `Facultatif : Si vrai, efface les sous-tâches existantes avant de générer de nouvelles pour chaque tâche éligible. La valeur par défaut est false (ajout).` (CLI : `--force`)
    *   `tag` : `Spécifiez quel contexte de tag étendre. Par défaut, c'est le tag actif actuel.` (CLI : `--tag <nom>`)
    *   `file` : `Chemin vers votre fichier Taskmaster 'tasks.json'. Par défaut, repose sur la détection automatique.` (CLI : `-f, --file <fichier>`)
*   **Utilisation :** Utile après la génération initiale des tâches ou l'analyse de complexité pour décomposer plusieurs tâches à la fois.
*   **Important :** Cet outil MCP effectue des appels IA et peut prendre jusqu'à une minute pour se compléter. Veuillez informer les utilisateurs de patienter pendant que l'opération est en cours.

### 15. Effacer les sous-tâches (`clear_subtasks`)

*   **Outil MCP :** `clear_subtasks`
*   **Commande CLI :** `task-master clear-subtasks [options]`
*   **Description :** `Supprimer toutes les sous-tâches d'une ou plusieurs tâches parentes Taskmaster spécifiées.`
*   **Paramètres/Options clés :**
    *   `id` : `L'ID(s) de la ou des tâches parentes Taskmaster dont vous souhaitez supprimer les sous-tâches, par ex. '15' ou '16,18'. Requis sauf si vous utilisez 'all'.` (CLI : `-i, --id <ids>`)
    *   `all` : `Indiquez à Taskmaster de supprimer les sous-tâches de toutes les tâches parentes.` (CLI : `--all`)
    *   `tag` : `Spécifiez quel contexte de tag opérer. Par défaut, c'est le tag actif actuel.` (CLI : `--tag <nom>`)
    *   `file` : `Chemin vers votre fichier Taskmaster 'tasks.json'. Par défaut, repose sur la détection automatique.` (CLI : `-f, --file <fichier>`)
*   **Utilisation :** Utilisé avant de régénérer des sous-tâches avec `expand_task` si la décomposition précédente doit être remplacée.

### 16. Supprimer une sous-tâche (`remove_subtask`)

*   **Outil MCP :** `remove_subtask`
*   **Commande CLI :** `task-master remove-subtask [options]`
*   **Description :** `Supprimer une sous-tâche de sa tâche parente Taskmaster, en la convertissant éventuellement en tâche autonome.`
*   **Paramètres/Options clés :**
    *   `id` : `Requis. L'ID(s) de la ou des sous-tâches Taskmaster à supprimer, par ex. '15.2' ou '16.1,16.3'.` (CLI : `-i, --id <id>`)
    *   `convert` : `Si utilisé, Taskmaster transformera la sous-tâche en une tâche régulière de niveau supérieur au lieu de la supprimer.` (CLI : `-c, --convert`)
    *   `generate` : `Activer Taskmaster pour régénérer les fichiers de tâches markdown après la suppression de la sous-tâche.` (CLI : `--generate`)
    *   `tag` : `Spécifiez quel contexte de tag opérer. Par défaut, c'est le tag actif actuel.` (CLI : `--tag <nom>`)
    *   `file` : `Chemin vers votre fichier Taskmaster 'tasks.json'. Par défaut, repose sur la détection automatique.` (CLI : `-f, --file <fichier>`)
*   **Utilisation :** Supprimer des sous-tâches inutiles ou promouvoir une sous-tâche en tâche de niveau supérieur.

### 17. Déplacer une tâche (`move_task`)

*   **Outil MCP :** `move_task`
*   **Commande CLI :** `task-master move [options]`
*   **Description :** `Déplacer une tâche ou une sous-tâche vers une nouvelle position dans la hiérarchie des tâches.`
*   **Paramètres/Options clés :**
    *   `from` : `Requis. ID de la tâche/sous-tâche à déplacer (par ex. "5" ou "5.2"). Peut être séparé par des virgules pour plusieurs tâches.` (CLI : `--from <id>`)
    *   `to` : `Requis. ID de la destination (par ex. "7" ou "7.3"). Doit correspondre au nombre d'IDs sources s'ils sont séparés par des virgules.` (CLI : `--to <id>`)
    *   `tag` : `Spécifiez quel contexte de tag opérer. Par défaut, c'est le tag actif actuel.` (CLI : `--tag <nom>`)
    *   `file` : `Chemin vers votre fichier Taskmaster 'tasks.json'. Par défaut, repose sur la détection automatique.` (CLI : `-f, --file <fichier>`)
*   **Utilisation :** Réorganiser les tâches en les déplaçant dans la hiérarchie. Prend en charge divers scénarios tels que :
    *   Déplacer une tâche pour en faire une sous-tâche
    *   Déplacer une sous-tâche pour en faire une tâche autonome
    *   Déplacer une sous-tâche vers un parent différent
    *   Réordonner les sous-tâches au sein du même parent
    *   Déplacer une tâche vers un nouvel ID inexistant (crée automatiquement des espaces réservés)
    *   Déplacer plusieurs tâches à la fois avec des IDs séparés par des virgules
*   **Fonctionnalités de validation :**
    *   Permet de déplacer des tâches vers des IDs de destination inexistants (crée des tâches espaces réservés)
    *   Empêche le déplacement vers des IDs de tâche existants qui ont déjà du contenu (pour éviter d'écraser)
    *   Valide que les tâches sources existent avant d'essayer de les déplacer
    *   Maintient les relations appropriées entre parents et enfants
*   **Exemple CLI :** `task-master move --from=5.2 --to=7.3` pour déplacer la sous-tâche 5.2 pour en faire la sous-tâche 7.3.
*   **Exemple de déplacement multiple :** `task-master move --from=10,11,12 --to=16,17,18` pour déplacer plusieurs tâches vers de nouvelles positions.
*   **Utilisation courante :** Résoudre les conflits de fusion dans tasks.json lorsque plusieurs membres de l'équipe créent des tâches sur des branches différentes.

---

## Gestion des dépendances

### 18. Ajouter une dépendance (`add_dependency`)

*   **Outil MCP :** `add_dependency`
*   **Commande CLI :** `task-master add-dependency [options]`
*   **Description :** `Définir une dépendance dans Taskmaster, rendant une tâche préalable à une autre.`
*   **Paramètres/Options clés :**
    *   `id` : `Requis. L'ID de la tâche Taskmaster qui dépend d'une autre.` (CLI : `-i, --id <id>`)
    *   `dependsOn` : `Requis. L'ID de la tâche Taskmaster qui doit être complétée en premier, la préalable.` (CLI : `-d, --depends-on <id>`)
    *   `tag` : `Spécifiez quel contexte de tag opérer. Par défaut, c'est le tag actif actuel.` (CLI : `--tag <nom>`)
    *   `file` : `Chemin vers votre fichier Taskmaster 'tasks.json'. Par défaut, repose sur la détection automatique.` (CLI : `-f, --file <path>`)
*   **Utilisation :** Établir l'ordre d'exécution correct entre les tâches.

### 19. Supprimer une dépendance (`remove_dependency`)

*   **Outil MCP :** `remove_dependency`
*   **Commande CLI :** `task-master remove-dependency [options]`
*   **Description :** `Supprimer une relation de dépendance entre deux tâches Taskmaster.`
*   **Paramètres/Options clés :**
    *   `id` : `Requis. L'ID de la tâche Taskmaster dont vous souhaitez supprimer une préalable.` (CLI : `-i, --id <id>`)
    *   `dependsOn` : `Requis. L'ID de la tâche Taskmaster qui ne doit plus être une préalable.` (CLI : `-d, --depends-on <id>`)
    *   `tag` : `Spécifiez quel contexte de tag opérer. Par défaut, c'est le tag actif actuel.` (CLI : `--tag <nom>`)
    *   `file` : `Chemin vers votre fichier Taskmaster 'tasks.json'. Par défaut, repose sur la détection automatique.` (CLI : `-f, --file <fichier>`)
*   **Utilisation :** Mettre à jour les relations de tâche lorsque l'ordre d'exécution change.

### 20. Valider les dépendances (`validate_dependencies`)

*   **Outil MCP :** `validate_dependencies`
*   **Commande CLI :** `task-master validate-dependencies [options]`
*   **Description :** `Vérifiez vos tâches Taskmaster pour des problèmes de dépendance (comme des références circulaires ou des liens vers des tâches inexistantes) sans apporter de modifications.`
*   **Paramètres/Options clés :**
    *   `tag` : `Spécifiez quel contexte de tag valider. Par défaut, c'est le tag actif actuel.` (CLI : `--tag <nom>`)
    *   `file` : `Chemin vers votre fichier Taskmaster 'tasks.json'. Par défaut, repose sur la détection automatique.` (CLI : `-f, --file <fichier>`)
*   **Utilisation :** Auditer l'intégrité de vos dépendances de tâches.

### 21. Corriger les dépendances (`fix_dependencies`)

*   **Outil MCP :** `fix_dependencies`
*   **Commande CLI :** `task-master fix-dependencies [options]`
*   **Description :** `Corriger automatiquement les problèmes de dépendance (comme des références circulaires ou des liens vers des tâches inexistantes) dans vos tâches Taskmaster.`
*   **Paramètres/Options clés :**
    *   `tag` : `Spécifiez quel contexte de tag corriger les dépendances. Par défaut, c'est le tag actif actuel.` (CLI : `--tag <nom>`)
    *   `file` : `Chemin vers votre fichier Taskmaster 'tasks.json'. Par défaut, repose sur la détection automatique.` (CLI : `-f, --file <fichier>`)
*   **Utilisation :** Nettoyer automatiquement les erreurs de dépendance.

---

## Analyse et rapport

### 22. Analyser la complexité du projet (`analyze_project_complexity`)

*   **Outil MCP :** `analyze_project_complexity`
*   **Commande CLI :** `task-master analyze-complexity [options]`
*   **Description :** `Faire analyser vos tâches par Taskmaster pour déterminer leur complexité et suggérer lesquelles doivent être décomposées davantage.`
*   **Paramètres/Options clés :**
    *   `output` : `Où enregistrer le rapport d'analyse de complexité. Par défaut, c'est '.taskmaster/reports/task-complexity-report.json' (ou '..._tagname.json' si un tag est utilisé).` (CLI : `-o, --output <fichier>`)
    *   `threshold` : `Le score de complexité minimum (1-10) qui devrait déclencher une recommandation d'expansion d'une tâche.` (CLI : `-t, --threshold <nombre>`)
    *   `research` : `Activer le rôle de recherche pour une analyse de complexité plus précise. Nécessite une clé API appropriée.` (CLI : `-r, --research`)
    *   `tag` : `Spécifiez quel contexte de tag analyser. Par défaut, c'est le tag actif actuel.` (CLI : `--tag <nom>`)
    *   `file` : `Chemin vers votre fichier Taskmaster 'tasks.json'. Par défaut, repose sur la détection automatique.` (CLI : `-f, --file <fichier>`)
*   **Utilisation :** Utilisé avant de décomposer des tâches pour identifier celles qui nécessitent le plus d'attention.
*   **Important :** Cet outil MCP effectue des appels IA et peut prendre jusqu'à une minute pour se compléter. Veuillez informer les utilisateurs de patienter pendant que l'opération est en cours.

### 23. Afficher le rapport de complexité (`complexity_report`)

*   **Outil MCP :** `complexity_report`
*   **Commande CLI :** `task-master complexity-report [options]`
*   **Description :** `Afficher le rapport d'analyse de complexité des tâches dans un format lisible.`
*   **Paramètres/Options clés :**
    *   `tag` : `Spécifiez quel contexte de tag afficher le rapport. Par défaut, c'est le tag actif actuel.` (CLI : `--tag <nom>`)
    *   `file` : `Chemin vers le rapport de complexité (par défaut : '.taskmaster/reports/task-complexity-report.json').` (CLI : `-f, --file <fichier>`)
*   **Utilisation :** Examiner et comprendre les résultats de l'analyse de complexité après avoir exécuté analyze-complexity.

---

## Gestion des fichiers

### 24. Générer des fichiers de tâches (`generate`)

*   **Outil MCP :** `generate`
*   **Commande CLI :** `task-master generate [options]`
*   **Description :** `Créer ou mettre à jour des fichiers Markdown individuels pour chaque tâche en fonction de votre tasks.json.`
*   **Paramètres/Options clés :**
    *   `output` : `Le répertoire où Taskmaster doit enregistrer les fichiers de tâches (par défaut : dans un répertoire 'tasks').` (CLI : `-o, --output <répertoire>`)
    *   `tag` : `Spécifiez quel contexte de tag générer des fichiers. Par défaut, c'est le tag actif actuel.` (CLI : `--tag <nom>`)
    *   `file` : `Chemin vers votre fichier Taskmaster 'tasks.json'. Par défaut, repose sur la détection automatique.` (CLI : `-f, --file <fichier>`)
*   **Utilisation :** Exécutez ceci après avoir apporté des modifications à tasks.json pour garder les fichiers de tâches individuels à jour. Cette commande est désormais manuelle et ne s'exécute plus automatiquement.

---

## Recherche alimentée par l'IA

### 25. Recherche (`research`)

*   **Outil MCP :** `research`
*   **Commande CLI :** `task-master research [options]`
*   **Description :** `Effectuer des requêtes de recherche alimentées par l'IA avec le contexte du projet pour obtenir des informations fraîches et à jour au-delà de la date limite de connaissance de l'IA.`
*   **Paramètres/Options clés :**
    *   `query` : `Requis. Requête/commande de recherche (par ex. "Quelles sont les dernières bonnes pratiques pour React Query v5 ?").` (CLI : `[query]` positionnel ou `-q, --query <texte>`)
    *   `taskIds` : `Liste d'IDs de tâche/sous-tâche séparés par des virgules du contexte actuel (par ex. "15,16.2,17").` (CLI : `-i, --id <ids>`)
    *   `filePaths` : `Liste de chemins de fichiers séparés par des virgules pour le contexte (par ex. "src/api.js,docs/readme.md").` (CLI : `-f, --files <chemins>`)
    *   `customContext` : `Texte de contexte personnalisé supplémentaire à inclure dans la recherche.` (CLI : `-c, --context <texte>`)
    *   `includeProjectTree` : `Inclure la structure de l'arborescence des fichiers du projet dans le contexte (par défaut : false).` (CLI : `--tree`)
    *   `detailLevel` : `Niveau de détail pour la réponse de recherche : 'low', 'medium', 'high' (par défaut : medium).` (CLI : `--detail <niveau>`)
    *   `saveTo` : `ID de tâche ou de sous-tâche (par ex. "15", "15.2") pour enregistrer automatiquement la conversation de recherche.` (CLI : `--save-to <id>`)
    *   `saveFile` : `Si vrai, enregistre la conversation de recherche dans un fichier markdown dans '.taskmaster/docs/research/'.` (CLI : `--save-file`)
    *   `noFollowup` : `Désactive le menu interactif de questions de suivi dans la CLI.` (CLI : `--no-followup`)
    *   `tag` : `Spécifiez quel contexte de tag utiliser pour la collecte de contexte basée sur les tâches. Par défaut, c'est le tag actif actuel.` (CLI : `--tag <nom>`)
    *   `projectRoot` : `Le répertoire du projet. Doit être un chemin absolu.` (CLI : Déterminé automatiquement)
*   **Utilisation :** **C'est un outil PUISSANT que les agents devraient utiliser FRÉQUEMMENT** pour :
    *   Obtenir des informations fraîches au-delà des dates limites de connaissance
    *   Rechercher les dernières bonnes pratiques, mises à jour de bibliothèques, correctifs de sécurité
    *   Trouver des exemples d'implémentation pour des technologies spécifiques
    *   Valider des approches par rapport aux normes industrielles actuelles
    *   Obtenir des conseils contextuels basés sur des fichiers et des tâches de projet
*   **Quand envisager d'utiliser la recherche :**
    *   **Avant d'implémenter une tâche** - Rechercher les bonnes pratiques actuelles
    *   **Lors de la rencontre de nouvelles technologies** - Obtenir des conseils d'implémentation à jour (bibliothèques, apis, etc)
    *   **Pour les tâches liées à la sécurité** - Trouver les dernières recommandations de sécurité
    *   **Lors de la mise à jour des dépendances** - Rechercher les changements majeurs et les guides de migration
    *   **Pour l'optimisation des performances** - Obtenir les meilleures pratiques de performance actuelles
    *   **Lors du débogage de problèmes complexes** - Rechercher des solutions et des contournements connus
*   **Modèle Recherche + Action :**
    *   Utiliser `research` pour rassembler des informations fraîches
    *   Utiliser `update_subtask` pour valider les découvertes avec des horodatages
    *   Utiliser `update_task` pour incorporer la recherche dans les détails de la tâche
    *   Utiliser `add_task` avec le flag de recherche pour une création de tâche informée
*   **Important :** Cet outil MCP effectue des appels IA et peut prendre jusqu'à une minute pour se compléter. La recherche fournit des données FRAÎCHES au-delà de la formation de l'IA, ce qui la rend inestimable pour les bonnes pratiques actuelles et les développements récents.

---

## Gestion des tags

Cette nouvelle suite de commandes vous permet de gérer différents contextes de tâches (tags).

### 26. Lister les tags (`tags`)

*   **Outil MCP :** `list_tags`
*   **Commande CLI :** `task-master tags [options]`
*   **Description :** `Lister tous les tags disponibles avec des comptes de tâches, un statut d'achèvement et d'autres métadonnées.`
*   **Paramètres/Options clés :**
    *   `file` : `Chemin vers votre fichier Taskmaster 'tasks.json'. Par défaut, repose sur la détection automatique.` (CLI : `-f, --file <fichier>`)
    *   `--show-metadata` : `Inclure des métadonnées détaillées dans la sortie (par ex. date de création, description).` (CLI : `--show-metadata`)

### 27. Ajouter un tag (`add_tag`)

*   **Outil MCP :** `add_tag`
*   **Commande CLI :** `task-master add-tag <tagName> [options]`
*   **Description :** `Créer un nouveau contexte de tag vide, ou copier des tâches d'un autre tag.`
*   **Paramètres/Options clés :**
    *   `tagName` : `Nom du nouveau tag à créer (alphanumérique, tirets, soulignements).` (CLI : `<tagName>` positionnel)
    *   `--from-branch` : `Crée un tag avec un nom dérivé de la branche git actuelle, en ignorant l'argument <tagName>.` (CLI : `--from-branch`)
    *   `--copy-from-current` : `Copier les tâches du tag actif actuel vers le nouveau tag.` (CLI : `--copy-from-current`)
    *   `--copy-from <tag>` : `Copier les tâches d'un tag source spécifique vers le nouveau tag.` (CLI : `--copy-from <tag>`)
    *   `--description <texte>` : `Fournir une description facultative pour le nouveau tag.` (CLI : `-d, --description <texte>`)
    *   `file` : `Chemin vers votre fichier Taskmaster 'tasks.json'. Par défaut, repose sur la détection automatique.` (CLI : `-f, --file <fichier>`)

### 28. Supprimer un tag (`delete_tag`)

*   **Outil MCP :** `delete_tag`
*   **Commande CLI :** `task-master delete-tag <tagName> [options]`
*   **Description :** `Supprimer définitivement un tag et toutes ses tâches associées.`
*   **Paramètres/Options clés :**
    *   `tagName` : `Nom du tag à supprimer.` (CLI : `<tagName>` positionnel)
    *   `--yes` : `Ignorer l'invite de confirmation.` (CLI : `-y, --yes`)
    *   `file` : `Chemin vers votre fichier Taskmaster 'tasks.json'. Par défaut, repose sur la détection automatique.` (CLI : `-f, --file <fichier>`)

### 29. Utiliser un tag (`use_tag`)

*   **Outil MCP :** `use_tag`
*   **Commande CLI :** `task-master use-tag <tagName>`
*   **Description :** `Changer le contexte de tâche actif vers un autre tag.`
*   **Paramètres/Options clés :**
    *   `tagName` : `Nom du tag vers lequel changer.` (CLI : `<tagName>` positionnel)
    *   `file` : `Chemin vers votre fichier Taskmaster 'tasks.json'. Par défaut, repose sur la détection automatique.` (CLI : `-f, --file <fichier>`)

### 30. Renommer un tag (`rename_tag`)

*   **Outil MCP :** `rename_tag`
*   **Commande CLI :** `task-master rename-tag <oldName> <newName>`
*   **Description :** `Renommer un tag existant.`
*   **Paramètres/Options clés :**
    *   `oldName` : `L'ancien nom du tag.` (CLI : `<oldName>` positionnel)
    *   `newName` : `Le nouveau nom pour le tag.` (CLI : `<newName>` positionnel)
    *   `file` : `Chemin vers votre fichier Taskmaster 'tasks.json'. Par défaut, repose sur la détection automatique.` (CLI : `-f, --file <fichier>`)

### 31. Copier un tag (`copy_tag`)

*   **Outil MCP :** `copy_tag`
*   **Commande CLI :** `task-master copy-tag <sourceName> <targetName> [options]`
*   **Description :** `Copier un contexte de tag entier, y compris toutes ses tâches et métadonnées, vers un nouveau tag.`
*   **Paramètres/Options clés :**
    *   `sourceName` : `Nom du tag à copier depuis.` (CLI : `<sourceName>` positionnel)
    *   `targetName` : `Nom du nouveau tag à créer.` (CLI : `<targetName>` positionnel)
    *   `--description <texte>` : `Description facultative pour le nouveau tag.` (CLI : `-d, --description <texte>`)

---

## Divers

### 32. Synchroniser le README (`sync-readme`) -- expérimental

*   **Outil MCP :** N/A
*   **Commande CLI :** `task-master sync-readme [options]`
*   **Description :** `Exporte votre liste de tâches vers le fichier README.md de votre projet, utile pour montrer les progrès.`
*   **Paramètres/Options clés :**
    *   `status` : `Filtrer les tâches par statut (par ex. 'pending', 'done').` (CLI : `-s, --status <status>`)
    *   `withSubtasks` : `Inclure les sous-tâches dans l'exportation.` (CLI : `--with-subtasks`)
    *   `tag` : `Spécifiez de quel contexte de tag exporter. Par défaut, c'est le tag actif actuel.` (CLI : `--tag <nom>`)

---

## Configuration des variables d'environnement (Mis à jour)

Taskmaster utilise principalement le fichier **`.taskmaster/config.json`** (dans la racine du projet) pour la configuration (modèles, paramètres, niveau de journalisation, etc.), géré via `task-master models --setup`.

Les variables d'environnement sont utilisées **uniquement** pour les clés API sensibles liées aux fournisseurs IA et des substitutions spécifiques comme l'URL de base d'Ollama :

*   **Clés API (Requises pour le fournisseur correspondant) :**
    *   `ANTHROPIC_API_KEY`
    *   `PERPLEXITY_API_KEY`
    *   `OPENAI_API_KEY`
    *   `GOOGLE_API_KEY`
    *   `MISTRAL_API_KEY`
    *   `AZURE_OPENAI_API_KEY` (Nécessite aussi `AZURE_OPENAI_ENDPOINT`)
    *   `OPENROUTER_API_KEY`
    *   `XAI_API_KEY`
    *   `OLLAMA_API_KEY` (Nécessite aussi `OLLAMA_BASE_URL`)
*   **Points de terminaison (Facultatif/Spécifique au fournisseur dans .taskmaster/config.json) :**
    *   `AZURE_OPENAI_ENDPOINT`
    *   `OLLAMA_BASE_URL` (Par défaut : `http://localhost:11434/api`)

**Définissez les clés API** dans votre fichier **`.env`** à la racine du projet (pour utilisation CLI) ou dans la section `env` de votre fichier **`.vscode/mcp.json`** (pour intégration MCP/VS Code). Tous les autres paramètres (choix du modèle, max tokens, température, niveau de journal, points de terminaison personnalisés) sont gérés dans `.taskmaster/config.json` via la commande `task-master models` ou l'outil MCP `models`.

---

Pour des détails sur la façon dont ces commandes s'intègrent dans le processus de développement, voir le fichier [dev_workflow.instructions.md](.github/instructions/dev_workflow.instructions.md).