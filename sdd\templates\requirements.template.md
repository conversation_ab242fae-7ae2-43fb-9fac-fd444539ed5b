# Template Requirements - Format EARS

## Instructions Intégrées pour l'Agent IA

### Objectif
Transformer une idée de fonctionnalité en document d'exigences structuré au format EARS (Easy Approach to Requirements Syntax).

### Processus Obligatoire
1. **Génération initiale** : Créer requirements.md basé sur l'idée utilisateur SANS questions séquentielles préalables
2. **Format EARS strict** : Utiliser la syntaxe QUAND/ALORS/SI/DOIT
3. **Structure hiérarchique** : Numérotation claire avec user stories et critères d'acceptation
4. **Validation utilisateur** : Cycle feedback-révision jusqu'à approbation explicite
5. **Passage au design** : Seulement après validation explicite

### Contraintes Techniques
- Créer le fichier dans `sdd/specs/{feature_name}/requirements.md`
- Nom de fonctionnalité en kebab-case obligatoire
- Prendre en compte les cas limites, UX, contraintes techniques
- Demander validation avec l'outil approprié
- Continuer les révisions jusqu'à approbation explicite

---

# Document des Exigences - {FEATURE_NAME}

## Introduction

[Description claire et concise de la fonctionnalité]

### Contexte
- **Problème résolu** : [Description du problème adressé]
- **Utilisateurs cibles** : [Qui utilisera cette fonctionnalité]
- **Valeur métier** : [Bénéfice attendu pour l'organisation]

### Portée
- **Inclus dans cette version** : [Ce qui sera implémenté]
- **Exclu de cette version** : [Ce qui ne sera pas implémenté]
- **Dépendances** : [Autres fonctionnalités ou systèmes requis]

## Exigences Fonctionnelles

### 1. [Nom de l'exigence principale]

**User Story :** En tant que [rôle], je veux [fonctionnalité], afin que [bénéfice]

#### Critères d'acceptation (Format EARS)

1. **QUAND** [événement déclencheur] **ALORS** [système] **DOIT** [réponse système]
2. **SI** [précondition] **ALORS** [système] **DOIT** [comportement attendu]
3. **QUAND** [événement] **ET** [condition additionnelle] **ALORS** [système] **DOIT** [réponse]

#### Cas d'erreur

1. **QUAND** [condition d'erreur] **ALORS** [système] **DOIT** [gestion d'erreur]
2. **SI** [données invalides] **ALORS** [système] **DOIT** [message d'erreur approprié]

### 2. [Nom de la deuxième exigence]

**User Story :** En tant que [rôle], je veux [fonctionnalité], afin que [bénéfice]

#### Critères d'acceptation (Format EARS)

1. **QUAND** [événement] **ALORS** [système] **DOIT** [réponse]
2. **SI** [condition] **ALORS** [système] **DOIT** [comportement]

#### Règles métier

1. **[Règle 1]** : [Description de la règle métier]
2. **[Règle 2]** : [Description de la règle métier]

### 3. [Exigences additionnelles selon le besoin]

[Répéter la structure ci-dessus pour chaque exigence majeure]

## Exigences Non-Fonctionnelles

### Performance
- **Temps de réponse** : [Exigence de performance]
- **Débit** : [Nombre d'opérations par seconde]
- **Charge** : [Nombre d'utilisateurs simultanés]

### Sécurité
- **Authentification** : [Exigences d'authentification]
- **Autorisation** : [Contrôles d'accès requis]
- **Chiffrement** : [Données à chiffrer]

### Utilisabilité
- **Interface** : [Exigences d'interface utilisateur]
- **Accessibilité** : [Standards d'accessibilité à respecter]
- **Compatibilité** : [Navigateurs/appareils supportés]

### Fiabilité
- **Disponibilité** : [Pourcentage de disponibilité requis]
- **Récupération** : [Temps de récupération après panne]
- **Sauvegarde** : [Fréquence et méthode de sauvegarde]

## Contraintes Techniques

### Architecture
- **Stack technique** : [Technologies à utiliser]
- **Intégrations** : [Systèmes externes à intégrer]
- **Standards** : [Standards de développement à respecter]

### Données
- **Format** : [Formats de données requis]
- **Volume** : [Volume de données attendu]
- **Migration** : [Besoins de migration de données]

## Critères d'Acceptation Globaux

### Tests
1. **QUAND** [tous les tests unitaires sont exécutés] **ALORS** [système] **DOIT** [passer avec 100% de succès]
2. **QUAND** [tests d'intégration sont exécutés] **ALORS** [système] **DOIT** [fonctionner avec les systèmes existants]
3. **QUAND** [tests de performance sont exécutés] **ALORS** [système] **DOIT** [respecter les exigences de performance]

### Documentation
1. **QUAND** [fonctionnalité est livrée] **ALORS** [équipe] **DOIT** [fournir documentation utilisateur complète]
2. **QUAND** [code est livré] **ALORS** [développeurs] **DOIVENT** [fournir documentation technique]

### Déploiement
1. **QUAND** [fonctionnalité est prête] **ALORS** [système] **DOIT** [être déployable sans interruption de service]
2. **QUAND** [déploiement échoue] **ALORS** [système] **DOIT** [permettre un rollback automatique]

## Glossaire

- **[Terme 1]** : [Définition claire du terme]
- **[Terme 2]** : [Définition claire du terme]
- **[Terme 3]** : [Définition claire du terme]

## Références

- [Document de contexte projet]
- [Spécifications techniques existantes]
- [Standards de l'industrie applicables]

---

## Instructions de Validation pour l'Agent

### Questions de Validation Suggérées
1. "Les exigences couvrent-elles tous les cas d'usage principaux ?"
2. "Les critères d'acceptation sont-ils testables et mesurables ?"
3. "Les contraintes techniques sont-elles réalistes ?"
4. "Les exigences non-fonctionnelles sont-elles suffisamment détaillées ?"
5. "Y a-t-il des cas d'erreur ou d'exception manquants ?"

### Format de Validation
```
**Validation Requirements :**
J'ai terminé le document requirements.md avec :
- [X] exigences fonctionnelles principales
- [Y] critères d'acceptation au format EARS
- Gestion des cas d'erreur
- Exigences non-fonctionnelles
- Contraintes techniques

**Les exigences vous conviennent-elles ? Si oui, nous pouvons passer au design.**
```

### Cycle de Révision
1. Présenter le document initial
2. Demander validation explicite
3. Si modifications demandées : réviser et re-valider
4. Répéter jusqu'à approbation explicite
5. Passer au design seulement après validation