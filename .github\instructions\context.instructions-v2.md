# Instructions Contexte SDD - Génération de Documentation Projet

## Objectif et Portée

Cette documentation définit les instructions globales pour la génération automatisée des trois fichiers de contexte projet selon la méthodologie Spec-Driven Development (SDD) :

- `sdd/project/product.md` - Documentation produit et vision
- `sdd/project/structure.md` - Architecture et organisation du projet  
- `sdd/project/tech.md` - Spécifications techniques et implémentation

Ces fichiers constituent la "banque de mémoire" centrale du projet, servant de référence unique pour tous les développements ultérieurs.

## Méthodologie SDD

### Approche "Banque de Mémoire"

La méthodologie SDD repose sur une approche "banque de mémoire" pour la structuration de l'information :

1. **Centralisation** : Toute l'information critique est centralisée dans `sdd/project/`
2. **Référencement** : Les templates dans `sdd/templates/` servent de modèles canoniques
3. **Cohérence** : Une seule source de vérité pour chaque aspect du projet
4. **Traçabilité** : Liens explicites entre besoins, conception et implémentation
5. **Évolutivité** : Structure permettant les mises à jour itératives

### Principes Fondamentaux

- **Validation Explicite Obligatoire** : Aucune génération ne peut être considérée comme terminée sans approbation utilisateur explicite
- **Cycles de Révision Itératifs** : Processus cycle feedback-révision jusqu'à satisfaction complète avec progression incrémentale
- **Instructions Intégrées** : Chaque template contient ses propres règles de validation
- **Non-Duplication** : Référencement des templates plutôt que copie du contenu

## Templates de Référence

### Templates Canoniques

Les templates suivants dans `sdd/templates/` servent de référence obligatoire :

1. **`sdd/templates/product.template.md`**
   - Structure de la documentation produit
   - Sections obligatoires : Vision, Objectifs, Utilisateurs cibles, Fonctionnalités
   - Questions de validation intégrées

2. **`sdd/templates/structure.template.md`**  
   - Architecture générale du projet
   - Sections obligatoires : Organisation, Modules, Dépendances, Patterns
   - Diagrammes et schémas requis

3. **`sdd/templates/tech.template.md`**
   - Spécifications techniques détaillées
   - Sections obligatoires : Technologies, Configuration, APIs, Données
   - Contraintes et exigences non-fonctionnelles

## Intégration GitHub Copilot

### Limites de Contexte
- **Seuil Conservateur** : 70% du contexte token maximum (Phase 1 plus restrictive)
- **Monitoring Préventif** : Alerte automatique dès 60% d'utilisation
- **Réservation Stratégique** : 30% du contexte préservé pour la Phase 2 (spécifications)
- **Alertes Progressives** : Signalement à 50%, 60%, 70% avec actions recommandées

### Stratégies de Chunking
- **Chunking par Domaine** : Traitement séparé Product / Structure / Tech
- **Chunking Contextuel** : Focus sur un aspect métier à la fois pour éviter la surcharge
- **Chunking Historique** : Intégration progressive de la documentation existante
- **Chunking Prédictif** : Préparation optimisée pour la transition vers Phase 2

### Optimisations pour l'IA
- **Cache Contextuel Intelligent** : 
  - Mémorisation des validations utilisateur explicites
  - Stockage des patterns de révision récurrents
  - Conservation des préférences de style et terminologie
- **Références Dynamiques** : Liens intelligents vers documentation existante plutôt que duplication
- **Synthèse Progressive** : Accumulation incrémentale du contexte avec résumés optimisés
- **Pipeline Phase 1→2** : Préparation automatique du contexte pour génération spécifications

### Techniques d'Optimisation Avancées
- **Extraction Sélective** : Identification automatique des éléments critiques pour Phase 2
- **Compression Contextuelle** : Algorithmes de synthèse pour documents volumineux
- **Indexation Sémantique** : Catalogage intelligent des éléments contextuels réutilisables
- **Optimisation Pipeline** : Flux de données optimisé entre Phase 1 et Phase 2

### Utilisation des Templates

- **Lecture Complète Obligatoire** : Chaque template doit être lu intégralement avant génération
- **Respect des Sections** : Toutes les sections template doivent être traitées
- **Application des Instructions** : Les instructions intégrées sont contraignantes
- **Adaptation Contextuelle** : Personnalisation selon le projet spécifique

## Processus Obligatoire

### Étape 1 : Génération Initiale Sans Questions Séquentielles

- Analyser l'ensemble du contexte disponible avant toute génération
- Ne PAS poser de questions séquentielles pendant la génération
- Utiliser le contexte existant et les templates pour inférer les besoins
- Générer des drafts complets basés sur l'analyse contextuelle

### Étape 2 : Utilisation Complète des Sections Template

- Lire intégralement chaque template de référence
- Implémenter TOUTES les sections obligatoires
- Respecter la structure et l'organisation prescrites
- Appliquer les instructions spécifiques de chaque template

### Étape 3 : Application de la Méthodologie Banque de Mémoire

- Centraliser l'information dans les fichiers de contexte
- Éviter la duplication entre les trois documents
- Maintenir la cohérence et la traçabilité
- Créer des références croisées appropriées

### Étape 4 : Validation Utilisateur avec Cycles Feedback-Révision

- Présenter chaque document généré pour validation explicite
- Utiliser les messages de validation standardisés
- Implémenter les modifications demandées
- Répéter jusqu'à approbation explicite

### Étape 5 : Création de Documents de Référence

- Finaliser les documents dans `sdd/project/`
- Vérifier la complétude et la cohérence
- Confirmer l'alignement avec les templates
- Documenter les décisions et rationales

## Contraintes Techniques

### Chemins de Fichiers

- **Destination Obligatoire** : `sdd/project/`
- **Nommage Standard** : `product.md`, `structure.md`, `tech.md`
- **Encodage** : UTF-8 avec BOM si nécessaire
- **Format** : Markdown strict avec métadonnées YAML

### Conventions de Nommage

- Utiliser les noms exacts spécifiés
- Maintenir la cohérence avec la structure existante
- Respecter les conventions de fichiers du projet
- Éviter les caractères spéciaux dans les noms

### Analyse Approfondie Requise

- Examiner l'ensemble du contexte projet avant génération
- Identifier les dépendances et contraintes existantes
- Analyser les patterns et conventions établies
- Intégrer les spécificités du domaine métier

## Validation et Révision

### Questions de Validation Suggérées

**Pour product.md :**
- La vision produit est-elle claire et alignée avec les objectifs ?
- Les utilisateurs cibles sont-ils bien définis et caractérisés ?
- Les fonctionnalités prioritaires sont-elles identifiées ?
- Les métriques de succès sont-elles mesurables ?

**Pour structure.md :**
- L'architecture propose-t-elle une organisation claire ?
- Les modules et leurs responsabilités sont-ils bien définis ?
- Les dépendances sont-elles identifiées et justifiées ?
- Les patterns choisis sont-ils appropriés au contexte ?

**Pour tech.md :**
- Les technologies sélectionnées sont-elles justifiées ?
- La configuration proposée est-elle complète et cohérente ?
- Les APIs sont-elles bien spécifiées ?
- Les contraintes non-fonctionnelles sont-elles respectées ?

### Format de Validation Standard

```
**Validation [Type de Document] :** J'ai terminé le document [nom].md avec :
- [Point de validation 1]
- [Point de validation 2] 
- [Point de validation 3]
- [Points additionnels si pertinents]

Cette documentation [type] vous convient-elle ?
```

### Gestion des Révisions

- Intégrer les commentaires utilisateur de manière exhaustive
- Maintenir la cohérence avec les autres documents
- Vérifier l'alignement avec les templates de référence
- Documenter les changements majeurs et leurs rationales

## Maintenance

### Procédures de Mise à Jour

- Surveiller les évolutions des templates de référence
- Maintenir la synchronisation avec les changements projet
- Mettre à jour les références croisées si nécessaire
- Valider la cohérence après modifications

### Versioning et Historique

- Documenter les versions majeures des contextes générés
- Maintenir un historique des décisions importantes
- Tracer l'évolution des spécifications
- Préserver la compatibilité descendante quand possible

---

*Ces instructions constituent le guide de référence pour la génération automatisée de contexte SDD. Elles doivent être appliquées rigoureusement pour maintenir la qualité et la cohérence de la documentation projet.*
