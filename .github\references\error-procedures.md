# Référence Technique - Procédures d'Erreur GitHub Copilot

## Scénarios d'Erreur Courants

### 1. Débordement de Contexte
**Symptômes** : 
- Troncature des réponses
- Perte d'informations en fin de génération
- Messages d'erreur de limite atteinte

**Procédures de Récupération** :
1. **Réduction automatique** : Limitation au domaine prioritaire
2. **Chunking d'urgence** : Basculement en mode sections
3. **Sauvegarde rapide** : Préservation des éléments validés
4. **Escalade utilisateur** : Assistance si critique

### 2. Gestion des Timeouts
**Symptômes** :
- Arrêt inattendu des générations
- Perte de session en cours
- Non-réponse prolongée

**Procédures de Récupération** :
1. **Reprise contextuelle** : Au dernier point de validation
2. **Restauration automatique** : Des éléments sauvegardés
3. **Génération partielle** : Avec continuation manuelle
4. **Documentation des pertes** : Pour régénération ciblée

### 3. Problèmes d'Accès aux Templates
**Symptômes** :
- Erreurs de lecture `sdd/templates/`
- Fichiers introuvables ou corrompus
- Permissions insuffisantes

**Procédures de Récupération** :
1. **Templates de base** : Utilisation des versions intégrées
2. **Structure minimale** : Génération avec garanties de base
3. **Signalement explicite** : Des limitations à l'utilisateur
4. **Report temporaire** : Jusqu'à résolution du problème

### 4. Interruptions des Cycles de Validation
**Symptômes** :
- Perte de contexte de validation
- Cycles incomplets ou interrompus
- État incohérent des validations

**Procédures de Récupération** :
1. **Re-présentation** : De la dernière validation en cours
2. **Récapitulatif** : Des éléments déjà validés
3. **Continuation** : Du cycle à partir du point d'interruption
4. **Redémarrage optionnel** : Complet si nécessaire

## Escalade et Support

### Niveaux d'Escalade
- **Niveau 1** : Récupération automatique
- **Niveau 2** : Assistance guidée utilisateur
- **Niveau 3** : Intervention manuelle nécessaire

### Diagnostic et Résolution
- **Collecte d'informations** : État système et contexte d'erreur
- **Identification de cause** : Analyse des symptômes
- **Application de solution** : Selon procédures établies
- **Vérification de récupération** : Test de fonctionnement normal

### Documentation des Incidents
- **Horodatage** : Moment de l'erreur
- **Contexte** : État du système lors de l'incident
- **Actions prises** : Procédures appliquées
- **Résolution** : Méthode de récupération réussie

---

**Utilisation** : Cette référence est consultée automatiquement par les agents SDD lors de la détection d'erreurs pour appliquer les procédures de récupération appropriées.
