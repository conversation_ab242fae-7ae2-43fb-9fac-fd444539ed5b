# Template Exécution des Tâches - Instructions Agent

## Instructions Intégrées pour l'Agent IA

### Objectif
Guider l'agent IA dans l'exécution correcte et méthodique des tâches d'implémentation définies dans tasks.md.

### Processus Obligatoire d'Exécution

#### 1. Préparation Obligatoire
**AVANT toute exécution de tâche, l'agent DOIT :**
- [ ] Lire et comprendre `requirements.md` complet
- [ ] Lire et comprendre `design.md` complet  
- [ ] Lire et comprendre `tasks.md` complet
- [ ] Identifier la tâche spécifique demandée par l'utilisateur

> ⚠️ **CRITIQUE** : Exécuter des tâches sans avoir lu ces documents entraînera des implémentations inexactes et non conformes.

#### 2. Règles d'Exécution Strictes

##### Focus Unique
- **UNE SEULE tâche à la fois** - Ne jamais implémenter plusieurs tâches simultanément
- **Respect de la hiérarchie** - Si la tâche a des sous-tâches, commencer TOUJOURS par les sous-tâches
- **Séquentialité** - Suivre l'ordre défini dans tasks.md

##### Validation Continue
- **Conformité requirements** - Vérifier l'implémentation contre TOUTES les exigences spécifiées
- **Conformité design** - Respecter l'architecture et les choix techniques du design.md
- **Conformité tâche** - Répondre précisément aux critères de la tâche en cours

##### Arrêt Obligatoire
- **Fin de tâche** - S'arrêter immédiatement après completion d'une tâche
- **Attendre validation** - Laisser l'utilisateur réviser avant de continuer
- **Pas de continuation automatique** - Ne JAMAIS passer automatiquement à la tâche suivante

#### 3. Gestion des Questions vs Exécution

##### Questions Informatives
Si l'utilisateur pose des questions sur les tâches SANS demander d'exécution :
- Fournir l'information demandée
- Ne PAS commencer d'exécution
- Proposer des recommandations si approprié

##### Demandes d'Exécution
Si l'utilisateur demande d'exécuter une tâche :
- Suivre le processus d'exécution complet
- Confirmer la tâche à exécuter
- Procéder méthodiquement

---

# Guide d'Exécution des Tâches - {FEATURE_NAME}

## Contexte de la Fonctionnalité

### Documents de Référence
- **Requirements** : `sdd/specs/{feature_name}/requirements.md`
- **Design** : `sdd/specs/{feature_name}/design.md`
- **Tasks** : `sdd/specs/{feature_name}/tasks.md`

### Résumé de la Fonctionnalité
[Résumé basé sur requirements.md et design.md]

### Objectifs d'Implémentation
- [Objectif principal de la fonctionnalité]
- [Objectifs techniques secondaires]
- [Contraintes à respecter]

## Instructions d'Exécution par Phase

### Phase 1 : Foundation et Setup

#### Avant de Commencer
```
✅ Checklist Préparation :
- [ ] Requirements.md lu et compris
- [ ] Design.md lu et compris
- [ ] Tasks.md lu et compris
- [ ] Environnement de développement prêt
- [ ] Outils de test configurés
```

#### Tâches 1.x - Configuration
**Instructions spécifiques :**
- Respecter l'architecture définie dans design.md
- Utiliser les technologies spécifiées
- Configurer selon les contraintes techniques
- Valider chaque étape avant de continuer

**Validation après chaque tâche 1.x :**
- [ ] Structure conforme au design
- [ ] Configuration fonctionnelle
- [ ] Tests de base passent

### Phase 2 : Logique Métier Core

#### Tâches 2.x - Modèles et Services
**Instructions spécifiques :**
- Implémenter selon les modèles de données du design.md
- Respecter les règles métier du requirements.md
- Écrire les tests AVANT l'implémentation (TDD)
- Valider contre les critères d'acceptation EARS

**Validation après chaque tâche 2.x :**
- [ ] Modèles conformes au schéma design.md
- [ ] Règles métier implémentées
- [ ] Tests unitaires passent
- [ ] Critères EARS validés

### Phase 3 : API et Interfaces

#### Tâches 3.x - API et Sécurité
**Instructions spécifiques :**
- Implémenter les endpoints selon design.md
- Respecter les formats de réponse définis
- Intégrer la sécurité selon les spécifications
- Tester tous les cas d'erreur

**Validation après chaque tâche 3.x :**
- [ ] API conforme aux spécifications
- [ ] Sécurité implémentée
- [ ] Gestion d'erreurs fonctionnelle
- [ ] Tests d'intégration passent

### Phase 4 : Interface Utilisateur

#### Tâches 4.x - UI et UX
**Instructions spécifiques :**
- Respecter les spécifications UX du requirements.md
- Implémenter selon l'architecture frontend du design.md
- Intégrer avec l'API backend
- Tester l'expérience utilisateur

**Validation après chaque tâche 4.x :**
- [ ] Interface conforme aux requirements
- [ ] Intégration API fonctionnelle
- [ ] UX validée
- [ ] Tests frontend passent

## Protocole d'Exécution d'une Tâche

### 1. Préparation
```
Agent : "Je vais exécuter la tâche {TASK_ID} : {TASK_DESCRIPTION}"

✅ Vérifications :
- [ ] Documents de référence lus
- [ ] Tâche comprise
- [ ] Dépendances satisfaites
- [ ] Environnement prêt
```

### 2. Exécution
```
Agent : "Début d'implémentation de la tâche {TASK_ID}"

📋 Actions :
1. Analyser les requirements spécifiques
2. Consulter le design pour les détails techniques
3. Implémenter selon les bonnes pratiques
4. Écrire/exécuter les tests
5. Valider contre les critères
```

### 3. Validation
```
Agent : "Validation de la tâche {TASK_ID}"

✅ Critères :
- [ ] Fonctionnalité implémentée
- [ ] Tests passent
- [ ] Requirements respectés
- [ ] Design respecté
- [ ] Code quality OK
```

### 4. Completion
```
Agent : "Tâche {TASK_ID} terminée. Résumé :"
- Fonctionnalité implémentée : [Description]
- Tests ajoutés : [Liste]
- Fichiers modifiés : [Liste]
- Prochaine tâche suggérée : {NEXT_TASK_ID}

"Souhaitez-vous que je continue avec la tâche suivante ?"
```

## Gestion des Cas Spéciaux

### Tâche avec Sous-tâches
```
Si tâche X.0 a des sous-tâches X.1, X.2, X.3 :
1. Commencer par X.1
2. Terminer X.1 complètement
3. Demander validation
4. Continuer avec X.2 seulement si approuvé
```

### Blocage ou Problème
```
En cas de blocage :
1. Identifier le problème précisément
2. Consulter les documents de référence
3. Proposer des solutions alternatives
4. Demander clarification à l'utilisateur
5. Ne PAS improviser sans validation
```

### Modification des Requirements
```
Si incohérence détectée :
1. Arrêter l'exécution
2. Signaler le problème
3. Proposer de revenir aux phases précédentes
4. Attendre instruction utilisateur
```

## Templates de Communication

### Début de Tâche
```
🚀 **Début Tâche {TASK_ID}**

**Objectif :** {TASK_DESCRIPTION}
**Références :** Requirements {REQ_IDS}, Design {DESIGN_SECTIONS}
**Durée estimée :** {ESTIMATION}
**Dépendances :** {DEPENDENCIES}

Procédure :
1. [Étape 1]
2. [Étape 2]
3. [Étape 3]

Commencer l'implémentation...
```

### Fin de Tâche
```
✅ **Tâche {TASK_ID} Terminée**

**Implémenté :**
- [Fonctionnalité 1]
- [Fonctionnalité 2]

**Tests Ajoutés :**
- [Test 1]
- [Test 2]

**Fichiers Modifiés :**
- [Fichier 1]
- [Fichier 2]

**Validation :**
- [ ] Requirements respectés
- [ ] Design respecté
- [ ] Tests passent

**Prochaine Tâche Suggérée :** {NEXT_TASK_ID}

Souhaitez-vous continuer ?
```

### Question sur Tâches
```
📋 **Information Tâches - {FEATURE_NAME}**

**Tâche Actuelle :** {CURRENT_TASK}
**Tâches Complétées :** {COMPLETED_TASKS}
**Tâches Restantes :** {REMAINING_TASKS}

**Prochaine Tâche Recommandée :** {NEXT_TASK}
**Raison :** {JUSTIFICATION}

Souhaitez-vous exécuter cette tâche ou avez-vous d'autres questions ?
```

## Métriques et Suivi

### Progression
- **Tâches Complétées :** {X}/{TOTAL}
- **Phase Actuelle :** {CURRENT_PHASE}
- **Temps Estimé Restant :** {ESTIMATION}

### Qualité
- **Couverture Tests :** {PERCENTAGE}%
- **Requirements Validés :** {X}/{TOTAL}
- **Design Respecté :** {PERCENTAGE}%

---

## Rappels Critiques pour l'Agent

### ⚠️ INTERDICTIONS ABSOLUES
- Ne JAMAIS exécuter une tâche sans avoir lu requirements.md, design.md, tasks.md
- Ne JAMAIS continuer automatiquement à la tâche suivante
- Ne JAMAIS implémenter plusieurs tâches simultanément
- Ne JAMAIS improviser sans référence aux documents

### ✅ OBLIGATIONS ABSOLUES
- TOUJOURS lire les documents de référence avant exécution
- TOUJOURS s'arrêter après completion d'une tâche
- TOUJOURS valider contre requirements et design
- TOUJOURS demander confirmation avant de continuer

### 🎯 OBJECTIFS PRIORITAIRES
- Conformité aux spécifications
- Qualité du code et des tests
- Progression méthodique et contrôlée
- Communication claire avec l'utilisateur