# Structure du Projet

## Organisation Générale

Ce projet suit une architecture modulaire organisée autour des workflows de développement dirigé par les spécifications (SPEC Driven Development).

## Arborescence Principale

```
/
├── .claude/                    # Workflows Claude Code
│   ├── specs/                  # Spécifications par fonctionnalité
│   ├── commands/               # Commandes personnalisées
│   ├── scripts/                # Scripts d'automatisation
│   └── templates/              # Modèles de documents
│
├── .claude-code-spec-workflown/ # Variante workflow Claude
├── .cline/                     # Configuration Cline
├── .kiro/                      # Configuration Kiro (ce projet)
│   └── steering/               # Règles de pilotage
│
├── .spec/                      # Spécifications centralisées
│   ├── context/                # Contexte global
│   └── features/               # Spécifications par fonctionnalité
│
├── docs/                       # Documentation technique
│   ├── prompts/                # Bibliothèque de prompts
│   └── *.md                    # Rapports et guides
│
└── project/                    # Projets et implémentations
    ├── SDD/                    # SPEC Driven Development
    ├── SPEC/                   # Spécifications par outil
    ├── TEAMS/                  # Workflows d'équipe
    └── UDPS/                   # User-Driven Product Specifications
```

## Conventions de Nommage

### Fichiers de Spécifications
- `requirements.md` : Exigences au format EARS
- `design.md` : Conception technique détaillée
- `tasks.md` : Plan d'implémentation avec tâches atomiques

### Fichiers de Documentation
- `Rapport-*.md` : Rapports de recherche et d'analyse
- `*-instructions.md` : Guides d'utilisation et instructions
- `Planning-Todo.md` : Planification et suivi des tâches

### Dossiers par Outil
- Chaque outil IA a son dossier dédié avec ses spécifications
- Format : `Spec-{NomOutil}.md` (ex: `Spec-Claude.md`, `Spec-Cursor.md`)

## Flux de Travail

### 1. Spécifications (.spec/ ou .claude/specs/)
- Point d'entrée pour toute nouvelle fonctionnalité
- Structure : Exigences → Conception → Tâches

### 2. Documentation (docs/)
- Rapports de recherche et guides techniques
- Bibliothèque de prompts réutilisables

### 3. Implémentation (project/)
- Projets concrets organisés par méthodologie
- Chaque sous-dossier contient des implémentations spécifiques

## Règles d'Organisation

### Cohérence
- Tous les documents en français (sauf code technique)
- Format Markdown avec structure standardisée
- Références croisées entre documents

### Traçabilité
- Chaque tâche doit référencer des exigences (_Exigences : X.Y_)
- Liens entre spécifications et implémentations
- Historique des modifications via Git

### Modularité
- Séparation claire entre contexte, spécifications et implémentation
- Réutilisabilité des composants entre projets
- Isolation des configurations par outil