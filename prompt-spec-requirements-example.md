# Prompt Spec Requirements Example

```md
# Document des exigences

## Introduction

[Texte d'introduction ici]

## Exigences

### Exigence 1

**User Story :** En tant que [rôle], je veux [fonctionnalité], afin que [bénéfice]

#### Critères d'acceptation
Cette section doit comporter des exigences EARS

1. QUAND [événement] ALORS [système] DOIT [réponse]
2. SI [précondition] ALORS [système] DOIT [réponse]
        
### Exigence 2

**User Story :** En tant que [rôle], je veux [fonctionnalité], afin que [bénéfice]

#### Critères d'acceptation

1. QUAND [événement] ALORS [système] DOIT [réponse]
2. QUAND [événement] ET [condition] ALORS [système] DOIT [réponse]
```