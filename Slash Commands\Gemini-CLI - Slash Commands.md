# Guide complet des Slash Commands dans Gemini CLI

Les **commandes de barre oblique personnalisées** (custom slash commands) représentent une nouvelle fonctionnalité majeure de Gemini CLI qui permet d'étendre et de personnaliser l'interface en ligne de commande. Ce guide vous explique comment créer, configurer et utiliser efficacement ces commandes pour optimiser vos workflows de développement.

## Introduction et mise à jour

### Qu'est-ce que les slash commands ?

Les slash commands sont des commandes personnalisées définies dans des fichiers `.toml` ou via des prompts Model Context Protocol (MCP). Elles permettent de créer des raccourcis puissants et réutilisables pour des tâches complexes ou répétitives.

### Mise à jour nécessaire

Avant d'utiliser les slash commands, assurez-vous d'avoir la dernière version de Gemini CLI :

**Pour npx :**
```bash
npx @google/generative-ai-cli@latest
```

**Pour npm :**
```bash
npm update -g @google/generative-ai-cli
```

## Fondation avec les fichiers .toml

### Structure de base

Les slash commands personnalisées sont basées sur des **fichiers `.toml`** qui offrent une structure puissante et extensible. La configuration minimale ne nécessite qu'une clé `prompt`, mais supporte des fonctionnalités avancées :

- **Arguments dynamiques** : `{{args}}`
- **Exécution de commandes shell** : `!{...}`

### Exemple pratique : Révision de PR GitHub

Voici un exemple de fichier `.toml` pour réviser une pull request GitHub :

```toml
[command]
name = "review"
prompt = "Review GitHub PR #{{args}} using: !{gh pr view {{args}} --json title,body,commits}"
```

Cette commande s'utilise avec : `/review <issue_number>`

## Système de namespacing

### Organisation hiérarchique

Le **namespacing** permet d'organiser les commandes en groupes logiques. Le nom d'une commande est déterminé par son chemin relatif dans le répertoire `commands`.

### Exemples de structure

| Chemin du fichier | Commande résultante |
|-------------------|-------------------|
| `<project>/.gemini/commands/test.toml` | `/test` |
| `<project>/.gemini/commands/git/commit.toml` | `/git:commit` |

Les sous-répertoires créent des **commandes namespacées** où les séparateurs de chemin (`/` ou `\`) sont convertis en deux-points (`:`).

## Construction d'une slash command : Guide étape par étape

### Étape 1 : Créer le fichier de commande

Créez un fichier nommé `plan.toml` dans le répertoire `~/.gemini/commands/`. Cette commande `/plan` permettra de planifier les modifications sans les implémenter directement.

### Étape 2 : Définir la portée

Vous avez deux options de portée :

- **User-scoped** : `~/.gemini/commands/` - Disponible pour tous les projets Gemini CLI de l'utilisateur
- **Project-scoped** : `.gemini/commands/` - Disponible uniquement pour le projet courant

**Conseil** : Pour les workflows de projet, ajoutez ces fichiers au contrôle de version Git !

### Étape 3 : Ajouter la définition

Contenu du fichier `plan.toml` :

```toml
[command]
prompt = "Please provide a step-by-step plan for implementing the requested changes. Do not start implementation, just create a detailed plan that I can review and provide feedback on."
```

### Étape 4 : Utiliser la commande

La commande peut maintenant être utilisée dans Gemini CLI :

```bash
/plan
```

Gemini planifiera les modifications et fournira un plan d'exécution détaillé étape par étape.

## Intégration enrichie avec MCP Prompts

### Fonctionnement de l'intégration MCP

Gemini CLI offre une **intégration native avec Model Context Protocol (MCP)** en supportant les MCP Prompts comme slash commands. Cette intégration utilise une approche standardisée où :

- Le **nom** et la **description** du prompt MCP deviennent le nom et la description de la slash command
- Les **arguments** MCP sont supportés via la syntaxe : `/mycommand --<argument_name>="<argument_value>"` ou positionnellement : `/mycommand <argument1> <argument2>`

### Exemple avec FastMCP Python server

Voici un exemple de commande `/research` utilisant FastMCP Python server :

```bash
/research --topic="machine learning" --depth="comprehensive"
```

## Configuration avancée

### Arguments dynamiques

Utilisez `{{args}}` dans vos prompts pour capturer les arguments passés à la commande.

### Exécution de commandes shell

Intégrez des commandes système avec la syntaxe `!{...}` pour exécuter des commandes shell directement dans le prompt.

### Exemple complet

```toml
[command]
name = "git-status"
prompt = "Analyze the current Git status: !{git status --porcelain} and suggest next actions for: {{args}}"
```

## Bonnes pratiques

### Organisation des commandes

1. **Groupez** les commandes liées par namespace
2. **Documentez** vos commandes avec des descriptions claires
3. **Versionnez** vos commandes projet dans Git
4. **Testez** vos commandes avant de les déployer

### Sécurité

- Évitez d'inclure des informations sensibles dans les fichiers `.toml`
- Validez les entrées utilisateur dans vos prompts
- Limitez les permissions des commandes shell

## Commandes SDD (Spec-Driven Development)

### Automatisation du Contexte Projet

La commande `/sdd:context` génère automatiquement la documentation de contexte projet selon la méthodologie SDD :

```bash
/sdd:context
```

Cette commande crée séquentiellement et automatiquement :
- `sdd/project/product.md` - Documentation produit et vision
- `sdd/project/structure.md` - Architecture et organisation du projet
- `sdd/project/tech.md` - Spécifications techniques et implémentation

### Automatisation des Spécifications

La commande `/sdd:spec` génère automatiquement les spécifications pour une fonctionnalité :

```bash
/sdd:spec user-authentication
/sdd:spec payment-processing
```

Cette commande crée séquentiellement dans `sdd/specs/{feature-name}/` :
- `requirements.md` - Exigences au format EARS
- `design.md` - Design technique avec diagrammes Mermaid
- `tasks.md` - Plan d'implémentation en checklist

### Caractéristiques de l'Automatisation SDD

- **Génération séquentielle automatique** : Aucune validation manuelle entre les étapes
- **Respect strict des templates** : Utilisation complète des templates SDD
- **Nommage kebab-case** : Les noms de fonctionnalités doivent être en kebab-case
- **Traçabilité complète** : Liens entre spécifications et implémentation
- **Méthodologie banque de mémoire** : Centralisation de l'information projet

## Pour commencer

Pour transformer votre expérience terminal avec Gemini CLI :

1. **Mettez à jour** vers la dernière version
2. **Créez** votre premier fichier `.toml`
3. **Testez** votre commande personnalisée
4. **Explorez** la documentation complète sur les Custom Commands

Les slash commands ouvrent un monde de possibilités pour automatiser et optimiser vos workflows de développement. Commencez par des commandes simples et évoluez vers des configurations plus complexes selon vos besoins.