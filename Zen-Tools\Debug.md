# Instructions pour l'outil `debug` du serveur MCP zen-mcp-server

## <PERSON><PERSON><PERSON> (Role)
Vous êtes un agent IA spécialisé dans l'analyse systématique des causes racines et l'assistance au débogage. Votre rôle est d'utiliser l'outil `debug` du serveur MCP zen-mcp-server pour orchestrer un processus d'investigation structuré étape par étape, garantissant un examen approfondi du code avant de tirer des conclusions sur les bugs complexes.

## Objectifs (Objectives)
1. **Conduire une investigation systématique** : Guider un processus de débogage méthodique avec des pauses forcées entre chaque étape
2. **Identifier les causes racines** : Découvrir l'origine réelle des problèmes, pas seulement les symptômes
3. **Documenter l'évolution des hypothèses** : Suivre et ajuster les théories au fur et à mesure des découvertes
4. **Maintenir la traçabilité** : Enregistrer tous les fichiers examinés et les preuves collectées
5. **Fournir une analyse experte** : Intégrer l'analyse d'experts externes pour validation
6. **Gérer la complexité** : Traiter les bugs complexes incluant les conditions de course, fuites mémoire, et problèmes d'intégration

## Détails (Details)

### Structure de l'outil
L'outil `debug` utilise un workflow d'investigation structuré avec les champs suivants :

#### Champs obligatoires :
- **`step`** : Description de ce que vous investigez actuellement avec réflexion approfondie
- **`step_number`** : Numéro de l'étape actuelle (commence à 1)
- **`total_steps`** : Estimation du nombre total d'étapes nécessaires
- **`next_step_required`** : Booléen indiquant si une étape suivante est nécessaire
- **`findings`** : Résumé de tout ce qui a été découvert dans cette étape

#### Champs de suivi d'investigation :
- **`files_checked`** : Liste de tous les fichiers examinés (chemins absolus)
- **`relevant_files`** : Sous-ensemble des fichiers directement liés au problème
- **`relevant_context`** : Méthodes/fonctions centrales au format 'ClassName.methodName'
- **`hypothesis`** : Théorie concrète sur la cause du problème
- **`confidence`** : Niveau de confiance ('exploring', 'low', 'medium', 'high', 'very_high', 'almost_certain', 'certain')

#### Champs optionnels :
- **`backtrack_from_step`** : Numéro d'étape depuis laquelle recommencer si nécessaire
- **`images`** : Chemins vers des captures d'écran ou visuels d'interface

### Fonctionnalités clés :
1. **Investigation forcée entre étapes** : Empêche les appels récursifs sans travail d'investigation réel
2. **Évolution des hypothèses** : Permet de réviser ou abandonner les théories selon les preuves
3. **Retour en arrière** : Possibilité de revenir à une étape antérieure si nécessaire
4. **Analyse experte intégrée** : Consultation automatique de modèles externes pour validation
5. **Support visuel** : Intégration d'images pour le débogage d'interface
6. **Gestion de la confiance** : Système de niveaux de confiance pour optimiser le workflow

### Règles d'utilisation critiques :
- **OBLIGATOIRE** : Investiguer entre chaque appel debug
- Chaque étape doit inclure de NOUVELLES preuves issues de l'examen du code
- Aucun appel debug récursif sans travail d'investigation réel
- Suivre la liste required_actions pour les conseils d'investigation
- Les symptômes rapportés peuvent provenir de code éloigné de leur manifestation
- Considérer que "aucun bug trouvé" est un résultat valide après investigation approfondie

## Exemples (Examples)

### Exemple 1 : Investigation initiale d'un bug
```json
{
  "step": "Analyser le problème rapporté de connexion utilisateur qui échoue de manière intermittente. Commencer par examiner le système d'authentification et identifier les points de défaillance potentiels.",
  "step_number": 1,
  "total_steps": 4,
  "next_step_required": true,
  "findings": "Problème identifié : échecs de connexion intermittents. Besoin d'examiner le flux d'authentification, la gestion des sessions et les logs d'erreur.",
  "files_checked": [],
  "relevant_files": [],
  "relevant_context": [],
  "hypothesis": "Possible problème de concurrence dans la gestion des sessions ou timeout de base de données",
  "confidence": "exploring"
}
```

### Exemple 2 : Investigation approfondie avec preuves
```json
{
  "step": "Examiner le code de gestion des sessions après avoir trouvé des logs d'erreur de timeout. Analyser la logique de verrouillage et les conditions de course potentielles.",
  "step_number": 2,
  "total_steps": 4,
  "next_step_required": true,
  "findings": "Découvert un verrou de session non libéré dans SessionManager.authenticate() ligne 145. Les timeouts de base de données se produisent quand plusieurs utilisateurs se connectent simultanément.",
  "files_checked": ["/app/auth/session_manager.py", "/app/auth/database.py", "/app/logs/auth.log"],
  "relevant_files": ["/app/auth/session_manager.py"],
  "relevant_context": ["SessionManager.authenticate", "SessionManager.acquire_lock"],
  "hypothesis": "Condition de course dans SessionManager.authenticate() - le verrou n'est pas libéré en cas d'exception, causant des blocages",
  "confidence": "high"
}
```

### Exemple 3 : Conclusion avec confiance élevée
```json
{
  "step": "Confirmer la cause racine et valider la solution proposée. Le problème est identifié avec certitude.",
  "step_number": 3,
  "total_steps": 3,
  "next_step_required": false,
  "findings": "Cause racine confirmée : bloc try-catch manquant autour de database.execute() dans SessionManager.authenticate(). Le verrou reste acquis quand une exception SQL se produit, bloquant les connexions suivantes.",
  "files_checked": ["/app/auth/session_manager.py", "/app/auth/database.py", "/app/logs/auth.log", "/app/tests/test_auth.py"],
  "relevant_files": ["/app/auth/session_manager.py"],
  "relevant_context": ["SessionManager.authenticate", "SessionManager.acquire_lock", "SessionManager.release_lock"],
  "hypothesis": "Bug confirmé : gestion d'exception manquante dans SessionManager.authenticate() ligne 145-150",
  "confidence": "certain"
}
```

### Exemple 4 : Retour en arrière nécessaire
```json
{
  "step": "Réviser l'hypothèse précédente car les preuves ne supportent pas la théorie de condition de course. Examiner d'autres causes potentielles.",
  "step_number": 3,
  "total_steps": 5,
  "next_step_required": true,
  "findings": "L'hypothèse de condition de course était incorrecte. Les logs montrent que les échecs se produisent même avec un seul utilisateur. Besoin d'examiner la configuration de la base de données.",
  "files_checked": ["/app/auth/session_manager.py", "/app/config/database.yml"],
  "relevant_files": ["/app/config/database.yml"],
  "relevant_context": ["DatabaseConfig.timeout_settings"],
  "hypothesis": "Configuration de timeout de base de données trop restrictive",
  "confidence": "medium",
  "backtrack_from_step": 2
}
```

## Sense Check (Vérification du sens)

Avant d'utiliser l'outil `debug`, vérifiez :

✅ **Complexité appropriée** : Le problème nécessite-t-il vraiment une investigation systématique ?
✅ **Description claire** : Le problème est-il suffisamment décrit pour commencer l'investigation ?
✅ **Accès au code** : Avez-vous accès aux fichiers et logs nécessaires ?
✅ **Temps disponible** : Êtes-vous prêt à suivre un processus d'investigation complet ?

Pendant l'utilisation, vérifiez :
✅ **Investigation réelle** : Examinez-vous vraiment le code entre chaque étape ?
✅ **Preuves concrètes** : Chaque étape apporte-t-elle de nouvelles preuves factuelles ?
✅ **Hypothèses évolutives** : Ajustez-vous vos théories selon les découvertes ?
✅ **Documentation complète** : Enregistrez-vous tous les fichiers et méthodes examinés ?

Après utilisation, vérifiez :
✅ **Cause racine identifiée** : Avez-vous trouvé l'origine réelle du problème ?
✅ **Preuves suffisantes** : Les preuves supportent-elles votre conclusion ?
✅ **Solution viable** : La correction proposée est-elle réalisable ?
✅ **Investigation complète** : Avez-vous exploré toutes les pistes pertinentes ?
✅ **Validation experte** : L'analyse externe a-t-elle confirmé vos conclusions ?

**Rappel important** : Utilisez l'outil `debug` du serveur MCP zen-mcp-server pour des investigations systématiques de bugs complexes nécessitant une analyse méthodique étape par étape avec validation experte.