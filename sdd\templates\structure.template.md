# Template Structure - Documentation Structure Projet

## Instructions Intégrées pour l'Agent IA

### Objectif
Créer une documentation de structure projet complète qui définit l'organisation des dossiers, les conventions de nommage, et l'architecture de fichiers pour garantir une approche systématique et traçable du développement.

### Processus Obligatoire
1. **Génération initiale** : Créer structure.md basé sur le contexte projet SANS questions séquentielles préalables
2. **Structure complète** : Utiliser toutes les sections avec analyse organisationnelle approfondie
3. **Méthodologie banque de mémoire** : Structurer pour faciliter la navigation et maintenance
4. **Validation utilisateur** : Cycle feedback-révision jusqu'à approbation explicite
5. **Documentation de référence** : Créer la source de vérité structurelle du projet

### Contraintes Techniques
- Créer le fichier dans `sdd/project/structure.md`
- Analyser l'organisation optimale selon le type de projet
- Définir des conventions claires et cohérentes
- Intégrer avec les bonnes pratiques SDD
- Demander validation avec l'outil approprié
- Continuer les révisions jusqu'à approbation explicite

---

# Structure du Projet - {PROJECT_NAME}

## Vue d'ensemble

### Objectif de la Structure
[Description de l'organisation choisie et de sa justification par rapport aux besoins du projet et aux bonnes pratiques de développement]

### Principes d'Organisation
- **Séparation des responsabilités** : [Comment les préoccupations sont séparées]
- **Scalabilité** : [Comment la structure évolue avec le projet]
- **Maintenabilité** : [Facilité de navigation et modification]
- **Cohérence** : [Standards uniformes dans tout le projet]
- **Traçabilité** : [Lien entre spécifications et implémentation]

### Méthodologie SDD Intégrée
Cette structure implémente la méthodologie Spec-Driven Development (SDD) qui garantit une approche systématique avec une traçabilité complète entre spécifications et implémentation.

## Architecture des Dossiers

### Structure Globale
```
{project-name}/
├── sdd/                          # Système SDD complet
│   ├── project/                  # Contexte du projet actuel
│   │   ├── structure.md          # Ce fichier - structure projet
│   │   ├── product.md            # Documentation produit et vision
│   │   ├── tech.md               # Stack technique et contraintes
│   │   ├── context.md            # Contexte technique et métier
│   │   └── guidelines.md         # Directives spécifiques au projet
│   ├── templates/                # Templates pour agents IA
│   │   ├── requirements.template.md
│   │   ├── design.template.md
│   │   ├── tasks.template.md
│   │   ├── task-execution.template.md
│   │   ├── system-prompt.template.md
│   │   ├── product.template.md   # Template documentation produit
│   │   ├── tech.template.md      # Template documentation technique
│   │   └── structure.template.md # Template documentation structure
│   └── specs/                    # Spécifications par fonctionnalité
│       └── [feature-name]/       # Dossier par fonctionnalité
│           ├── requirements.md   # Exigences EARS
│           ├── design.md         # Design technique
│           └── tasks.md          # Plan d'implémentation
├── src/                          # Code source principal
│   ├── [structure-spécifique]    # Organisation selon le type de projet
├── tests/                        # Tests automatisés
├── docs/                         # Documentation utilisateur
├── config/                       # Configuration et environnement
├── scripts/                      # Scripts d'automatisation
└── [autres-dossiers]             # Structure spécifique au projet
```

### Hiérarchie et Navigation
```mermaid
flowchart TD
    Root[Racine Projet] --> SDD[sdd/]
    Root --> Src[src/]
    Root --> Tests[tests/]
    Root --> Docs[docs/]
    
    SDD --> Project[project/]
    SDD --> Templates[templates/]
    SDD --> Specs[specs/]
    
    Project --> Structure[structure.md]
    Project --> Product[product.md]
    Project --> Tech[tech.md]
    Project --> Context[context.md]
    Project --> Guidelines[guidelines.md]
    
    Specs --> Feature1[feature-1/]
    Specs --> Feature2[feature-2/]
    
    Feature1 --> Req1[requirements.md]
    Feature1 --> Des1[design.md]
    Feature1 --> Task1[tasks.md]
```

## Dossier sdd/project/

### Objectif du Dossier
Contient le contexte global du projet qui guide tous les agents IA et développeurs dans leurs décisions d'implémentation et d'architecture.

### Fichiers de Contexte Projet

#### structure.md (ce fichier)
- **Rôle** : Définit l'organisation des dossiers et fichiers
- **Contenu** : Architecture, conventions de nommage, workflows
- **Mise à jour** : À chaque modification structurelle majeure
- **Utilisateurs** : Agents IA, nouveaux développeurs, architectes

#### product.md
- **Rôle** : Vision produit, objectifs stratégiques et contexte métier
- **Contenu** : Personas, métriques de succès, roadmap produit
- **Mise à jour** : Évolution des objectifs business et retours utilisateurs
- **Utilisateurs** : Product managers, agents IA, équipe développement

#### tech.md
- **Rôle** : Stack technique, contraintes et standards de développement
- **Contenu** : Technologies, outils, configurations, bonnes pratiques
- **Mise à jour** : Évolution du stack et des pratiques techniques
- **Utilisateurs** : Développeurs, agents IA, architectes techniques

#### context.md (optionnel)
- **Rôle** : Contexte technique et métier complémentaire
- **Contenu** : Intégrations, contraintes spécifiques, historique
- **Mise à jour** : Changements d'environnement ou de contraintes

#### guidelines.md (optionnel)
- **Rôle** : Directives et standards spécifiques au projet
- **Contenu** : Coding standards, review process, workflows
- **Mise à jour** : Évolution des pratiques et retours d'expérience

### Fichiers Optionnels Avancés

#### dependencies.md
- **Rôle** : Gestion centralisée des dépendances
- **Contenu** : Librairies approuvées, versions, justifications
- **Exemple d'usage** : Projets avec besoins de conformité strict

#### deployment.md
- **Rôle** : Stratégie et configuration de déploiement
- **Contenu** : Environnements, CI/CD, monitoring, rollback
- **Exemple d'usage** : Applications avec déploiement complexe

#### security.md
- **Rôle** : Directives de sécurité spécifiques au projet
- **Contenu** : Authentification, chiffrement, audit, conformité
- **Exemple d'usage** : Applications traitant des données sensibles

## Dossier sdd/specs/

### Organisation par Fonctionnalité

#### Principe d'Organisation
Chaque fonctionnalité majeure dispose de son propre sous-dossier avec la **triade complète SDD** :
- `requirements.md` : Exigences au format EARS (Event-driven Requirements)
- `design.md` : Architecture et design technique détaillé
- `tasks.md` : Plan d'implémentation étape par étape

#### Cycle de Vie d'une Spec
```mermaid
sequenceDiagram
    participant PM as Product Manager
    participant AI as Agent IA
    participant Dev as Développeur
    participant QA as Quality Assurance

    PM->>AI: Idée de fonctionnalité
    AI->>AI: Créer requirements.md
    AI->>PM: Validation requirements
    PM->>AI: Approuvé
    AI->>AI: Créer design.md
    AI->>Dev: Validation design
    Dev->>AI: Approuvé
    AI->>AI: Créer tasks.md
    AI->>Dev: Plan d'implémentation
    Dev->>Dev: Implémentation
    Dev->>QA: Tests basés sur requirements
```

### Conventions de Nommage

#### Dossiers de Fonctionnalités
- **Format** : kebab-case obligatoire
- **Exemples** : `user-authentication`, `payment-processing`, `notification-system`
- **Cohérence** : Même nom utilisé dans toute la documentation et le code

#### Fichiers de Spécification
- **Noms fixes** : `requirements.md`, `design.md`, `tasks.md`
- **Pas de préfixes** : Éviter `01-requirements.md` ou `req-user-auth.md`
- **Cohérence** : Structure identique dans toutes les fonctionnalités

### Exemples de Structure Specs

#### Exemple Simple
```
sdd/specs/
├── user-login/
│   ├── requirements.md      # Exigences authentification
│   ├── design.md           # Architecture auth service
│   └── tasks.md            # Plan implémentation
└── dashboard/
    ├── requirements.md      # Exigences tableau de bord
    ├── design.md           # Design UI/UX
    └── tasks.md            # Tâches de développement
```

#### Exemple Complexe avec Sous-fonctionnalités
```
sdd/specs/
├── e-commerce-platform/
│   ├── requirements.md      # Exigences générales plateforme
│   ├── design.md           # Architecture globale
│   └── tasks.md            # Plan général
├── product-catalog/
│   ├── requirements.md      # Gestion catalogue produits
│   ├── design.md           # Base de données produits
│   └── tasks.md            # Implémentation catalogue
├── shopping-cart/
│   ├── requirements.md      # Fonctionnalités panier
│   ├── design.md           # Logique panier/session
│   └── tasks.md            # Développement panier
└── payment-integration/
    ├── requirements.md      # Intégration paiement
    ├── design.md           # Architecture sécurisée
    └── tasks.md            # Implémentation sécurisée
```

## Dossier sdd/templates/

### Templates pour Agents IA

#### Objectif des Templates
Fournir des modèles structurés et des instructions intégrées pour que les agents IA génèrent des documents de spécification cohérents et complets.

#### Templates de Spécification
- **requirements.template.md** : Guide création requirements.md (format EARS)
- **design.template.md** : Guide création design.md (architecture technique)
- **tasks.template.md** : Guide création tasks.md (plan d'implémentation)
- **task-execution.template.md** : Guide exécution des tâches

#### Templates de Contexte (Nouveaux)
- **product.template.md** : Guide création product.md
- **tech.template.md** : Guide création tech.md
- **structure.template.md** : Guide création structure.md

#### Template Système
- **system-prompt.template.md** : Instructions système pour agents IA

### Utilisation des Templates
```mermaid
flowchart LR
    Template[Template] --> AI[Agent IA]
    AI --> Document[Document généré]
    Document --> Validation[Validation utilisateur]
    Validation --> Revision[Révisions]
    Revision --> Final[Document final]
```

## Organisation du Code Source (src/)

### Structure selon le Type de Projet

#### Application Web (Frontend + Backend)
```
src/
├── frontend/                # Application client
│   ├── components/         # Composants réutilisables
│   ├── pages/             # Pages/Routes principales
│   ├── services/          # Services API
│   ├── utils/             # Utilitaires
│   ├── types/             # Types TypeScript
│   └── config/            # Configuration frontend
├── backend/               # API serveur
│   ├── controllers/       # Contrôleurs API
│   ├── services/          # Logique métier
│   ├── models/            # Modèles de données
│   ├── middleware/        # Middleware Express
│   ├── routes/            # Définition des routes
│   └── config/            # Configuration backend
└── shared/                # Code partagé
    ├── types/             # Types communs
    ├── utils/             # Utilitaires partagés
    └── constants/         # Constantes communes
```

#### Application Mobile (React Native/Flutter)
```
src/
├── screens/               # Écrans de l'application
├── components/            # Composants réutilisables
├── navigation/            # Configuration navigation
├── services/              # Services API et logique
├── stores/                # Gestion d'état (Redux/MobX)
├── utils/                 # Utilitaires
├── types/                 # Définitions de types
├── assets/                # Images, fonts, etc.
└── config/                # Configuration app
```

#### API/Microservice
```
src/
├── controllers/           # Contrôleurs API
├── services/              # Logique métier
├── models/                # Modèles de données
├── middleware/            # Middleware
├── routes/                # Définition routes
├── validators/            # Validation des données
├── utils/                 # Utilitaires
├── types/                 # Types TypeScript
├── config/                # Configuration
└── tests/                 # Tests unitaires
```

### Conventions d'Intégration avec SDD

#### Références aux Spécifications
```typescript
/**
 * Service d'authentification utilisateur
 * @spec sdd/specs/user-authentication/
 * @requirements REQ-AUTH-1, REQ-AUTH-2, REQ-AUTH-3
 * @design AuthService architecture
 */
export class AuthenticationService {
  // Implémentation basée sur design.md
}
```

#### Traçabilité dans les Tests
```typescript
/**
 * Tests pour l'authentification utilisateur
 * @spec sdd/specs/user-authentication/requirements.md
 * @testsCoverage REQ-AUTH-1, REQ-AUTH-2
 */
describe('Authentication Service', () => {
  // Tests basés sur les critères d'acceptation EARS
});
```

## Intégration avec le Code Source

### Liens avec l'Implémentation

#### Traçabilité Bidirectionnelle
- **Specs → Code** : Chaque composant référence sa spécification
- **Code → Specs** : Tests valident les exigences des requirements
- **Documentation** : Docs utilisateur référencent les specs pour détails

#### Workflow de Développement
```mermaid
graph TD
    A[Idée fonctionnalité] --> B[Créer spec dans sdd/specs/]
    B --> C[requirements.md]
    C --> D[design.md]
    D --> E[tasks.md]
    E --> F[Implémentation dans src/]
    F --> G[Tests dans tests/]
    G --> H[Documentation dans docs/]
    H --> I[Validation contre requirements]
```

### Organisation des Tests

#### Structure des Tests
```
tests/
├── unit/                  # Tests unitaires
│   └── [mirror-src-structure]
├── integration/           # Tests d'intégration
│   └── [by-feature]
├── e2e/                   # Tests end-to-end
│   └── [user-journeys]
└── fixtures/              # Données de test
    └── [test-data]
```

#### Correspondance avec Specs
```
tests/integration/user-authentication/
├── login.test.js          # Tests REQ-AUTH-1
├── logout.test.js         # Tests REQ-AUTH-2
└── password-reset.test.js # Tests REQ-AUTH-3
```

## Workflow de Développement

### Cycle de Vie d'une Fonctionnalité

#### Phase 1 : Spécification (SDD)
1. **Création dossier** : Nouveau dossier dans `sdd/specs/[feature-name]/`
2. **Requirements** : Rédaction `requirements.md` avec format EARS
3. **Design** : Création `design.md` basé sur requirements
4. **Planning** : Génération `tasks.md` depuis design
5. **Validation** : Review et approbation des specs

#### Phase 2 : Développement
1. **Setup** : Création structure dans `src/` selon `tasks.md`
2. **Implémentation** : Développement avec traçabilité vers specs
3. **Tests** : Tests unitaires et d'intégration basés sur requirements
4. **Documentation** : Mise à jour docs utilisateur

#### Phase 3 : Livraison
1. **Validation** : Tests E2E et validation requirements
2. **Review** : Code review avec vérification traçabilité
3. **Déploiement** : Déploiement selon stratégie définie
4. **Monitoring** : Suivi métriques définies dans specs

### Maintenance et Évolution

#### Cycle de Maintenance
- **Bug fixes** : Analyse requirements → design pour corrections
- **Évolutions** : Mise à jour des 3 fichiers specs en cascade
- **Refactoring** : Révision design → tasks → implémentation
- **Optimisations** : Amélioration continue basée sur métriques

#### Versioning et Historique
- **Git** : Historique complet des changements specs et code
- **Tagging** : Tags pour versions majeures avec specs
- **Release notes** : Basées sur évolution des requirements

## Conventions et Standards

### Conventions de Nommage Globales

#### Dossiers
- **kebab-case** : `user-authentication`, `payment-processing`
- **Cohérence** : Même nom dans specs, code, tests, docs
- **Descriptif** : Noms explicites évitant les abréviations

#### Fichiers
- **Specs** : Noms fixes (`requirements.md`, `design.md`, `tasks.md`)
- **Code** : Convention selon langage (camelCase, PascalCase)
- **Tests** : Suffixe `.test.js` ou `.spec.js`
- **Config** : Noms explicites (`database.config.js`)

#### Variables et Fonctions
```javascript
// Conventions de code
const API_BASE_URL = 'https://api.example.com';  // UPPER_SNAKE_CASE
const userService = new UserService();           // camelCase
class AuthenticationService {}                   // PascalCase
function authenticateUser() {}                   // camelCase verbe
```

### Standards de Documentation

#### Structure Markdown
```markdown
# Titre Principal (H1)
## Section Majeure (H2)
### Sous-section (H3)
#### Détail (H4)

- Listes à puces pour énumérations
1. Listes numérotées pour séquences
`code inline` pour éléments techniques
```

#### Code Blocks et Exemples
````markdown
```javascript
// Exemple de code avec langage spécifié
function example() {
  return 'formatted code';
}
```
````

#### Diagrammes Mermaid
```mermaid
flowchart TD
    A[Début] --> B[Traitement]
    B --> C[Fin]
```

### Bonnes Pratiques d'Organisation

#### Séparation des Responsabilités
- **Specs** : Une seule préoccupation par dossier fonctionnalité
- **Code** : Séparation claire UI/logique/données
- **Tests** : Tests isolés et indépendants
- **Docs** : Documentation par audience (dev/user/ops)

#### Scalabilité de la Structure
- **Modularité** : Structure extensible sans refactoring majeur
- **Hiérarchie** : Maximum 3-4 niveaux de profondeur
- **Groupement** : Regroupement logique par domaine métier

#### Facilité de Navigation
- **README** : Fichiers README dans dossiers principaux
- **Index** : Fichiers d'index pour navigation rapide
- **Cross-références** : Liens entre documents connexes

## Outils et Automatisation

### Scripts d'Automatisation

#### Scripts de Projet
```json
{
  "scripts": {
    "sdd:create-feature": "node scripts/create-feature.js",
    "sdd:validate": "node scripts/validate-sdd.js",
    "sdd:generate-docs": "node scripts/generate-docs.js",
    "sdd:check-traceability": "node scripts/check-traceability.js"
  }
}
```

#### Script de Création de Fonctionnalité
```javascript
// scripts/create-feature.js
const fs = require('fs');
const path = require('path');

function createFeature(featureName) {
  const specDir = path.join('sdd', 'specs', featureName);
  
  // Créer le dossier
  fs.mkdirSync(specDir, { recursive: true });
  
  // Créer les fichiers depuis templates
  createFromTemplate('requirements.template.md', 
                     path.join(specDir, 'requirements.md'));
  createFromTemplate('design.template.md', 
                     path.join(specDir, 'design.md'));
  createFromTemplate('tasks.template.md', 
                     path.join(specDir, 'tasks.md'));
}
```

### Validation Automatique

#### Script de Validation SDD
```javascript
// scripts/validate-sdd.js
function validateSDD() {
  const requiredFiles = [
    'sdd/project/structure.md',
    'sdd/project/product.md',
    'sdd/project/tech.md'
  ];
  
  // Vérifier présence fichiers requis
  // Vérifier format des specs
  // Vérifier traçabilité code/specs
}
```

### Intégration CI/CD

#### Validation dans Pipeline
```yaml
# .github/workflows/sdd-validation.yml
name: SDD Validation
on: [push, pull_request]

jobs:
  validate-sdd:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
      - run: npm install
      - run: npm run sdd:validate
      - run: npm run sdd:check-traceability
```

## Migration vers SDD

### Pour Projets Existants

#### Audit Initial
1. **Analyse structure** : Évaluer organisation actuelle
2. **Identification fonctionnalités** : Mapper features existantes
3. **Extraction specs** : Reverse engineering vers requirements/design
4. **Validation cohérence** : Vérifier alignement code/besoins

#### Plan de Migration
```mermaid
gantt
    title Migration vers SDD
    dateFormat  YYYY-MM-DD
    section Phase 1
    Audit structure          :a1, 2024-01-01, 1w
    Setup SDD               :a2, after a1, 1w
    section Phase 2
    Extract core features   :b1, after a2, 2w
    Create specs           :b2, after b1, 2w
    section Phase 3
    Validate alignment     :c1, after b2, 1w
    Train team            :c2, after c1, 1w
```

#### Stratégies par Type de Projet
- **Petits projets** : Migration complète en une fois
- **Projets moyens** : Migration par modules/fonctionnalités
- **Grands projets** : Migration progressive avec priorisation

### Pour Nouveaux Projets

#### Setup Initial
1. **Création structure** : Initialiser structure SDD complète
2. **Context setup** : Rédaction fichiers `sdd/project/`
3. **Templates** : Personnalisation templates si nécessaire
4. **Formation** : Onboarding équipe sur processus SDD

#### Premier Développement
```mermaid
sequenceDiagram
    participant PM as Product Manager
    participant AI as Agent IA
    participant Dev as Développeur

    PM->>AI: Brief initial projet
    AI->>AI: Créer product.md + tech.md + structure.md
    AI->>PM: Validation contexte projet
    PM->>AI: Première fonctionnalité
    AI->>AI: Créer première spec complète
    Dev->>Dev: Implémentation selon spec
```

## Évolution et Maintenance de la Structure

### Signaux de Réorganisation Nécessaire
- **Dossiers > 10 fichiers** : Besoin de sous-organisation
- **Navigation difficile** : Hiérarchie trop profonde/plate
- **Doublons** : Code/docs dupliqués dans plusieurs endroits
- **Conflits fréquents** : Structure cause des conflits Git

### Stratégies d'Évolution
- **Refactoring incrémental** : Petites améliorations continues
- **Migration planifiée** : Changements structurels majeurs planifiés
- **Versioning structure** : Documentation des changements structurels

### Métriques de Santé Structurelle
- **Time to find** : Temps pour localiser un élément
- **Onboarding time** : Temps formation nouveaux développeurs
- **Maintenance overhead** : Effort maintenance structure vs. code

---

## Instructions de Validation pour l'Agent

### Questions de Validation Suggérées
1. "La structure proposée est-elle adaptée au type et à la taille du projet ?"
2. "Les conventions de nommage sont-elles claires et cohérentes ?"
3. "L'organisation SDD est-elle bien intégrée et compréhensible ?"
4. "Les workflows de développement sont-ils pratiques et efficaces ?"
5. "La traçabilité entre spécifications et implémentation est-elle claire ?"
6. "La structure est-elle scalable pour l'évolution du projet ?"

### Format de Validation
```
**Validation Documentation Structure :**
J'ai terminé le document structure.md avec :
- Architecture des dossiers complète et justifiée
- Organisation SDD intégrée (project/, templates/, specs/)
- Conventions de nommage cohérentes
- Workflow de développement détaillé
- Intégration code source et traçabilité
- Stratégies de migration et évolution
- Outils d'automatisation et validation

**Cette documentation de structure vous convient-elle ? Elle servira de référence pour l'organisation de tout le projet.**
```

### Cycle de Révision
1. Présenter la documentation de structure complète
2. Demander validation explicite avec l'outil approprié
3. Si modifications demandées : réviser les sections concernées
4. Répéter jusqu'à approbation explicite
5. Utiliser comme référence pour l'organisation du projet et le développement
