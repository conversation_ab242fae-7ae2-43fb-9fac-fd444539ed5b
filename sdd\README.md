# Système SDD (Spec-Driven Development)

## Vue d'ensemble

Le système SDD (Spec-Driven Development) est une méthodologie de développement dirigée par les spécifications qui transforme les idées utilisateur en logiciels fonctionnels à travers un processus structuré et validé.

## Architecture du Système

```
sdd/
├── project/                    # Contexte et configuration projet
│   ├── product.md             # Vision produit et objectifs
│   ├── structure.md           # Architecture et organisation
│   └── tech.md               # Stack technique et contraintes
├── templates/                  # Templates pour génération documents
│   ├── requirements.template.md    # Template exigences EARS
│   ├── design.template.md         # Template architecture technique
│   ├── tasks.template.md          # Template plan implémentation
│   ├── task-execution.template.md # Guide exécution tâches
│   └── system-prompt.template.md  # Prompt système principal
├── specs/                      # Spécifications par fonctionnalité
│   └── {feature_name}/
│       ├── requirements.md    # Exigences validées (format EARS)
│       ├── design.md         # Architecture technique validée
│       └── tasks.md          # Plan implémentation validé
└── README.md                  # Ce fichier
```

## Processus SDD en 4 Phases

### Phase 1 : Requirements (Exigences)
**Objectif :** Transformer l'idée utilisateur en document d'exigences structuré

- **Input :** Idée ou demande utilisateur
- **Process :** Analyse et structuration au format EARS
- **Output :** `requirements.md` validé
- **Format :** QUAND/SI... ALORS... DOIT

### Phase 2 : Design (Conception)
**Objectif :** Transformer les requirements en architecture technique

- **Input :** `requirements.md` validé
- **Process :** Recherche, architecture, justifications techniques
- **Output :** `design.md` validé
- **Inclut :** Architecture, composants, sécurité, performance

### Phase 3 : Tasks (Tâches)
**Objectif :** Transformer le design en plan d'implémentation

- **Input :** `design.md` validé
- **Process :** Décomposition en tâches atomiques orientées test
- **Output :** `tasks.md` validé
- **Format :** Liste numérotée, progression incrémentale

### Phase 4 : Execution (Exécution)
**Objectif :** Implémenter les tâches selon les spécifications

- **Input :** `tasks.md` validé + specs complètes
- **Process :** Exécution méthodique tâche par tâche
- **Output :** Code fonctionnel conforme aux specs

## Utilisation du Système

### Pour les Agents IA

1. **Charger le prompt système :**
   ```
   Utilisez le contenu de sdd/templates/system-prompt.template.md
   comme instructions système principales.
   ```

2. **Consulter le contexte projet :**
   - Lire `sdd/project/product.md` pour la vision
   - Lire `sdd/project/structure.md` pour l'architecture
   - Lire `sdd/project/tech.md` pour les contraintes techniques

3. **Suivre le processus SDD :**
   - Identifier la phase appropriée selon la demande
   - Utiliser les templates correspondants
   - Valider chaque étape avec l'utilisateur
   - Maintenir la traçabilité requirements → design → tasks → code

### Pour les Développeurs

1. **Nouvelle fonctionnalité :**
   ```bash
   # L'agent créera automatiquement :
   sdd/specs/ma-nouvelle-feature/
   ├── requirements.md
   ├── design.md
   └── tasks.md
   ```

2. **Exécution des tâches :**
   - Ouvrir `tasks.md` de la fonctionnalité
   - Demander à l'agent d'exécuter une tâche spécifique
   - Valider chaque implémentation
   - Progresser tâche par tâche

## Règles Fondamentales

### Validation Obligatoire
- ✅ Chaque phase doit être validée explicitement par l'utilisateur
- ✅ Aucune progression sans approbation
- ✅ Cycle feedback-révision jusqu'à satisfaction

### Traçabilité
- ✅ Requirements → Design → Tasks → Code
- ✅ Références croisées maintenues
- ✅ Justifications documentées

### Exécution Contrôlée
- ✅ Une tâche à la fois
- ✅ Lecture obligatoire des specs avant exécution
- ✅ Arrêt après chaque tâche
- ✅ Pas de continuation automatique

## Format EARS (Easy Approach to Requirements Syntax)

Le système utilise le format EARS pour structurer les exigences :

```
QUAND [événement déclencheur] ALORS [système] DOIT [réponse système]
SI [précondition] ALORS [système] DOIT [comportement attendu]
QUAND [événement] ET [condition] ALORS [système] DOIT [réponse]
```

**Exemple :**
```
QUAND un utilisateur clique sur "Se connecter" 
ALORS le système DOIT afficher le formulaire de connexion

SI les identifiants sont invalides 
ALORS le système DOIT afficher un message d'erreur
```

## Templates Disponibles

### requirements.template.md
- Structure complète pour documenter les exigences
- Format EARS intégré
- Sections : fonctionnelles, non-fonctionnelles, contraintes
- Instructions pour l'agent IA

### design.template.md
- Architecture technique complète
- Composants, interfaces, modèles de données
- Sécurité, performance, gestion d'erreurs
- Plan d'implémentation

### tasks.template.md
- Plan d'implémentation structuré
- Tâches atomiques orientées test
- Progression incrémentale
- Références aux requirements et design

### task-execution.template.md
- Guide d'exécution pour les agents
- Protocoles de validation
- Gestion des cas spéciaux
- Templates de communication

### system-prompt.template.md
- Instructions système complètes
- Processus SDD intégré
- Règles et contraintes
- Adaptation contextuelle

## Avantages du Système SDD

### Pour les Projets
- **Qualité** : Spécifications complètes et validées
- **Traçabilité** : Lien direct idée → code
- **Maintenance** : Documentation à jour
- **Évolutivité** : Architecture pensée

### Pour les Équipes
- **Clarté** : Objectifs et contraintes explicites
- **Collaboration** : Validation continue
- **Efficacité** : Moins de refactoring
- **Apprentissage** : Processus reproductible

### Pour les Agents IA
- **Guidage** : Instructions précises
- **Contexte** : Information complète
- **Validation** : Critères objectifs
- **Cohérence** : Approche standardisée

## Exemples d'Usage

### Nouvelle Fonctionnalité
```
Utilisateur : "Je veux ajouter un système d'authentification"

Agent :
1. Phase Requirements → Crée requirements.md avec format EARS
2. Validation utilisateur
3. Phase Design → Crée design.md avec architecture
4. Validation utilisateur  
5. Phase Tasks → Crée tasks.md avec plan implémentation
6. Validation utilisateur
7. Phase Execution → Exécute tâche par tâche
```

### Exécution de Tâche
```
Utilisateur : "Exécute la tâche 2.1 de l'authentification"

Agent :
1. Lit requirements.md, design.md, tasks.md
2. Identifie la tâche 2.1
3. Implémente selon les spécifications
4. Valide contre les critères
5. S'arrête et demande validation
```

### Question sur Progression
```
Utilisateur : "Où en est l'authentification ?"

Agent :
1. Consulte tasks.md
2. Identifie les tâches complétées
3. Suggère la prochaine tâche
4. Fournit l'information sans exécuter
```

## Maintenance et Évolution

### Mise à Jour des Templates
- Modifier les fichiers dans `sdd/templates/`
- Tester avec de nouvelles fonctionnalités
- Documenter les changements

### Adaptation Projet
- Modifier `sdd/project/` selon les besoins
- Ajuster les contraintes techniques
- Personnaliser les processus

### Extension du Système
- Ajouter de nouveaux templates
- Créer des spécialisations par domaine
- Intégrer des outils externes

## Support et Documentation

- **Spécification complète :** `Spec-Standard.md`
- **Templates :** `sdd/templates/`
- **Exemples :** `sdd/specs/` (après utilisation)
- **Configuration :** `sdd/project/`

---

**Le système SDD transforme le développement logiciel en un processus prévisible, documenté et de haute qualité.**