# Spec-Memory : Système d'Instructions Hybride SDD (SpecDrivenDevelopment)

## Vue d'Ensemble

Ce document présente plusieurs propositions pour créer un système d'instructions d'agent IA hybride qui combine les concepts de **persistance de contexte** et **SpecDrivenDevelopment** (workflow structuré de spécification) tout en optimisant pour réduire le nombre de requêtes coûteuses.

### Problématique

- Les agents IA sont facturés par requête
- Il faut maximiser la production de documents .md en une seule session
- Besoin de validation utilisateur sans déclencher de nouvelles requêtes
- Maintenir la cohérence et la qualité du workflow de spécification

### Solution Proposée

Utilisation du serveur MCP interactif pour permettre à l'agent de poser des questions et obtenir des validations via des appels d'outils MCP sans stopper l'exécution ni déclencher de nouvelles requêtes.

### Deux Variantes d'Architecture

1. **Version Standard** : Structure de dossiers `sdd/` avec `project/` et `specs/`
2. **Version GitHub Copilot** : Intégration native avec `.github/instructions/` et `.github/chatmodes/`

---

## Templates de Documents

### Structure des Templates

La structure SDD utilise des templates standardisés basés sur les fichiers `prompt-spec-*.md` existants pour garantir la cohérence et la qualité des documents générés :

#### Templates Contexte Projet
- **product.template.md** : Basé sur `steering/product.md`
  - Vision et objectifs du produit
  - Public cible et personas
  - Proposition de valeur unique
  - Métriques de succès

- **structure.template.md** : Basé sur `steering/structure.md`
  - Architecture générale du projet
  - Organisation des modules
  - Conventions de nommage
  - Structure des dossiers

- **tech.template.md** : Basé sur `steering/tech.md`
  - Stack technologique
  - Contraintes techniques
  - Standards de développement
  - Outils et frameworks

#### Templates Spécifications avec Instructions Précises

##### **requirements.template.md**
Basé sur `prompt-spec-requirements-example.md` et `prompt-spec-workflow-requirement-clarification.md`

**Instructions intégrées :**
- Génération initiale basée sur l'idée utilisateur SANS questions séquentielles
- Format EARS (Easy Approach to Requirements Syntax) obligatoire
- Structure hiérarchique numérotée avec :
  - User stories : "En tant que [rôle], je veux [fonctionnalité], afin que [bénéfice]"
  - Critères d'acceptation au format EARS
- Prise en compte des cas limites, UX, contraintes techniques
- Cycle feedback-révision jusqu'à approbation explicite
- Validation obligatoire avant passage au design

##### **design.template.md**
Basé sur `prompt-spec-workflow-design-template.md` et `prompt-spec-workflow-research-design.md`

**Instructions intégrées :**
- Recherche contextuelle intégrée (pas de fichiers séparés)
- Sections obligatoires du template design
- Diagrammes Mermaid si approprié
- Justification des décisions de design
- Réponse à toutes les exigences identifiées
- Cycle feedback-révision jusqu'à approbation explicite
- Validation obligatoire avant passage aux tâches

##### **tasks.template.md**
Basé sur `prompt-spec-workflow-example-plan.md` et `prompt-spec-workflow-implementation-plan.md`

**Instructions intégrées :**
- Conversion du design en prompts LLM orientés test
- Format liste numérotée à cocher (max 2 niveaux)
- Notation décimale pour sous-tâches (1.1, 1.2, 2.1)
- Chaque tâche doit inclure :
  - Objectif clair (écriture/modification/test de code)
  - Informations supplémentaires en sous-puces
  - Références spécifiques aux exigences
- Progression incrémentale sans code orphelin
- Une tâche à la fois, arrêt pour révision utilisateur

#### Instructions Workflow Complètes

**Basées sur `prompt-spec-workflow-overview.md` :**
- Méthodologie développement piloté par spécifications
- Validation utilisateur obligatoire à chaque étape
- Nom de fonctionnalité en kebab-case
- Processus itératif requirements → design → tasks

**Dépannage (`prompt-spec-workflow-troubleshooting.md`) :**
- Gestion des blocages lors de clarification
- Approches alternatives si informations manquantes
- Découpage en composants si complexité excessive

**Exécution des tâches (`prompt-spec-task-background.md`) :**
- Lecture obligatoire requirements.md, design.md, tasks.md
- Une tâche à la fois, arrêt pour révision
- Vérification contre toutes les exigences spécifiées

### Utilisation des Templates

**Version Standard :**
```
Agent: "Je vais créer requirements.md en utilisant le template sdd/templates/requirements.template.md"
```

**Version GitHub Copilot :**
```
Agent: "Je vais créer requirements.md en utilisant le template .github/templates/requirements.template.md"
```

---

## Proposition 1 : Architecture "Session Unifiée avec Boucle de Validation Continue"

### Principe Fondamental

Une seule "macro-tâche" lancée à l'agent IA qui suit le processus SpecDrivenDevelopment de bout en bout, utilisant la structure SDD comme mémoire de travail et le serveur MCP comme oracle pour les validations.

### Version Standard : Structure SDD

```
sdd/
├── project/                 # Contexte global du projet
│   ├── product.md          # Vision produit et objectifs
│   ├── structure.md        # Architecture et organisation
│   └── tech.md             # Stack technique et outils
├── templates/              # Templates pour tous les documents
│   ├── product.template.md    # Template basé sur steering/product.md
│   ├── structure.template.md  # Template basé sur steering/structure.md
│   ├── tech.template.md       # Template basé sur steering/tech.md
│   ├── requirements.template.md # Template EARS (prompt-spec-requirements-example.md)
│   ├── design.template.md     # Template conception (prompt-spec-workflow-design-template.md)
│   └── tasks.template.md      # Template plan d'implémentation (prompt-spec-workflow-example-plan.md)
└── specs/                  # Spécifications par fonctionnalité
    └── {feature_name}/
        ├── requirements.md # Exigences EARS
        ├── design.md      # Design détaillé
        └── tasks.md       # Plan d'implémentation
```

### Version GitHub Copilot : Intégration Native

```
.github/
├── instructions/           # Contexte global (équivalent project/)
│   ├── product.instructions.md     # Vision produit
│   ├── structure.instructions.md   # Architecture
│   └── tech.instructions.md        # Stack technique
├── chatmodes/             # Workflows SDD spécialisés
│   ├── spec-requirements.chatmode.md
│   ├── spec-design.chatmode.md
│   └── spec-implementation.chatmode.md
├── templates/             # Templates pour GitHub Copilot
│   ├── requirements.template.md # Template EARS (prompt-spec-requirements-example.md)
│   ├── design.template.md     # Template conception (prompt-spec-workflow-design-template.md)
│   └── tasks.template.md      # Template plan d'implémentation (prompt-spec-workflow-example-plan.md)
specs/                     # Spécifications (racine du projet)
└── {feature_name}/
    ├── requirements.md
    ├── design.md
    └── tasks.md
```

### Architecture Technique

```mermaid
flowchart TD
    A[Utilisateur : Idée de fonctionnalité] --> B[Agent : Chargement contexte SDD complet]
    B --> C[Phase 1: Clarification des exigences]
    C --> D{Besoin de validation?}
    D -->|Oui| E[MCP: mcp_ask_for_validation]
    E --> F[Utilisateur répond via MCP]
    F --> G[Agent reçoit réponse et continue]
    G --> C
    D -->|Non| H[Phase 2: Recherche & Design]
    H --> I{Besoin de validation?}
    I -->|Oui| E
    I -->|Non| J[Phase 3: Plan d'implémentation]
    J --> K{Besoin de validation?}
    K -->|Oui| E
    K -->|Non| L[Génération documents .md finaux]
    L --> M[Session terminée - Tous documents produits]
```

### Composants Clés

#### 1. Prompt Système Hybride

**Version Standard :**

```markdown
# INSTRUCTIONS SYSTÈME HYBRIDE SDD

Tu es un ingénieur senior spécialisé dans la transformation d'idées en spécifications détaillées.

## RÔLE ET MISSION
- Transformer une idée de fonctionnalité en plan d'implémentation complet
- Suivre rigoureusement les 3 phases du SpecDrivenDevelopment
- Utiliser la structure SDD comme source de vérité et mémoire de travail
- Maximiser la production de documents .md en une seule session

## PROCESSUS OBLIGATOIRE (basé sur prompt-spec-workflow-overview.md)
1. **Nom de fonctionnalité** : Format kebab-case obligatoire
2. **Phase 1 - Requirements** : Créer requirements.md avec méthodologie EARS
3. **Phase 2 - Design** : Créer design.md avec recherche intégrée
4. **Phase 3 - Tasks** : Créer tasks.md avec prompts LLM orientés test

### Phase 1: Requirements (prompt-spec-workflow-requirement-clarification.md)
- Génération initiale basée sur l'idée utilisateur SANS questions séquentielles
- Format EARS (Easy Approach to Requirements Syntax) obligatoire
- Structure hiérarchique numérotée avec :
  - User stories : "En tant que [rôle], je veux [fonctionnalité], afin que [bénéfice]"
  - Critères d'acceptation au format EARS
- Prise en compte des cas limites, UX, contraintes techniques
- Cycle feedback-révision jusqu'à approbation explicite
- Validation obligatoire avant passage au design

### Phase 2: Design (prompt-spec-workflow-research-design.md)
- Recherche contextuelle intégrée (pas de fichiers séparés)
- Sections obligatoires du template design
- Diagrammes Mermaid si approprié
- Justification des décisions de design
- Réponse à toutes les exigences identifiées
- Cycle feedback-révision jusqu'à approbation explicite
- Validation obligatoire avant passage aux tâches

### Phase 3: Tasks (prompt-spec-workflow-implementation-plan.md)
- Conversion du design en prompts LLM orientés test
- Format liste numérotée à cocher (max 2 niveaux)
- Notation décimale pour sous-tâches (1.1, 1.2, 2.1)
- Chaque tâche doit inclure :
  - Objectif clair (écriture/modification/test de code)
  - Informations supplémentaires en sous-puces
  - Références spécifiques aux exigences
- Progression incrémentale sans code orphelin
- Une tâche à la fois, arrêt pour révision utilisateur

## CONTEXTE PROJET (sdd/project/)
Le contexte projet fourni est ta source de vérité. Tu DOIS :
- Lire product.md, structure.md, tech.md au début
- Respecter les contraintes et conventions définies
- Maintenir la cohérence avec l'architecture existante

## EXÉCUTION DES TÂCHES (prompt-spec-task-background.md)
- Lecture obligatoire requirements.md, design.md, tasks.md
- Une tâche à la fois, arrêt pour révision
- Vérification contre toutes les exigences spécifiées

## DÉPANNAGE (prompt-spec-workflow-troubleshooting.md)
- Gestion des blocages lors de clarification
- Approches alternatives si informations manquantes
- Découpage en composants si complexité excessive

## VALIDATION CONTINUE (MCP)
Lorsque tu as besoin d'une clarification ou validation :
- N'ARRÊTE PAS ta réflexion
- Utilise les outils MCP interactive disponibles :
  - `request_user_input` pour questions simples
  - `start_intensive_chat` + `ask_intensive_chat` pour sessions approfondies
  - `message_complete_notification` pour notifier les completions
- Attends la réponse et utilise-la pour continuer
- Continue jusqu'à completion complète des 3 phases

## RÈGLES IMPORTANTES
- Validation utilisateur obligatoire à chaque étape
- Ne mentionnez pas explicitement le workflow à l'utilisateur
- Informez simplement quand vous terminez un document
- Processus itératif avec retours possibles aux étapes précédentes

## SORTIE ATTENDUE
À la fin de la session, tu DOIS fournir :
- Les fichiers de spécification dans sdd/specs/{feature_name}/
- Un résumé des décisions prises
```

**Version GitHub Copilot :**

```markdown
# INSTRUCTIONS CHATMODE SDD

Tu es un assistant expert en SpecDrivenDevelopment intégré à GitHub Copilot.

## RÔLE
Transformer une idée de fonctionnalité en spécification complète suivant la méthodologie SDD.

## PROCESSUS OBLIGATOIRE (basé sur prompt-spec-workflow-overview.md)
1. **Analyse du contexte** : Utilise @workspace pour lire les instructions globales
2. **Nom de fonctionnalité** : Format kebab-case obligatoire
3. **Phase Requirements** : Créer requirements.md avec méthodologie EARS
4. **Phase Design** : Créer design.md avec recherche intégrée
5. **Phase Tasks** : Créer tasks.md avec prompts LLM orientés test

### Phase 1: Requirements (prompt-spec-workflow-requirement-clarification.md)
- Génération initiale basée sur l'idée utilisateur SANS questions séquentielles
- Format EARS (Easy Approach to Requirements Syntax) obligatoire
- Structure hiérarchique numérotée avec :
  - User stories : "En tant que [rôle], je veux [fonctionnalité], afin que [bénéfice]"
  - Critères d'acceptation au format EARS
- Prise en compte des cas limites, UX, contraintes techniques
- Cycle feedback-révision jusqu'à approbation explicite
- Validation obligatoire avant passage au design

### Phase 2: Design (prompt-spec-workflow-research-design.md)
- Recherche contextuelle intégrée (pas de fichiers séparés)
- Sections obligatoires du template design
- Diagrammes Mermaid si approprié
- Justification des décisions de design
- Réponse à toutes les exigences identifiées
- Cycle feedback-révision jusqu'à approbation explicite
- Validation obligatoire avant passage aux tâches

### Phase 3: Tasks (prompt-spec-workflow-implementation-plan.md)
- Conversion du design en prompts LLM orientés test
- Format liste numérotée à cocher (max 2 niveaux)
- Notation décimale pour sous-tâches (1.1, 1.2, 2.1)
- Chaque tâche doit inclure :
  - Objectif clair (écriture/modification/test de code)
  - Informations supplémentaires en sous-puces
  - Références spécifiques aux exigences
- Progression incrémentale sans code orphelin
- Une tâche à la fois, arrêt pour révision utilisateur

## EXÉCUTION DES TÂCHES (prompt-spec-task-background.md)
- Lecture obligatoire requirements.md, design.md, tasks.md
- Une tâche à la fois, arrêt pour révision
- Vérification contre toutes les exigences spécifiées

## DÉPANNAGE (prompt-spec-workflow-troubleshooting.md)
- Gestion des blocages lors de clarification
- Approches alternatives si informations manquantes
- Découpage en composants si complexité excessive

## VALIDATION CONTINUE (MCP)
Lorsque tu as besoin d'une clarification ou validation :
- Utilise les outils MCP interactive disponibles :
  - `request_user_input` pour questions simples
  - `start_intensive_chat` + `ask_intensive_chat` pour sessions approfondies
  - `message_complete_notification` pour notifier les completions
- Attends la réponse et utilise-la pour continuer

## RÈGLES IMPORTANTES
- Validation utilisateur obligatoire à chaque étape
- Ne mentionnez pas explicitement le workflow à l'utilisateur
- Informez simplement quand vous terminez un document
- Processus itératif avec retours possibles aux étapes précédentes

## CONTRAINTES
- Respecter les conventions définies dans les instructions globales
- Utiliser la stack technique spécifiée
- Maintenir la cohérence avec l'architecture existante
- Générer tous les fichiers dans specs/{feature_name}/
```

#### 2. Outil MCP de Validation

```typescript
interface McpValidationTool {
  request_user_input(args: {
    project_name: string;          // Nom du projet
    message: string;               // Question claire et concise
    predefined_options?: string[]; // Choix multiples optionnels
  }): Promise<string>;             // Réponse utilisateur
  
  start_intensive_chat(args: {
    session_title: string;         // Titre de la session
  }): Promise<string>;             // ID de session
  
  ask_intensive_chat(args: {
    session_id: string;            // ID de session
    question: string;              // Question dans la session
    options?: string[];            // Choix multiples optionnels
  }): Promise<string>;             // Réponse utilisateur
  
  stop_intensive_chat(args: {
    session_id: string;            // ID de session à fermer
  }): Promise<boolean>;            // Confirmation fermeture
  
  message_complete_notification(args: {
    project_name: string;          // Nom du projet
    message: string;               // Message de notification
  }): Promise<boolean>;            // Confirmation envoi
}
```

#### 3. Structure SDD Complète

**Version Standard :**

```
sdd/
├── project/                  # Contexte global du projet
│   ├── product.md           # Vision produit, objectifs, composants
│   ├── structure.md         # Architecture, organisation, conventions
│   └── tech.md              # Stack technique, outils, commandes
└── specs/                   # Spécifications par fonctionnalité
    └── {feature_name}/
        ├── requirements.md  # Exigences EARS
        ├── design.md       # Design détaillé
        └── tasks.md        # Plan d'implémentation
```

**Version GitHub Copilot :**

```
.github/
├── instructions/            # Contexte global (auto-chargé)
│   ├── product.instructions.md     # Vision et objectifs
│   ├── structure.instructions.md   # Architecture et conventions
│   └── tech.instructions.md        # Stack technique
├── chatmodes/              # Workflows SDD spécialisés
│   ├── sdd-full.chatmode.md        # Workflow complet
│   ├── sdd-requirements.chatmode.md # Phase exigences
│   ├── sdd-design.chatmode.md      # Phase design
│   └── sdd-tasks.chatmode.md       # Phase implémentation
specs/                      # Spécifications (racine projet)
└── {feature_name}/
    ├── requirements.md
    ├── design.md
    └── tasks.md
```

---

## Proposition 2 : Architecture "Pipeline Intelligent avec Checkpoints"

### Principe

Diviser le processus en micro-sessions avec des checkpoints automatiques, permettant une reprise intelligente en cas d'interruption.

### Fonctionnement

1. **Initialisation** : L'agent analyse le MemoryBank et détermine l'état actuel
2. **Planification** : Création d'un plan de session avec checkpoints
3. **Exécution par blocs** : Traitement par phases avec validation MCP
4. **Sauvegarde continue** : Mise à jour du MemoryBank à chaque checkpoint
5. **Reprise intelligente** : Capacité à reprendre depuis le dernier checkpoint

### Avantages

- Résilience aux interruptions
- Progression visible pour l'utilisateur
- Optimisation fine du contexte
- Gestion intelligente de la taille du contexte

---

## Proposition 3 : Architecture "Agent Orchestrateur Multi-Spécialisé"

### Principe

Un agent orchestrateur qui coordonne des agents spécialisés pour chaque phase, tous partageant le même MemoryBank.

### Composants

1. **Agent Orchestrateur** : Gère le workflow global et les transitions
2. **Agent Requirements** : Spécialisé dans la clarification des exigences
3. **Agent Designer** : Expert en architecture et design
4. **Agent Planner** : Spécialisé dans la planification d'implémentation

### Workflow

```mermaid
sequenceDiagram
    participant U as Utilisateur
    participant O as Orchestrateur
    participant R as Agent Requirements
    participant D as Agent Designer
    participant P as Agent Planner
    participant S as Structure SDD
    
    U->>O: Idée de fonctionnalité
    O->>S: Chargement contexte projet
    O->>R: Phase requirements
    R->>U: Validation via MCP
    R->>S: Création requirements.md
    O->>D: Phase design
    D->>U: Validation via MCP
    D->>S: Création design.md
    O->>P: Phase planning
    P->>U: Validation via MCP
    P->>S: Création tasks.md
    O->>U: Livraison documents finaux
```

---

## Proposition 4 : Architecture "Session Continue avec IA Conversationnelle"

### Principe

Utiliser les capacités conversationnelles avancées pour maintenir un dialogue continu avec validation en temps réel.

### Caractéristiques

- **Dialogue naturel** : L'agent pose des questions de clarification au fur et à mesure
- **Validation incrémentale** : Chaque section est validée avant de passer à la suivante
- **Adaptation dynamique** : Le workflow s'adapte selon les réponses utilisateur
- **Mémoire contextuelle** : Utilisation optimale du MemoryBank pour le contexte

### Exemple de Flux

**Version Standard :**

```
Agent: "J'ai analysé le contexte projet (sdd/project/). Basé sur votre stack technique 
et votre idée d'authentification, dois-je inclure l'authentification sociale ?"

Utilisateur (via MCP): "Oui, Google et GitHub"

Agent: "Parfait. Je crée requirements.md dans sdd/specs/auth/... 
Pour la gestion des sessions, préférez-vous JWT ou sessions serveur ?"

Utilisateur (via MCP): "JWT"

Agent: "Excellent. Je finalise les exigences et passe au design..."
```

**Version GitHub Copilot :**

```
Utilisateur: @workspace /sdd-full "Créer une fonctionnalité d'authentification"

Agent: "J'ai analysé les instructions globales. Basé sur votre stack technique, 
dois-je inclure l'authentification sociale ?"

Utilisateur: "Oui, Google et GitHub"

Agent: "Parfait. Je crée les fichiers dans specs/auth/..."
```

---

## Implémentation Recommandée

### Phase 1 : Prototype Minimal (Proposition 1)

1. **Créer le prompt système hybride**
2. **Implémenter l'outil MCP mcp_ask_for_validation**
3. **Tester avec un cas simple**
4. **Itérer sur la qualité des questions/validations**

### Phase 2 : Optimisation

1. **Ajouter la gestion intelligente du contexte**
2. **Implémenter les checkpoints (Proposition 2)**
3. **Optimiser les prompts selon les retours**

### Phase 3 : Évolution

1. **Évaluer l'approche multi-agents (Proposition 3)**
2. **Intégrer les capacités conversationnelles avancées (Proposition 4)**
3. **Automatiser la gestion du MemoryBank**

---

## Outils MCP Requis

### Serveur MCP Interactive

Le système utilise uniquement le serveur MCP `interactive` avec les outils suivants :

#### 1. Question unique

```python
# Utilisation : request_user_input
def ask_validation(
    project_name: str,
    message: str,
    predefined_options: List[str] = None
) -> str:
    """
    Pose une question simple à l'utilisateur pour validation.
    Utilisé pour les validations ponctuelles pendant le workflow SDD.
    """
    pass
```

#### 2. Conversation approfondie

```python
# Utilisation : start_intensive_chat, ask_intensive_chat, stop_intensive_chat
def intensive_validation_session(
    session_title: str,
    questions: List[dict]
) -> List[str]:
    """
    Initie une session de validation approfondie pour les phases complexes.
    Utilisé pour la clarification des exigences et la validation du design.
    """
    pass
```

#### 3. Notification de fin

```python
# Utilisation : message_complete_notification
def notify_completion(
    project_name: str,
    message: str
) -> bool:
    """
    Notifie l'utilisateur de la fin d'une phase ou d'une tâche.
    Utilisé pour signaler la completion des documents SDD.
    """
    pass
```

---

## Métriques de Succès

### Efficacité
- **Réduction des requêtes** : Objectif 80% de réduction (de 5-10 requêtes à 1-2)
- **Temps de session** : Maintenir < 30 minutes par fonctionnalité complète
- **Qualité des documents** : Validation par checklist qualité automatisée

### Expérience Utilisateur
- **Fluidité du dialogue** : Temps de réponse MCP Interactive < 2 secondes
- **Pertinence des questions** : Taux de questions utiles > 90%
- **Complétude des livrables** : 100% des documents requis générés
- **Sessions approfondies** : Validation complexe via intensive_chat

### Technique
- **Utilisation du contexte** : Optimisation pour rester < 80% de la limite
- **Cohérence des documents** : Validation croisée automatique
- **Traçabilité** : Historique complet des décisions dans la structure SDD
- **Intégration GitHub** : Support natif des chatmodes et instructions

---

## Conclusion

Le système Spec-Memory proposé révolutionne l'approche de la spécification assistée par IA en combinant :

1. **La persistance intelligente** via la structure SDD
2. **La rigueur méthodologique** du SpecDrivenDevelopment  
3. **L'optimisation économique** via les outils MCP
4. **La validation continue** sans interruption de session
5. **L'intégration native** avec GitHub Copilot

### Deux Approches Complémentaires

**Version Standard (sdd/)** :
- Structure de dossiers claire et portable
- Contexte projet centralisé (product.md, structure.md, tech.md)
- Spécifications organisées par fonctionnalité
- Compatible avec tous les agents IA

**Version GitHub Copilot (.github/)** :
- Intégration native avec l'écosystème GitHub
- Instructions auto-chargées dans le contexte
- Chatmodes spécialisés pour chaque phase SDD
- Optimisé pour les workflows de développement

### Recommandation d'Implémentation

1. **Démarrer** avec la Version Standard pour valider les concepts
2. **Migrer** vers la Version GitHub Copilot pour les projets en production
3. **Combiner** les deux approches selon les besoins de l'équipe

Cette approche transforme l'agent IA d'un simple exécutant en un véritable partenaire de conception, capable de mener un projet de spécification de bout en bout de manière autonome, économique et intégrée aux outils de développement modernes.