#!/usr/bin/env node

/**
 * Script de Validation de Cohérence SDD
 * Vérifie la cohérence entre les fichiers .github/ et les templates SDD
 */

const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

// Configuration des chemins
const GITHUB_DIR = '.github';
const SDD_TEMPLATES_DIR = 'sdd/templates';
const CONTEXT_FILES = [
    'instructions/context.instructions.md',
    'prompts/context.prompt.md',
    'chatmodes/context.chatmode.md'
];
const SPEC_FILES = [
    'instructions/spec.instructions.md',
    'prompts/spec.prompt.md', 
    'chatmodes/spec.chatmode.md'
];
const ALL_FILES = [...CONTEXT_FILES, ...SPEC_FILES];
const TEMPLATE_FILES = [
    'requirements.template.md',
    'design.template.md',
    'tasks.template.md'
];

class SDDValidator {
    constructor() {
        this.errors = [];
        this.warnings = [];
        this.checksums = new Map();
    }

    // Calcul de checksum pour un fichier
    calculateChecksum(filePath) {
        try {
            const content = fs.readFileSync(filePath, 'utf8');
            return crypto.createHash('md5').update(content).digest('hex');
        } catch (error) {
            this.errors.push(`Impossible de lire ${filePath}: ${error.message}`);
            return null;
        }
    }

    // Vérification de l'existence des fichiers
    validateFileExists(filePath) {
        if (!fs.existsSync(filePath)) {
            this.errors.push(`Fichier manquant: ${filePath}`);
            return false;
        }
        return true;
    }

    // Validation des références croisées
    validateCrossReferences() {
        console.log('🔍 Validation des références croisées...');
        
        // Vérification des références dans spec.instructions.md
        const instructionsPath = path.join(GITHUB_DIR, 'instructions/spec.instructions.md');
        if (this.validateFileExists(instructionsPath)) {
            const content = fs.readFileSync(instructionsPath, 'utf8');
            
            // Vérifier les références aux templates
            TEMPLATE_FILES.forEach(template => {
                const templateRef = `sdd/templates/${template}`;
                if (!content.includes(templateRef)) {
                    this.warnings.push(`Référence manquante vers ${templateRef} dans spec.instructions.md`);
                }
            });
        }

        // Vérification des références dans spec.prompt.md
        const promptPath = path.join(GITHUB_DIR, 'prompts/spec.prompt.md');
        if (this.validateFileExists(promptPath)) {
            const content = fs.readFileSync(promptPath, 'utf8');
            
            if (!content.includes('spec.instructions.md')) {
                this.warnings.push('Référence manquante vers spec.instructions.md dans spec.prompt.md');
            }
        }
    }

    // Validation de la cohérence terminologique
    validateTerminology() {
        console.log('📝 Validation de la terminologie...');
        
        const requiredTerms = [
            'validation explicite',
            'cycle feedback-révision',
            'méthodologie SDD',
            'progression incrémentale'
        ];

        ALL_FILES.forEach(file => {
            const filePath = path.join(GITHUB_DIR, file);
            if (this.validateFileExists(filePath)) {
                const content = fs.readFileSync(filePath, 'utf8').toLowerCase();
                
                requiredTerms.forEach(term => {
                    if (!content.includes(term.toLowerCase())) {
                        this.warnings.push(`Terme manquant "${term}" dans ${file}`);
                    }
                });
            }
        });
    }

    // Validation des formats de validation
    validateValidationFormats() {
        console.log('✅ Validation des formats de validation...');
        
        const chatmodePath = path.join(GITHUB_DIR, 'chatmodes/spec.chatmode.md');
        if (this.validateFileExists(chatmodePath)) {
            const content = fs.readFileSync(chatmodePath, 'utf8');
            
            const requiredValidationFormats = [
                '**Validation Requirements :**',
                '**Validation Design :**',
                '**Validation Tasks :**'
            ];

            // Vérifier si les formats existent directement ou via référence
            const hasDirectFormats = requiredValidationFormats.every(format => content.includes(format));
            const hasReferenceToInstructions = content.includes('spec.instructions.md') && content.includes('section');
            
            if (!hasDirectFormats && !hasReferenceToInstructions) {
                this.errors.push('Formats de validation manquants ou références incorrectes dans spec.chatmode.md');
            }
        }
    }

    // Validation des templates SDD
    validateTemplates() {
        console.log('🗂️ Validation des templates SDD...');
        
        TEMPLATE_FILES.forEach(template => {
            const templatePath = path.join(SDD_TEMPLATES_DIR, template);
            if (this.validateFileExists(templatePath)) {
                const checksum = this.calculateChecksum(templatePath);
                if (checksum) {
                    this.checksums.set(template, checksum);
                }
            }
        });
    }

    // Validation des commandes de déclenchement
    validateTriggerCommands() {
        console.log('⚡ Validation des commandes de déclenchement...');
        
        // Validation des commandes spec
        const specPromptPath = path.join(GITHUB_DIR, 'prompts/spec.prompt.md');
        if (this.validateFileExists(specPromptPath)) {
            const content = fs.readFileSync(specPromptPath, 'utf8');
            
            const expectedSpecCommands = [
                '/kiro-spec',
                '/sdd-generate', 
                '/kiro-sdd-spec'
            ];

            expectedSpecCommands.forEach(command => {
                if (!content.includes(command)) {
                    this.errors.push(`Commande de déclenchement manquante: ${command} dans spec.prompt.md`);
                }
            });
        }

        // Validation des commandes context
        const contextPromptPath = path.join(GITHUB_DIR, 'prompts/context.prompt.md');
        if (this.validateFileExists(contextPromptPath)) {
            const content = fs.readFileSync(contextPromptPath, 'utf8');
            
            const expectedContextCommands = [
                '/kiro-context',
                '/sdd-context',
                '/kiro-sdd-context'
            ];

            expectedContextCommands.forEach(command => {
                if (!content.includes(command)) {
                    this.errors.push(`Commande de déclenchement manquante: ${command} dans context.prompt.md`);
                }
            });
        }
    }

    // Génération du rapport de validation
    generateReport() {
        console.log('\n📊 RAPPORT DE VALIDATION SDD');
        console.log('================================');
        
        if (this.errors.length === 0 && this.warnings.length === 0) {
            console.log('✅ Validation réussie - Aucun problème détecté');
        } else {
            if (this.errors.length > 0) {
                console.log('\n❌ ERREURS CRITIQUES:');
                this.errors.forEach(error => console.log(`   - ${error}`));
            }
            
            if (this.warnings.length > 0) {
                console.log('\n⚠️  AVERTISSEMENTS:');
                this.warnings.forEach(warning => console.log(`   - ${warning}`));
            }
        }

        console.log('\n🔧 CHECKSUMS DES TEMPLATES:');
        this.checksums.forEach((checksum, template) => {
            console.log(`   ${template}: ${checksum}`);
        });

        const exitCode = this.errors.length > 0 ? 1 : 0;
        console.log(`\n🎯 Validation terminée avec code de sortie: ${exitCode}`);
        
        return exitCode;
    }

    // Exécution complète de la validation
    validate() {
        console.log('🚀 Démarrage de la validation de cohérence SDD...\n');
        
        this.validateTemplates();
        this.validateCrossReferences();
        this.validateTerminology();
        this.validateValidationFormats();
        this.validateTriggerCommands();
        
        return this.generateReport();
    }
}

// Exécution du script
if (require.main === module) {
    const validator = new SDDValidator();
    const exitCode = validator.validate();
    process.exit(exitCode);
}

module.exports = SDDValidator;
