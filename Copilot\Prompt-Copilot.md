Création d'instructions pour GitHub Copilot :

- .github\instructions\context.instructions.md :
    - Instructions globales pour GitHub Copilot pour la création de 3 fichiers à partir de templates :
        - sdd/project/product.md > à partir de sdd/templates/product.md
        - sdd/project/structure.md > à partir de sdd/templates/structure.md
        - sdd/project/tech.md > à partir de sdd/templates/tech.md

- .github\prompts\context.prompt.md
    - Instructions spécifiques pour GitHub Copilot pour la création de 3 fichiers à partir de templates :
        - sdd/project/product.md > à partir de sdd/templates/product.md
        - sdd/project/structure.md > à partir de sdd/templates/structure.md
        - sdd/project/tech.md > à partir de sdd/templates/tech.md

- .github\chatmodes\context.chatmode.md
    - Modes de chat pour GitHub Copilot pour la création de 3 fichiers à partir de templates :
        - sdd/project/product.md > à partir de sdd/templates/product.md
        - sdd/project/structure.md > à partir de sdd/templates/structure.md
        - sdd/project/tech.md > à partir de sdd/templates/tech.md

---

- .github\instructions\spec.instructions.md
    - Instructions globales pour GitHub Copilot pour la création de 3 fichiers à partir de templates :
        - sdd/project/requirements.md > à partir de sdd/templates/requirements.md
        - sdd/project/design.md > à partir de sdd/templates/design.md
        - sdd/project/tasks.md > à partir de sdd/templates/tasks.md

- .github\prompts\spec.prompt.md
    - Instructions spécifiques pour GitHub Copilot pour la création de 3 fichiers à partir de templates :
        - sdd/project/requirements.md > à partir de sdd/templates/requirements.md
        - sdd/project/design.md > à partir de sdd/templates/design.md
        - sdd/project/tasks.md > à partir de sdd/templates/tasks.md

- .github\chatmodes\spec.chatmode.md
    - Modes de chat pour GitHub Copilot pour la création de 3 fichiers à partir de templates :
        - sdd/project/requirements.md > à partir de sdd/templates/requirements.md
        - sdd/project/design.md > à partir de sdd/templates/design.md
        - sdd/project/tasks.md > à partir de sdd/templates/tasks.md