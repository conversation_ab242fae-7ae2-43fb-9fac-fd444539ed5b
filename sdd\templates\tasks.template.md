# Template Tasks - Plan d'Implémentation

## Instructions Intégrées pour l'Agent IA

### Objectif
Transformer le document design.md en plan d'implémentation exploitable avec checklist de tâches de codage orientées test.

### Processus Obligatoire
1. **Vérification prérequis** : S'assurer que design.md existe et est validé
2. **Conversion design → tâches** : Créer une série de prompts pour LLM de génération de code
3. **Orientation test** : Prioriser bonnes pratiques, progression incrémentale, tests précoces
4. **Tâches atomiques** : Chaque prompt s'appuie sur les précédents, pas de code orphelin
5. **Validation utilisateur** : Cycle feedback-révision jusqu'à approbation explicite
6. **Fin du workflow** : Informer que l'implémentation peut commencer

### Contraintes Techniques
- Créer le fichier dans `sdd/specs/{feature_name}/tasks.md`
- Format : liste numérotée à cocher (max 2 niveaux de hiérarchie)
- Sous-tâches en notation décimale (1.1, 1.2, 2.1)
- UNIQUEMENT tâches de codage (écriture, modification, test de code)
- Chaque tâche référence des exigences spécifiques du requirements.md
- Progression incrémentale et validation précoce
- Demander validation avec l'outil approprié
- Continuer les révisions jusqu'à approbation explicite

### Instructions Spécifiques de Conversion
```
Convertissez le design de la fonctionnalité en une série de prompts pour un LLM de génération de code qui implémentera chaque étape de manière orientée test. Priorisez les bonnes pratiques, la progression incrémentale et les tests précoces, en évitant les sauts de complexité. Chaque prompt doit s'appuyer sur les précédents et finir par tout relier ensemble. Il ne doit pas y avoir de code orphelin non intégré à une étape précédente. Concentrez-vous UNIQUEMENT sur les tâches impliquant l'écriture, la modification ou le test de code.
```

### Tâches EXCLUES (Non-Code)
- Tests d'acceptation utilisateur ou collecte de retours
- Déploiement en production ou préproduction
- Collecte ou analyse de métriques de performance
- Exécution manuelle de l'application pour tests bout en bout
- Formation utilisateur ou création de documentation
- Changements de processus métier ou organisationnels
- Activités marketing ou de communication

---

# Plan d'Implémentation - {FEATURE_NAME}

## Vue d'ensemble du Plan

### Résumé
[Description concise du plan d'implémentation basé sur le design.md]

### Stratégie d'Implémentation
- **Approche** : [TDD, BDD, ou approche choisie]
- **Progression** : [Incrémentale, par composant, par couche]
- **Validation** : [Tests unitaires, intégration, E2E]

### Couverture des Exigences
- **Requirements couverts** : [Liste des requirements.md couverts]
- **Composants implémentés** : [Composants du design.md]
- **Tests associés** : [Types de tests pour chaque composant]

## Phase 1 : Foundation et Setup

### 1. Configuration Projet
- [ ] **1.1 Initialiser la structure du projet**
  - Créer l'architecture de dossiers selon design.md
  - Configurer les outils de build et développement
  - *Référence : [Section Architecture du design.md]*

- [ ] **1.2 Configurer l'environnement de test**
  - Installer et configurer le framework de test
  - Créer les utilitaires de test de base
  - *Référence : [Section Stratégie de Test du design.md]*

- [ ] **1.3 Setup base de données**
  - Créer les scripts de migration initiale
  - Configurer la connexion et les pools
  - *Référence : [Requirements {REQ_ID} et Section Modèles de Données]*

### 2. Composants Core
- [ ] **2.1 Implémenter les modèles de données principaux**
  - Créer les entités/modèles selon le schéma design.md
  - Ajouter la validation des données
  - Écrire les tests unitaires des modèles
  - *Référence : [Requirements {REQ_ID} - Modèles de données]*

- [ ] **2.2 Créer les interfaces et types**
  - Définir les interfaces TypeScript/types
  - Créer les DTOs et contrats d'API
  - *Référence : [Section APIs et Interfaces du design.md]*

## Phase 2 : Logique Métier Core

### 3. Services et Logique Métier
- [ ] **3.1 Implémenter le service principal {SERVICE_NAME}**
  - Créer la classe/module de service
  - Implémenter les méthodes CRUD de base
  - Écrire les tests unitaires du service
  - *Référence : [Requirements {REQ_ID} - User Story principale]*

- [ ] **3.2 Ajouter la logique de validation métier**
  - Implémenter les règles métier du requirements.md
  - Ajouter la gestion des cas d'erreur
  - Tester les règles métier et cas limites
  - *Référence : [Requirements {REQ_ID} - Règles métier]*

- [ ] **3.3 Implémenter les services secondaires**
  - Créer les services de support
  - Intégrer avec le service principal
  - Tests d'intégration entre services
  - *Référence : [Requirements {REQ_ID} - Fonctionnalités secondaires]*

### 4. Couche d'Accès aux Données
- [ ] **4.1 Créer les repositories/DAOs**
  - Implémenter les repositories selon le design
  - Ajouter les requêtes optimisées
  - Tests unitaires des repositories
  - *Référence : [Section Performance du design.md]*

- [ ] **4.2 Implémenter le caching**
  - Ajouter la couche de cache selon design.md
  - Configurer les stratégies de cache
  - Tester les mécanismes de cache
  - *Référence : [Section Performance - Caching]*

## Phase 3 : API et Interfaces

### 5. API REST/GraphQL
- [ ] **5.1 Créer les contrôleurs/resolvers principaux**
  - Implémenter les endpoints selon design.md
  - Ajouter la validation des inputs
  - Tests d'intégration API
  - *Référence : [Requirements {REQ_ID} - Critères d'acceptation EARS]*

- [ ] **5.2 Implémenter l'authentification et autorisation**
  - Ajouter les middlewares de sécurité
  - Implémenter les contrôles d'accès
  - Tester les scénarios de sécurité
  - *Référence : [Section Sécurité du design.md]*

- [ ] **5.3 Ajouter la gestion d'erreurs globale**
  - Créer les handlers d'erreur
  - Implémenter les formats de réponse d'erreur
  - Tester les cas d'erreur
  - *Référence : [Section Gestion des Erreurs]*

### 6. Interface Utilisateur (si applicable)
- [ ] **6.1 Créer les composants UI principaux**
  - Implémenter les composants selon design.md
  - Ajouter la gestion d'état
  - Tests unitaires des composants
  - *Référence : [Requirements {REQ_ID} - Interface utilisateur]*

- [ ] **6.2 Intégrer avec l'API**
  - Créer les services client API
  - Implémenter la gestion des états de chargement
  - Tests d'intégration frontend-backend
  - *Référence : [Section Flux de Données du design.md]*

## Phase 4 : Intégration et Optimisation

### 7. Tests d'Intégration
- [ ] **7.1 Tests d'intégration système**
  - Créer les tests de bout en bout automatisés
  - Tester les flux utilisateur principaux
  - Valider les critères d'acceptation EARS
  - *Référence : [Tous les Requirements - Critères d'acceptation]*

- [ ] **7.2 Tests de performance**
  - Implémenter les tests de charge
  - Valider les objectifs de performance
  - Optimiser les points de contention
  - *Référence : [Section Performance du design.md]*

### 8. Finalisation
- [ ] **8.1 Optimisations finales**
  - Refactoring et nettoyage du code
  - Optimisation des requêtes et performances
  - Validation de la couverture de tests
  - *Référence : [Critères de Succès du design.md]*

- [ ] **8.2 Validation complète**
  - Exécuter tous les tests (unitaires, intégration, E2E)
  - Vérifier la conformité aux requirements.md
  - Valider la sécurité et la performance
  - *Référence : [Tous les Requirements et Design]*

## Critères de Validation par Phase

### Phase 1 - Foundation
- [ ] Structure projet conforme au design.md
- [ ] Tests configurés et fonctionnels
- [ ] Base de données initialisée

### Phase 2 - Logique Métier
- [ ] Services principaux implémentés et testés
- [ ] Règles métier validées
- [ ] Couche données fonctionnelle

### Phase 3 - API et UI
- [ ] API complète et documentée
- [ ] Sécurité implémentée
- [ ] Interface utilisateur fonctionnelle

### Phase 4 - Intégration
- [ ] Tests d'intégration passent
- [ ] Performance conforme aux objectifs
- [ ] Tous les requirements.md validés

## Références Croisées

### Mapping Requirements → Tâches
- **{REQ_ID_1}** : Tâches 2.1, 3.1, 5.1, 7.1
- **{REQ_ID_2}** : Tâches 3.2, 5.2, 7.1
- **{REQ_ID_3}** : Tâches 4.1, 4.2, 7.2

### Mapping Design → Tâches
- **Architecture** : Tâches 1.1, 2.2
- **Composants** : Tâches 3.1, 3.3, 6.1
- **Sécurité** : Tâches 5.2, 7.1
- **Performance** : Tâches 4.2, 7.2, 8.1

### Dépendances entre Tâches
- **1.x** → **2.x** : Setup requis avant développement
- **2.x** → **3.x** : Modèles requis pour services
- **3.x** → **5.x** : Services requis pour API
- **5.x** → **6.x** : API requise pour UI
- **Toutes** → **7.x** : Composants requis pour tests intégration

## Notes d'Implémentation

### Bonnes Pratiques
- Écrire les tests AVANT l'implémentation (TDD)
- Commits atomiques par tâche
- Code review après chaque phase
- Documentation inline du code

### Gestion des Erreurs
- Chaque tâche doit inclure la gestion d'erreur
- Tests des cas d'erreur obligatoires
- Logging approprié pour le debugging

### Performance
- Profiling après chaque phase
- Optimisation incrémentale
- Monitoring des métriques clés

---

## Instructions de Validation pour l'Agent

### Questions de Validation Suggérées
1. "Le plan couvre-t-il tous les aspects du design.md ?"
2. "Chaque tâche est-elle actionnable par un agent de codage ?"
3. "La progression est-elle incrémentale et logique ?"
4. "Les références aux requirements.md sont-elles précises ?"
5. "Les tests sont-ils intégrés à chaque étape ?"

### Format de Validation
```
**Validation Tasks :**
J'ai terminé le document tasks.md avec :
- [X] tâches de codage organisées en [Y] phases
- Progression incrémentale et orientée test
- Références précises aux requirements.md
- Couverture complète du design.md
- Critères de validation par phase

**Les tâches vous conviennent-elles ?**
```

### Cycle de Révision
1. Présenter le plan initial
2. Demander validation explicite
3. Si modifications demandées : réviser et re-valider
4. Répéter jusqu'à approbation explicite
5. Informer que l'implémentation peut commencer

### Fin du Workflow
Après approbation :
```
**Workflow de planification terminé !**

Vous pouvez maintenant commencer l'implémentation en :
1. Ouvrant le fichier tasks.md
2. Cliquant sur "Démarrer la tâche" à côté des éléments
3. Suivant la progression incrémentale définie

Chaque tâche s'appuie sur les précédentes et inclut les tests appropriés.
```