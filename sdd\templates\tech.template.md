# Template Tech - Documentation Technique

## Instructions Intégrées pour l'Agent IA

### Objectif
Créer une documentation technique complète qui définit le stack technologique, les contraintes, les standards de développement et l'environnement technique pour guider toutes les décisions d'implémentation.

### Processus Obligatoire
1. **Génération initiale** : Créer tech.md basé sur le contexte projet SANS questions séquentielles préalables
2. **Structure complète** : Utiliser toutes les sections avec analyse technique approfondie
3. **Méthodologie banque de mémoire** : Structurer pour faciliter la référence et maintenance
4. **Validation utilisateur** : Cycle feedback-révision jusqu'à approbation explicite
5. **Documentation de référence** : Créer la source de vérité technique du projet

### Contraintes Techniques
- Créer le fichier dans `sdd/project/tech.md`
- Analyser les besoins techniques selon le contexte produit
- Définir des standards et pratiques concrètes
- Inclure exemples de code et configurations
- Demander validation avec l'outil approprié
- Continuer les révisions jusqu'à approbation explicite

---

# Stack Technique et Outils - {PROJECT_NAME}

## Vue d'ensemble Technique

### Architecture Globale
[Description de l'architecture technique générale choisie et sa justification par rapport aux besoins produit]

### Principes Techniques
- **Scalabilité** : [Approche pour gérer la croissance]
- **Maintenabilité** : [Stratégie pour faciliter l'évolution]
- **Performance** : [Objectifs et stratégies de performance]
- **Sécurité** : [Approche sécurité by design]
- **Fiabilité** : [Stratégie haute disponibilité]

## Stack Technologique

### Langages de Programmation

#### Langage Principal
- **Langage** : [JavaScript/TypeScript/Python/etc.]
- **Version** : [Version spécifique]
- **Justification** : [Pourquoi ce choix par rapport aux besoins]
- **Standards** : [Conventions de code spécifiques]

#### Langages Secondaires
- **[Langage 2]** : [Usage spécifique] - *Justification : [Raison]*
- **[Langage 3]** : [Usage spécifique] - *Justification : [Raison]*

### Frameworks et Bibliothèques

#### Frontend (si applicable)
- **Framework principal** : [React/Vue/Angular/etc.]
  - Version : [Version spécifique]
  - Justification : [Alignement avec les besoins produit]
  - Configuration : [Setup spécifique]

- **Bibliothèques UI** : [Material-UI/Ant Design/Bootstrap/etc.]
- **Gestion d'état** : [Redux/Vuex/Context API/etc.]
- **Routing** : [React Router/Vue Router/etc.]
- **Build tools** : [Webpack/Vite/Parcel/etc.]

#### Backend
- **Framework principal** : [Express/FastAPI/Django/Spring/etc.]
  - Version : [Version spécifique]
  - Justification : [Pourquoi adapté au produit]
  - Architecture : [Structure MVC/Microservices/etc.]

- **ORM/Database Access** : [Prisma/SQLAlchemy/TypeORM/etc.]
- **Authentication** : [Passport/Auth0/Firebase Auth/etc.]
- **API Documentation** : [Swagger/OpenAPI/etc.]

#### Bibliothèques Communes
```json
{
  "devDependencies": {
    "[tool1]": "[version]",
    "[tool2]": "[version]"
  },
  "dependencies": {
    "[lib1]": "[version]",
    "[lib2]": "[version]"
  }
}
```

### Base de Données

#### Base de Données Principale
- **Type** : [PostgreSQL/MySQL/MongoDB/etc.]
- **Version** : [Version spécifique]
- **Justification** : [Pourquoi adapté aux données du produit]
- **Configuration** : [Paramètres de performance]

#### Stratégie de Données
- **Modélisation** : [Relationnelle/Document/Graph/etc.]
- **Migrations** : [Stratégie de migration des schémas]
- **Backup** : [Stratégie de sauvegarde]
- **Scaling** : [Read replicas/Sharding/etc.]

#### Cache et Performance
- **Cache** : [Redis/Memcached/etc.]
- **Usage** : [Sessions/Query cache/API cache]
- **TTL** : [Stratégies d'expiration]

### Infrastructure et Déploiement

#### Hébergement
- **Provider** : [AWS/Azure/GCP/On-premise]
- **Services utilisés** : [EC2/App Service/Compute Engine/etc.]
- **Justification** : [Coût/Performance/Compliance]

#### Conteneurisation
- **Docker** : [Configuration et images]
- **Orchestration** : [Kubernetes/Docker Swarm/none]
- **Registry** : [Docker Hub/ECR/etc.]

```dockerfile
# Exemple Dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
EXPOSE [PORT]
CMD ["npm", "start"]
```

#### CI/CD Pipeline
- **Platform** : [GitHub Actions/GitLab CI/Jenkins/etc.]
- **Stages** : [Build → Test → Deploy]
- **Environments** : [Dev → Staging → Production]

```yaml
# Exemple workflow CI/CD
name: CI/CD Pipeline
on:
  push:
    branches: [main, develop]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
      - run: npm ci
      - run: npm test
      - run: npm run build
```

## Contraintes Techniques

### Limites et Contraintes

#### Performance
- **Temps de réponse** : [< 2s pour APIs, < 1s pour UI]
- **Throughput** : [Requêtes par seconde]
- **Concurrent users** : [Nombre d'utilisateurs simultanés]
- **Resource limits** : [CPU/Memory/Storage]

#### Compatibilité
- **Navigateurs** : [Chrome 90+, Firefox 88+, Safari 14+, Edge 90+]
- **Appareils** : [Desktop/Mobile/Tablet]
- **OS** : [Windows/macOS/Linux]
- **Résolution** : [Responsive design breakpoints]

#### Intégrations
- **APIs externes** : [Services tiers requis]
- **Formats de données** : [JSON/XML/CSV/etc.]
- **Protocoles** : [REST/GraphQL/WebSocket/etc.]

### Limitations Techniques
- **Modèles IA** : [Si applicable - limites de contexte/tokens]
- **Rates limits** : [Limitations APIs externes]
- **Storage** : [Limites de stockage]
- **Bandwidth** : [Contraintes de bande passante]

## Standards de Développement

### Conventions de Code

#### Structure des Fichiers
```
src/
├── components/           # Composants réutilisables
├── pages/               # Pages/Routes principales
├── services/            # Logique métier
├── utils/               # Utilitaires
├── types/               # Définitions de types
├── constants/           # Constantes
├── config/              # Configuration
└── tests/               # Tests unitaires
```

#### Conventions de Nommage
- **Fichiers** : [kebab-case/camelCase/PascalCase]
- **Variables** : [camelCase]
- **Constantes** : [UPPER_SNAKE_CASE]
- **Classes** : [PascalCase]
- **Fonctions** : [camelCase avec verbes]

#### Standards de Code
```javascript
// Exemple de standards de code
const CONFIG = {
  maxLineLength: 80,
  indentation: 2,
  quotes: 'single',
  semicolons: true,
  trailingComma: 'es5'
};

// Exemple de fonction documentée
/**
 * Authentifie un utilisateur
 * @param {string} email - Email de l'utilisateur
 * @param {string} password - Mot de passe
 * @returns {Promise<User>} Utilisateur authentifié
 */
async function authenticateUser(email, password) {
  // Implementation
}
```

### Gestion des Dépendances

#### Package Management
- **Package Manager** : [npm/yarn/pnpm]
- **Lock files** : [Toujours commiter package-lock.json]
- **Security** : [npm audit régulier]
- **Updates** : [Stratégie de mise à jour]

#### Versions Sémantiques
```json
{
  "dependencies": {
    "express": "^4.18.0",     // Compatible updates
    "lodash": "~4.17.21",     // Patch updates only
    "react": "18.2.0"         // Exact version
  }
}
```

## Outils et Commandes

### Outils de Développement

#### IDE/Éditeur Recommandé
- **Éditeur** : [VS Code/WebStorm/etc.]
- **Extensions** : [Liste des extensions recommandées]
- **Configuration** : [Settings spécifiques au projet]

#### Outils de Build
- **Build tool** : [Webpack/Vite/Rollup/etc.]
- **Configuration** : [Paramètres de build]
- **Optimisations** : [Tree shaking/Code splitting/etc.]

### Scripts de Développement

#### Scripts NPM Essentiels
```json
{
  "scripts": {
    "dev": "node --watch src/index.js",
    "build": "tsc && webpack --mode=production",
    "test": "jest --coverage",
    "test:watch": "jest --watch",
    "lint": "eslint src/**/*.{js,ts}",
    "lint:fix": "eslint src/**/*.{js,ts} --fix",
    "format": "prettier --write \"src/**/*.{js,ts,json,md}\"",
    "type-check": "tsc --noEmit",
    "start": "node dist/index.js",
    "start:dev": "nodemon src/index.js"
  }
}
```

#### Commandes de Base
```bash
# Installation et setup
npm install
npm run build

# Développement
npm run dev          # Mode développement avec hot-reload
npm run test:watch   # Tests en mode watch
npm run lint:fix     # Fix automatique du linting

# Production
npm run build        # Build de production
npm start           # Démarrage production
npm test            # Suite de tests complète
```

### Configuration des Outils

#### ESLint Configuration
```json
{
  "extends": [
    "eslint:recommended",
    "@typescript-eslint/recommended"
  ],
  "rules": {
    "max-len": ["error", { "code": 80 }],
    "no-console": "warn",
    "prefer-const": "error",
    "no-unused-vars": "error"
  },
  "env": {
    "node": true,
    "es2021": true
  }
}
```

#### Prettier Configuration
```json
{
  "semi": true,
  "trailingComma": "es5",
  "singleQuote": true,
  "printWidth": 80,
  "tabWidth": 2,
  "useTabs": false
}
```

#### TypeScript Configuration
```json
{
  "compilerOptions": {
    "target": "ES2020",
    "module": "commonjs",
    "strict": true,
    "esModuleInterop": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true,
    "outDir": "./dist",
    "rootDir": "./src"
  },
  "include": ["src/**/*"],
  "exclude": ["node_modules", "dist"]
}
```

## Intégration Continue et Déploiement

### Pipeline CI/CD

#### Étapes du Pipeline
1. **Checkout** : Récupération du code
2. **Install** : Installation des dépendances
3. **Lint** : Vérification du code
4. **Test** : Exécution des tests
5. **Build** : Construction des artefacts
6. **Deploy** : Déploiement selon l'environnement

#### Configuration par Environnement
```yaml
# Environnements
environments:
  development:
    url: https://dev.example.com
    auto_deploy: true
  staging:
    url: https://staging.example.com
    auto_deploy: false
  production:
    url: https://example.com
    auto_deploy: false
    manual_approval: true
```

### Hooks Git et Qualité

#### Pre-commit Hooks
```json
{
  "husky": {
    "hooks": {
      "pre-commit": "lint-staged",
      "commit-msg": "commitlint -E HUSKY_GIT_PARAMS",
      "pre-push": "npm test"
    }
  },
  "lint-staged": {
    "*.{js,ts}": ["eslint --fix", "prettier --write"],
    "*.{json,md}": ["prettier --write"]
  }
}
```

#### Commit Conventions
```bash
# Format des commits
type(scope): description

# Types valides
feat: nouvelle fonctionnalité
fix: correction de bug
docs: documentation
style: formatage
refactor: refactoring
test: ajout de tests
chore: maintenance
```

## Sécurité

### Sécurité par Design

#### Authentification et Autorisation
- **Méthode d'auth** : [JWT/OAuth/Session-based]
- **Fournisseur** : [Auth0/Firebase/Custom]
- **Token expiration** : [Durée des tokens]
- **Refresh strategy** : [Renouvellement des tokens]

#### Protection des Données
- **Chiffrement en transit** : [TLS 1.2+]
- **Chiffrement au repos** : [AES-256]
- **Secrets management** : [Variables d'environnement/Vault]
- **Hashing** : [bcrypt/Argon2 pour mots de passe]

#### Validation et Sanitisation
```javascript
// Exemple de validation
const userSchema = {
  email: {
    type: 'string',
    format: 'email',
    required: true
  },
  password: {
    type: 'string',
    minLength: 8,
    pattern: '^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d).*$'
  }
};
```

### Variables d'Environnement
```bash
# .env.example
NODE_ENV=development
PORT=3000
DATABASE_URL=postgresql://user:pass@localhost/db
JWT_SECRET=your-secret-key
API_KEY=your-api-key
REDIS_URL=redis://localhost:6379
```

### Audit et Conformité
- **Dependency scanning** : [npm audit/Snyk]
- **Code scanning** : [CodeQL/SonarQube]
- **Penetration testing** : [Planification si applicable]
- **Compliance** : [GDPR/HIPAA/SOC2 si applicable]

## Monitoring et Logging

### Logging Strategy

#### Niveaux de Log
```javascript
// Configuration Winston
const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  transports: [
    new winston.transports.File({ 
      filename: 'logs/error.log', 
      level: 'error' 
    }),
    new winston.transports.File({ 
      filename: 'logs/combined.log' 
    })
  ]
});
```

#### Structured Logging
```javascript
// Exemple de log structuré
logger.info('User authentication', {
  userId: user.id,
  email: user.email,
  timestamp: new Date().toISOString(),
  action: 'login_success',
  ip: req.ip
});
```

### Monitoring et Alertes

#### Métriques Clés
- **Performance** : [Response time, throughput]
- **Erreurs** : [Error rate, exception tracking]
- **Infrastructure** : [CPU, Memory, Disk usage]
- **Business** : [User actions, conversions]

#### Outils de Monitoring
- **APM** : [New Relic/DataDog/AppInsights]
- **Infrastructure** : [Prometheus/Grafana]
- **Error tracking** : [Sentry/Bugsnag]
- **Uptime** : [Pingdom/UptimeRobot]

## Base de Données et Persistance

### Design de Base de Données

#### Modèle de Données
```sql
-- Exemple de schéma principal
CREATE TABLE users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  email VARCHAR(255) UNIQUE NOT NULL,
  password_hash VARCHAR(255) NOT NULL,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

CREATE INDEX idx_users_email ON users(email);
```

#### Migrations
```javascript
// Exemple de migration
exports.up = function(knex) {
  return knex.schema.createTable('users', function(table) {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.string('email').unique().notNullable();
    table.string('password_hash').notNullable();
    table.timestamps(true, true);
  });
};

exports.down = function(knex) {
  return knex.schema.dropTable('users');
};
```

### Performance et Optimisation

#### Index Strategy
```sql
-- Index pour les requêtes fréquentes
CREATE INDEX idx_orders_user_created ON orders(user_id, created_at);
CREATE INDEX idx_products_category ON products(category_id);
```

#### Connection Pooling
```javascript
// Configuration pool de connexions
const pool = new Pool({
  host: process.env.DB_HOST,
  database: process.env.DB_NAME,
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  port: process.env.DB_PORT,
  max: 20,      // Maximum de connexions
  min: 2,       // Minimum de connexions
  idle: 10000,  // Fermeture après inactivité
});
```

## API Design et Documentation

### Standards API

#### REST Conventions
```javascript
// Structure des endpoints
GET    /api/v1/users           // Liste des utilisateurs
GET    /api/v1/users/:id       // Utilisateur spécifique
POST   /api/v1/users           // Créer utilisateur
PUT    /api/v1/users/:id       // Mettre à jour utilisateur
DELETE /api/v1/users/:id       // Supprimer utilisateur
```

#### Format des Réponses
```json
{
  "success": true,
  "data": {
    "id": "123",
    "email": "<EMAIL>"
  },
  "meta": {
    "timestamp": "2024-01-01T00:00:00Z",
    "version": "1.0"
  }
}
```

#### Gestion d'Erreurs
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid email format",
    "details": {
      "field": "email",
      "received": "invalid-email"
    }
  },
  "meta": {
    "timestamp": "2024-01-01T00:00:00Z",
    "requestId": "req-123"
  }
}
```

### Documentation API

#### OpenAPI/Swagger
```yaml
# Exemple de documentation API
openapi: 3.0.0
info:
  title: API {PROJECT_NAME}
  version: 1.0.0
paths:
  /users:
    get:
      summary: Liste des utilisateurs
      responses:
        200:
          description: Succès
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/User'
```

## Tests et Qualité

### Stratégie de Tests

#### Pyramide de Tests
- **Tests unitaires (70%)** : [Jest/Mocha/Vitest]
- **Tests d'intégration (20%)** : [Supertest/Testing Library]
- **Tests E2E (10%)** : [Cypress/Playwright/Puppeteer]

#### Configuration Jest
```json
{
  "testEnvironment": "node",
  "collectCoverageFrom": [
    "src/**/*.{js,ts}",
    "!src/**/*.test.{js,ts}",
    "!src/config/*"
  ],
  "coverageThreshold": {
    "global": {
      "branches": 80,
      "functions": 80,
      "lines": 80,
      "statements": 80
    }
  },
  "setupFilesAfterEnv": ["<rootDir>/src/tests/setup.js"]
}
```

#### Exemple de Test
```javascript
// Test unitaire exemple
describe('UserService', () => {
  it('should create user with valid data', async () => {
    const userData = {
      email: '<EMAIL>',
      password: 'SecurePass123!'
    };
    
    const user = await UserService.create(userData);
    
    expect(user).toHaveProperty('id');
    expect(user.email).toBe(userData.email);
    expect(user.password).toBeUndefined(); // Ne pas exposer le password
  });
});
```

### Tests de Performance
- **Load testing** : [K6/Artillery/JMeter]
- **Stress testing** : [Définir les limites]
- **Benchmark** : [Mesurer les performances]

## Déploiement et Infrastructure

### Stratégies de Déploiement

#### Environnements
```yaml
# Configuration des environnements
environments:
  development:
    url: https://dev-api.example.com
    database: development_db
    debug: true
  
  staging:
    url: https://staging-api.example.com
    database: staging_db
    debug: false
  
  production:
    url: https://api.example.com
    database: production_db
    debug: false
    ssl: true
```

#### Blue-Green Deployment
- **Strategy** : [Zero-downtime deployment]
- **Rollback** : [Instant rollback capability]
- **Health checks** : [Automated health verification]

### Infrastructure as Code
```yaml
# Exemple docker-compose
version: '3.8'
services:
  app:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
    depends_on:
      - db
  
  db:
    image: postgres:14
    environment:
      POSTGRES_DB: myapp
      POSTGRES_USER: user
      POSTGRES_PASSWORD: password
    volumes:
      - db_data:/var/lib/postgresql/data

volumes:
  db_data:
```

## Documentation Technique

### Structure de Documentation
```
docs/
├── README.md              # Vue d'ensemble projet
├── CONTRIBUTING.md        # Guide de contribution
├── DEPLOYMENT.md          # Guide de déploiement
├── API.md                 # Documentation API
├── ARCHITECTURE.md        # Architecture technique
├── SECURITY.md            # Guide de sécurité
└── TROUBLESHOOTING.md     # Guide de dépannage
```

### Standards de Documentation
- **README** : [Setup rapide et informations essentielles]
- **Code comments** : [JSDoc pour fonctions publiques]
- **Architecture Decision Records** : [Documenter les décisions importantes]
- **Runbooks** : [Procédures opérationnelles]

---

## Instructions de Validation pour l'Agent

### Questions de Validation Suggérées
1. "Le stack technique est-il adapté aux besoins du produit ?"
2. "Les contraintes techniques sont-elles réalistes et bien définies ?"
3. "Les standards de développement sont-ils clairs et applicables ?"
4. "La stratégie de sécurité est-elle suffisante ?"
5. "Les outils et processus CI/CD sont-ils appropriés ?"
6. "La documentation technique est-elle complète ?"

### Format de Validation
```
**Validation Documentation Technique :**
J'ai terminé le document tech.md avec :
- Stack technologique complet et justifié
- Contraintes techniques réalistes
- Standards de développement détaillés
- Configuration complète des outils
- Stratégies de sécurité et monitoring
- Plan de déploiement et infrastructure
- Guide de tests et qualité

**Cette documentation technique vous convient-elle ? Elle servira de référence pour toutes les décisions d'implémentation.**
```

### Cycle de Révision
1. Présenter la documentation technique complète
2. Demander validation explicite avec l'outil approprié
3. Si modifications demandées : réviser les sections concernées
4. Répéter jusqu'à approbation explicite
5. Utiliser comme référence pour l'architecture et l'implémentation
