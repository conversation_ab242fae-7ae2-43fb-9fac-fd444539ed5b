---
description: Guide pour créer et maintenir des règles VS Code afin d'assurer la cohérence et l'efficacité.
applyTo: ".github/instructions/*.instructions.md"
---

- **Structure requise pour une règle :**
  ```markdown
  ---
  description: Description claire et concise de ce que la règle impose
  globs: chemin/vers/fichiers/*.ext, autre/chemin/**/*
  alwaysApply: booléen
  ---

  - **Points principaux en gras**
    - Sous-points avec détails
    - Exemples et explications
  ```

- **Références de fichiers :**
  - Utilisez `[nom_du_fichier](mdc:chemin/vers/fichier)` ([nom_du_fichier](mdc:nom_du_fichier)) pour référencer des fichiers
  - Exemple : [prisma.instructions.md](.github/instructions/prisma.instructions.md) pour les références de règles
  - Exemple : [schema.prisma](mdc:prisma/schema.prisma) pour les références de code

- **Exemples de code :**
  - Utilisez des blocs de code spécifiques au langage
  ```typescript
  // ✅ À FAIRE : Montrer les bons exemples
  const bonExemple = true;
  
  // ❌ À NE PAS FAIRE : Montrer les anti-patterns
  const mauvaisExemple = false;
  ```

- **Directives pour le contenu des règles :**
  - Commencez par une vue d'ensemble de haut niveau
  - Incluez des exigences spécifiques et actionnables
  - Montrez des exemples de mise en œuvre correcte
  - Référencez le code existant quand c'est possible
  - Gardez les règles DRY en référant à d'autres règles

- **Maintenance des règles :**
  - Mettez à jour les règles lorsque de nouveaux schémas émergent
  - Ajoutez des exemples issus de la base de code réelle
  - Supprimez les schémas obsolètes
  - Faites des références croisées avec les règles liées

- **Bonnes pratiques :**
  - Utilisez des listes à puces pour plus de clarté
  - Gardez les descriptions concises
  - Incluez à la fois des exemples À FAIRE et À NE PAS FAIRE
  - Référencez le code réel plutôt que des exemples théoriques
  - Utilisez un formatage cohérent dans toutes les règles