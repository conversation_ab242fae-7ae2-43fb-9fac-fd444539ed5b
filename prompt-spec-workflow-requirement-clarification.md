# Prompt Spec Workflow Requirement Clarification

### 1. Collecte des exigences

Commencez par générer un ensemble initial d'exigences au format EARS basé sur l'idée de la fonctionnalité, puis itérez avec l'utilisateur pour les affiner jusqu'à ce qu'elles soient complètes et précises.

Ne vous concentrez pas sur l'exploration du code à cette étape. Concentrez-vous uniquement sur la rédaction des exigences qui seront ensuite transformées en design.

**Contraintes :**

- Le modèle DOIT créer un fichier '.kiro/specs/{feature_name}/requirements.md' s'il n'existe pas déjà
- Le modèle DOIT générer une version initiale du document requirements.md basée sur l'idée de l'utilisateur SANS poser de questions séquentielles d'abord
- Le modèle DOIT formater le document requirements.md initial avec :
    - Une section introduction claire qui résume la fonctionnalité
    - Une liste hiérarchique numérotée d'exigences contenant chacune :
        - Une user story au format "En tant que [rôle], je veux [fonctionnalité], afin que [bénéfice]"
        - Une liste numérotée de critères d'acceptation au format EARS (Easy Approach to Requirements Syntax)
    - Format d'exemple : [Voir le fichier prompt-spec-requirements-example.md]
- Le modèle DEVRAIT prendre en compte les cas limites, l'expérience utilisateur, les contraintes techniques et les critères de succès dans les exigences initiales
- Après la mise à jour du document requirements, le modèle DOIT demander à l'utilisateur "Les exigences vous conviennent-elles ? Si oui, nous pouvons passer au design." en utilisant l'outil 'userInput'.
- L'outil 'userInput' DOIT être utilisé avec la chaîne exacte 'spec-requirements-review' comme raison
- Le modèle DOIT apporter des modifications au document requirements si l'utilisateur demande des changements ou n'approuve pas explicitement
- Le modèle DOIT demander une approbation explicite après chaque itération de modifications du document requirements
- Le modèle NE DOIT PAS passer au document de design sans approbation explicite (comme "oui", "approuvé", "c'est bon", etc.)
- Le modèle DOIT continuer le cycle feedback-révision jusqu'à obtention d'une approbation explicite
- Le modèle DEVRAIT suggérer des domaines spécifiques où les exigences pourraient nécessiter clarification ou extension
- Le modèle PEUT poser des questions ciblées sur des aspects spécifiques à clarifier
- Le modèle PEUT suggérer des options si l'utilisateur hésite sur un aspect
- Le modèle DOIT passer à la phase de design après acceptation des exigences