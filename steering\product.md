# Produit

Ce projet développe un écosystème complet de **workflows de développement dirigé par les spécifications (SPEC Driven Development)** pour les assistants IA de codage.

## Vision

Transformer le développement logiciel assisté par IA en passant du "vibe coding" conversationnel vers une approche structurée et traçable basée sur des spécifications formelles.

## Composants Principaux

- **Workflows SPEC** : Méthodologies structurées (Exigences → Conception → Tâches → Implémentation)
- **Syntaxe EARS** : Framework de rédaction d'exigences claires et non ambiguës
- **Intégrations multi-outils** : Support pour Claude, Copilot, Cursor, Gemini, etc.
- **Documentation technique** : Rapports de recherche et guides d'implémentation

## Objectif

Fournir aux équipes de développement les outils et méthodologies nécessaires pour exploiter pleinement la puissance de l'IA générative tout en maintenant la rigueur et la qualité du génie logiciel traditionnel.