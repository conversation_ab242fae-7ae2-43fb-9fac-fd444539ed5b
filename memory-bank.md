# Banque de Mémoire

## Guide Complet de la Banque de Mémoire

### Guide d’Installation Rapide

Pour commencer avec la Banque de Mémoire :

1. **Installer ou ouvrir l’outil**
2. **Copier les instructions personnalisées** – Utilisez le bloc de code ci-dessous
3. **Coller dans l’outil** – Ajoutez comme instructions personnalisées ou dans un fichier `.rules`
4. **Initialiser** – Demandez à l’outil d’« initialiser la banque de mémoire »

[Voir les instructions détaillées d’installation](#démarrage-avec-la-banque-de-mémoire)

### Instructions Personnalisées pour la Banque de Mémoire [À COPIER]

# Banque de Mémoire

Je suis un assistant expert en ingénierie logicielle avec une caractéristique unique : ma mémoire est réinitialisée complètement entre chaque session. Ce n’est pas une limitation – c’est ce qui me pousse à maintenir une documentation parfaite. Après chaque réinitialisation, je dépends ENTIÈREMENT de la Banque de Mémoire pour comprendre le projet et poursuivre le travail efficacement. Je DOIS lire TOUS les fichiers de la banque de mémoire au début de CHAQUE tâche – c’est obligatoire.

## Structure de la Banque de Mémoire

La Banque de Mémoire se compose de fichiers principaux et de fichiers contextuels optionnels, tous au format Markdown. Les fichiers s’organisent selon une hiérarchie claire :

```mermaid
flowchart TD
   PB[projectbrief.md] --> PC[productContext.md]
   PB --> SP[systemPatterns.md]
   PB --> TC[techContext.md]

   PC --> AC[activeContext.md]
   SP --> AC
   TC --> AC

   AC --> P[progress.md]
```

### Fichiers Principaux (Obligatoires)
1. `projectbrief.md`
   - Document fondamental qui façonne tous les autres fichiers
   - Créé au début du projet s’il n’existe pas
   - Définit les exigences et objectifs principaux
   - Source de vérité pour le périmètre du projet

2. `productContext.md`
   - Pourquoi ce projet existe
   - Problèmes résolus
   - Fonctionnement attendu
   - Objectifs d’expérience utilisateur

3. `activeContext.md`
   - Focus de travail actuel
   - Changements récents
   - Prochaines étapes
   - Décisions et considérations actives
   - Modèles et préférences importants
   - Enseignements et insights du projet

4. `systemPatterns.md`
   - Architecture du système
   - Décisions techniques clés
   - Modèles de conception utilisés
   - Relations entre composants
   - Chemins d’implémentation critiques

5. `techContext.md`
   - Technologies utilisées
   - Configuration de développement
   - Contraintes techniques
   - Dépendances
   - Modèles d’utilisation des outils

6. `progress.md`
   - Ce qui fonctionne
   - Ce qu’il reste à construire
   - Statut actuel
   - Problèmes connus
   - Évolution des décisions du projet

### Contexte Additionnel
Créez des fichiers/dossiers supplémentaires dans `memory-bank/` pour organiser :
- Documentation de fonctionnalités complexes
- Spécifications d’intégration
- Documentation API
- Stratégies de test
- Procédures de déploiement

## Flux de Travail Principaux

### Mode Planification
flowchart TD
   Start[Début] --> ReadFiles[Lire la Banque de Mémoire]
   ReadFiles --> CheckFiles{Fichiers complets ?}

   CheckFiles -->|Non| Plan[Créer un plan]
   Plan --> Document[Documenter dans le chat]

   CheckFiles -->|Oui| Verify[Vérifier le contexte]
   Verify --> Strategy[Développer une stratégie]
   Strategy --> Present[Présenter l’approche]

### Mode Action
flowchart TD
   Start[Début] --> Context[Vérifier la Banque de Mémoire]
   Context --> Update[Mettre à jour la documentation]
   Update --> Execute[Exécuter la tâche]
   Execute --> Document[Documenter les changements]

## Mises à Jour de la Documentation

Les mises à jour de la Banque de Mémoire ont lieu lorsque :
1. Découverte de nouveaux modèles de projet
2. Après des changements importants
3. Sur demande de l’utilisateur avec **mettre à jour la banque de mémoire** (DOIT revoir TOUS les fichiers)
4. Lorsque le contexte nécessite une clarification

flowchart TD
   Start[Processus de mise à jour]

   subgraph Processus
      P1[Revoir TOUS les fichiers]
      P2[Documenter l’état actuel]
      P3[Clarifier les prochaines étapes]
      P4[Documenter les insights & modèles]

      P1 --> P2 --> P3 --> P4
   end

   Start --> Processus

Note : Lorsqu’une mise à jour est déclenchée par **mettre à jour la banque de mémoire**, il faut revoir chaque fichier, même si certains ne nécessitent pas de modifications. Portez une attention particulière à `activeContext.md` et `progress.md` car ils suivent l’état actuel.

RAPPEL : Après chaque réinitialisation de mémoire, tout recommence à zéro. La Banque de Mémoire est le seul lien avec le travail précédent. Elle doit être maintenue avec précision et clarté, car l’efficacité dépend entièrement de son exactitude.

---

### Qu’est-ce que la Banque de Mémoire ?

La Banque de Mémoire est un système de documentation structuré qui permet de préserver le contexte d’un projet entre les sessions. Elle transforme un assistant sans mémoire en un partenaire de développement persistant capable de « se souvenir » des détails du projet au fil du temps.

#### Principaux Avantages

* **Préservation du contexte** : Maintenir la connaissance du projet entre les sessions
* **Développement cohérent** : Interactions prévisibles avec l’assistant
* **Projets auto-documentés** : Générer une documentation précieuse en travaillant
* **Adaptable à tout projet** : Fonctionne pour des projets de toute taille ou complexité
* **Indépendant de la technologie** : Compatible avec tous les langages et outils

### Fonctionnement de la Banque de Mémoire

La Banque de Mémoire n’est pas une fonctionnalité spécifique à un outil – c’est une méthodologie pour gérer le contexte via une documentation structurée. Lorsque vous demandez à l’assistant de « suivre les instructions personnalisées », il lit les fichiers de la Banque de Mémoire pour reconstruire sa compréhension du projet.

#### Comprendre les Fichiers

Les fichiers de la Banque de Mémoire sont simplement des fichiers markdown créés dans votre projet. Ce ne sont pas des fichiers cachés ou spéciaux – juste de la documentation accessible à tous.

### Explication des Fichiers de la Banque de Mémoire

#### Fichiers Principaux

1. **projectbrief.md**
   * La base du projet
   * Vue d’ensemble de ce que vous construisez
   * Exigences et objectifs principaux
   * Exemple : « Développement d’une application web React pour la gestion d’inventaire avec scan de codes-barres »
2. **productContext.md**
   * Pourquoi le projet existe
   * Problèmes à résoudre
   * Fonctionnement attendu
   * Exemple : « Le système d’inventaire doit gérer plusieurs entrepôts et des mises à jour en temps réel »
3. **activeContext.md**
   * Fichier le plus fréquemment mis à jour
   * Focus de travail actuel et changements récents
   * Décisions et considérations actives
   * Modèles et enseignements importants
   * Exemple : « Implémentation du composant scanner de codes-barres ; dernière session : intégration de l’API terminée »
4. **systemPatterns.md**
   * Architecture du système
   * Décisions techniques clés
   * Modèles de conception utilisés
   * Relations entre composants
   * Exemple : « Utilisation de Redux pour la gestion d’état avec une structure de store normalisée »
5. **techContext.md**
   * Technologies et frameworks utilisés
   * Configuration de développement
   * Contraintes techniques
   * Dépendances et outils
   * Exemple : « React 18, TypeScript, Firebase, Jest pour les tests »
6. **progress.md**
   * Suivi de ce qui fonctionne et reste à faire
   * Statut des fonctionnalités
   * Problèmes et limitations connus
   * Évolution des décisions du projet
   * Exemple : « Authentification utilisateur terminée ; gestion d’inventaire à 80 % ; reporting non commencé »

#### Contexte Additionnel

Créez des fichiers supplémentaires si besoin pour organiser :

* Documentation de fonctionnalités complexes
* Spécifications d’intégration
* Documentation API
* Stratégies de test
* Procédures de déploiement

### Démarrage avec la Banque de Mémoire

#### Première Installation

1. Créez un dossier `memory-bank/` à la racine du projet
2. Préparez un brief de projet de base (technique ou non)
3. Demandez à l’assistant d’« initialiser la banque de mémoire »

#### Conseils pour le Brief de Projet

* Commencez simplement – le niveau de détail est libre
* Concentrez-vous sur ce qui compte pour vous
* L’assistant peut aider à compléter et poser des questions
* Vous pouvez le mettre à jour au fil du projet

### Travailler avec la Banque de Mémoire

#### Flux de Travail Principaux

**Mode Planification**

Utilisez ce mode pour la stratégie et la planification.

**Mode Action**

Utilisez ce mode pour l’implémentation et l’exécution des tâches.

#### Commandes Clés

* **« suivre les instructions personnalisées »** – Demande à l’assistant de lire les fichiers de la Banque de Mémoire et de reprendre le travail là où il s’est arrêté (à utiliser au début des tâches)
* **« initialiser la banque de mémoire »** – À utiliser lors du démarrage d’un nouveau projet
* **« mettre à jour la banque de mémoire »** – Déclenche une revue complète et une mise à jour de la documentation pendant une tâche
* Alternez entre les modes Planification/Action selon les besoins

#### Mises à Jour de la Documentation

Les mises à jour de la Banque de Mémoire doivent avoir lieu automatiquement lorsque :

1. De nouveaux modèles sont découverts dans le projet
2. Après des changements importants
3. Sur demande explicite avec **« mettre à jour la banque de mémoire »**
4. Lorsque le contexte nécessite une clarification

### Foire Aux Questions

#### Où sont stockés les fichiers de la banque de mémoire ?

Ce sont des fichiers markdown classiques stockés dans le dépôt du projet, généralement dans un dossier `memory-bank/`. Ils ne sont pas cachés – ils font partie de la documentation du projet.

#### Faut-il utiliser des instructions personnalisées ou un fichier `.rules` ?

Les deux approches fonctionnent :

* **Instructions personnalisées** : Appliquées globalement à toutes les conversations. Idéal pour un comportement cohérent sur tous les projets.
* **Fichier `.rules`** : Spécifique au projet et stocké dans le dépôt. Idéal pour une personnalisation par projet.

Le choix dépend du besoin de globalité ou de spécificité.

#### Gestion de la fenêtre de contexte

Au fil du travail, la fenêtre de contexte se remplit (voir la barre de progression). Si les réponses deviennent plus lentes ou moins précises, il faut :

1. Demander à l’assistant de **« mettre à jour la banque de mémoire »** pour documenter l’état actuel
2. Démarrer une nouvelle conversation/tâche
3. Demander à l’assistant de **« suivre les instructions personnalisées »** dans la nouvelle session

Ce flux garantit la préservation du contexte important avant de repartir sur une nouvelle session.

#### À quelle fréquence mettre à jour la banque de mémoire ?

Après chaque étape importante ou changement de direction. En développement actif, une mise à jour tous les quelques sessions est utile. Utilisez la commande **« mettre à jour la banque de mémoire »** pour garantir la préservation du contexte. L’assistant met aussi à jour la banque automatiquement.

#### Ce concept fonctionne-t-il avec d’autres outils d’IA ?

Oui ! La Banque de Mémoire est une méthodologie documentaire applicable à tout assistant IA pouvant lire des fichiers de documentation. Les commandes peuvent varier, mais la structure reste efficace.

#### Quel rapport avec les limitations de fenêtre de contexte ?

La Banque de Mémoire aide à gérer les limitations en stockant les informations importantes dans un format structuré, facilement rechargé au besoin. Cela évite la surcharge tout en gardant l’essentiel disponible.

#### Peut-on utiliser ce concept pour des projets non techniques ?

Absolument ! La Banque de Mémoire fonctionne pour tout projet nécessitant une documentation structurée : rédaction, organisation d’événements, etc. La structure des fichiers peut varier, mais le principe reste puissant.

#### Est-ce différent d’un fichier README ?

Similaire dans l’idée, la Banque de Mémoire propose une approche plus structurée et complète, conçue pour maintenir le contexte entre les sessions IA. Elle va plus loin qu’un simple README.

### Bonnes Pratiques

#### Pour Commencer

* Démarrez avec un brief de projet basique et laissez la structure évoluer
* Laissez l’assistant aider à créer la structure initiale
* Révisez et ajustez les fichiers selon votre flux de travail

#### Travail Continu

* Laissez les modèles émerger naturellement
* Ne forcez pas les mises à jour – elles doivent être organiques
* Faites confiance au processus – la valeur s’accumule avec le temps
* Vérifiez le contexte au début de chaque session

#### Flux de Documentation

* **projectbrief.md** est la base
* **activeContext.md** évolue le plus
* **progress.md** suit les étapes clés
* Tous les fichiers maintiennent l’intelligence du projet

### Instructions d’Installation Détaillées

#### Pour les Instructions Personnalisées (Global)

1. Ouvrez VSCode
2. Accédez aux paramètres de l’extension
3. Trouvez « Instructions personnalisées »
4. Copiez et collez les instructions complètes de la Banque de Mémoire depuis le début de ce guide

#### Pour `.rules` (Spécifique au Projet)

1. Créez un fichier `.rules` à la racine du projet
2. Copiez et collez les instructions de la Banque de Mémoire depuis le début de ce guide
3. Enregistrez le fichier
4. Les règles seront appliquées automatiquement lors du travail sur ce projet
