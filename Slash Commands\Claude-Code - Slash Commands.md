# Guide complet des Slash Commands dans Claude Code

Les **slash commands** de Claude Code sont une fonctionnalité puissante qui permet de créer des commandes personnalisées pour automatiser les tâches répétitives et optimiser votre workflow de développement. Ce guide vous explique tout ce que vous devez savoir pour maîtriser cette fonctionnalité.

## Introduction aux Slash Commands

### Qu'est-ce que les Slash Commands ?

Les slash commands sont des **prompts pré-définis stockés dans des fichiers Markdown** que Claude Code peut exécuter instantanément. Elles fonctionnent comme des raccourcis intelligents qui encapsulent des instructions spécifiques et peuvent être réutilisées à travers vos projets.

### Syntaxe de base

```
/<nom-commande> [arguments]
```

**Paramètres :**
- `<nom-commande>` : Nom dérivé du fichier Markdown (sans l'extension `.md`)
- `[arguments]` : Arguments optionnels passés à la commande

### Avantages clés

- **Réduction de la consommation de tokens** en évitant de répéter les mêmes instructions
- **Standardisation des processus** à travers l'équipe
- **Accélération des tâches communes** avec des instructions pré-écrites
- **Continuité du workflow** sans interruption de session

## Types de Commandes

### Commandes de Projet

Les **commandes de projet** sont stockées dans votre dépôt et partagées avec votre équipe.

**Emplacement :** `.claude/commands/`
**Préfixe :** `/project:`
**Indicateur :** `(project)` affiché dans `/help`

**Exemple de création :**
```bash
# Créer une commande de projet
mkdir -p .claude/commands
echo "Analyser ce code pour les problèmes de performance et suggérer des optimisations :" > .claude/commands/optimize.md
```

**Utilisation :** `/project:optimize` ou simplement `/optimize`

### Commandes Personnelles

Les **commandes personnelles** sont disponibles dans tous vos projets.

**Emplacement :** `~/.claude/commands/`
**Préfixe :** `/user:`
**Indicateur :** `(user)` affiché dans `/help`

**Exemple de création :**
```bash
# Créer une commande personnelle
mkdir -p ~/.claude/commands
echo "Examiner ce code pour les vulnérabilités de sécurité :" > ~/.claude/commands/security-review.md
```

**Utilisation :** `/user:security-review` ou `/security-review`

## Fonctionnalités Avancées

### Arguments Dynamiques avec $ARGUMENTS

Vous pouvez passer des valeurs dynamiques à vos commandes en utilisant le placeholder `$ARGUMENTS`.

**Exemple de commande avec arguments :**
```markdown
Trouve et corrige le problème #$ARGUMENTS. Suis ces étapes :
1. Comprendre le problème décrit dans le ticket
2. Localiser le code pertinent dans notre base de code
3. Implémenter une solution qui traite la cause racine
4. Ajouter des tests appropriés
5. Préparer une description concise de PR
```

**Utilisation :**
```bash
> /fix-issue 123
```

Le `$ARGUMENTS` sera remplacé par "123" dans le prompt.

### Système de Namespacing

Le **namespacing** permet d'organiser vos commandes en groupes logiques via des sous-répertoires.

**Structure :** `<prefix>:<namespace>:<command>`

| Chemin du fichier | Commande résultante |
|-------------------|-------------------|
| `.claude/commands/optimize.md` | `/project:optimize` |
| `.claude/commands/frontend/component.md` | `/project:frontend:component` |
| `~/.claude/commands/security/audit.md` | `/user:security:audit` |

### Intégration avec CLAUDE.md

Vous pouvez référencer vos slash commands dans votre fichier `CLAUDE.md` pour une utilisation en langage naturel :

```markdown
### Mots-clés de travail
- **"corriger le problème"** : Exécute la commande `/fix-issue` avec le numéro
- **"analyser les performances"** : Exécute la commande `/optimize`
- **"révision de sécurité"** : Exécute la commande `/security-review`
```

## Guide de Création Étape par Étape

### Étape 1 : Créer la Structure de Répertoires

Pour les **commandes de projet** (partagées avec l'équipe) :
```bash
mkdir -p .claude/commands
```

Pour les **commandes personnelles** (usage individuel) :
```bash
mkdir -p ~/.claude/commands
```

### Étape 2 : Créer le Fichier de Commande

**Exemple : Commande de déploiement**
```bash
# Créer le fichier de commande
cat > .claude/commands/deploy-check.md << 'EOF'
Effectue une vérification pré-déploiement complète :

## Étapes de vérification
1. Vérifier que tous les tests passent
2. Contrôler la couverture de code
3. Analyser les vulnérabilités de sécurité
4. Valider la configuration d'environnement
5. Confirmer les migrations de base de données
6. Vérifier les dépendances externes

Fournis un rapport détaillé avec le statut de chaque vérification.
EOF
```

### Étape 3 : Utiliser la Commande

```bash
> /deploy-check
```

## Exemples Pratiques de Commandes

### Commande de Révision de Code
```markdown
<!-- .claude/commands/code-review.md -->
Effectue une révision de code complète pour $ARGUMENTS :

## Critères d'évaluation
- **Lisibilité** : Code clair et bien commenté
- **Performance** : Identification des goulots d'étranglement
- **Sécurité** : Vulnérabilités potentielles
- **Maintenabilité** : Structure et organisation
- **Tests** : Couverture et qualité des tests

Fournis des recommandations d'amélioration spécifiques.
```

### Commande de Génération de Tests
```markdown
<!-- .claude/commands/generate-tests.md -->
Génère une suite de tests complète pour $ARGUMENTS :

## Types de tests à créer
1. **Tests unitaires** : Fonctions individuelles
2. **Tests d'intégration** : Interactions entre composants
3. **Tests de régression** : Cas de non-régression
4. **Tests de performance** : Benchmarks si applicable

Assure-toi que les tests couvrent les cas limites et d'erreur.
```

### Commande de Documentation
```markdown
<!-- .claude/commands/document.md -->
Crée une documentation complète pour $ARGUMENTS :

## Sections à inclure
- **Vue d'ensemble** : Description et objectif
- **Installation** : Prérequis et setup
- **Utilisation** : Exemples pratiques
- **API** : Référence des méthodes/fonctions
- **Configuration** : Paramètres disponibles
- **Dépannage** : Problèmes courants et solutions

Utilise le format Markdown avec des exemples de code.
```

## Commandes Populaires de la Communauté

Basé sur les pratiques de la communauté, voici des idées de commandes utiles :

### Workflow de Développement
- `/session-start` - Initialiser une session de développement
- `/pr-create` - Workflow de création de pull request
- `/build` - Construire le projet et corriger les erreurs
- `/commit` - Committer les changements avec message approprié

### Qualité et Sécurité
- `/security-scan` - Audit de sécurité
- `/performance-check` - Analyse de performance
- `/lint-fix` - Correction automatique du linting
- `/type-check` - Vérification des types

### Gestion de Projet
- `/issue-analyze` - Analyser un problème GitHub
- `/feature-plan` - Planifier une nouvelle fonctionnalité
- `/backup-create` - Procédures de sauvegarde
- `/deploy-prep` - Préparation de déploiement

## Bonnes Pratiques

### Organisation des Commandes

1. **Utilisez des noms descriptifs** : `/security-review` plutôt que `/sec`
2. **Groupez par domaine** : Utilisez des namespaces comme `/frontend:`, `/backend:`
3. **Documentez vos commandes** : Ajoutez des commentaires explicatifs
4. **Versionnez les commandes projet** : Incluez-les dans votre contrôle de version

### Optimisation des Prompts

1. **Soyez spécifique** : Des instructions précises donnent de meilleurs résultats
2. **Structurez vos prompts** : Utilisez des listes et des sections claires
3. **Anticipez les cas d'usage** : Prévoyez différents scénarios avec `$ARGUMENTS`
4. **Testez régulièrement** : Vérifiez que vos commandes produisent les résultats attendus

### Gestion d'Équipe

1. **Standardisez les commandes** : Créez un ensemble cohérent pour l'équipe
2. **Documentez l'usage** : Maintenez une liste des commandes disponibles
3. **Révisez périodiquement** : Mettez à jour et optimisez vos commandes
4. **Partagez les bonnes pratiques** : Formez l'équipe à l'utilisation efficace

## Découverte et Utilisation

### Lister les Commandes Disponibles
```bash
> /help
```
Cette commande affiche toutes les slash commands disponibles avec leurs descriptions et leur scope.

### Autocomplétion
Tapez `/` dans l'interface de Claude Code pour voir toutes les commandes disponibles. L'autocomplétion vous aide à découvrir et utiliser les commandes efficacement.

### Spécification de Modèle
Vous pouvez spécifier quel modèle utiliser pour une commande spécifique :
```bash
> /optimize --model=claude-3-opus
```

## Collections de Commandes Prêtes à l'Emploi

La communauté a développé plusieurs collections de commandes :

- **Production-ready commands** : 52 commandes organisées en workflows et outils
- **Claude Command Suite** : 119+ commandes et 54 agents IA
- **Session continuity commands** : Commandes pour maintenir la continuité entre sessions

## Intégration dans le Workflow de Développement

### Workflow Type avec Slash Commands

1. **Initialisation** : `/session-start` pour configurer l'environnement
2. **Développement** : `/feature-plan` pour planifier, `/build` pour construire
3. **Qualité** : `/lint-fix`, `/test-generate`, `/security-review`
4. **Finalisation** : `/pr-create`, `/deploy-check`

### Mesure de l'Efficacité

Les slash commands permettent de :
- **Réduire le temps de setup** de 60-80% pour les tâches répétitives
- **Standardiser les processus** à travers l'équipe
- **Maintenir la cohérence** dans la qualité du code
- **Accélérer l'onboarding** des nouveaux développeurs

Les slash commands de Claude Code transforment votre expérience de développement en automatisant les tâches répétitives et en standardisant les workflows. Commencez par créer quelques commandes simples pour vos tâches quotidiennes, puis évoluez vers des systèmes plus complexes avec namespacing et arguments dynamiques. L'investissement initial dans la création de bonnes commandes se traduit rapidement par des gains de productivité significatifs pour vous et votre équipe.