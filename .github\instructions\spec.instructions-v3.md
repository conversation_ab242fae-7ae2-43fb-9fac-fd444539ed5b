# Instructions de Génération des Spécifications SDD

## Objectif et Portée

Ce document définit les instructions globales pour la génération automatisée des trois fichiers de spécification SDD dans le répertoire `sdd/specs/{feature_name}/` :
- `requirements.md` - Documentation des exigences fonctionnelles et non-fonctionnelles
- `design.md` - Architecture et conception détaillée du système
- `tasks.md` - Planification des tâches et jalons d'implémentation

## Méthodologie SDD

La méthodologie SDD (Specification-Driven Development) impose une approche séquentielle stricte avec validation obligatoire à chaque étape :

1. **Phase Requirements** : Analyse et documentation des exigences métier selon le format EARS
2. **Phase Design** : Conception technique avec diagrammes Mermaid et spécifications d'architecture
3. **Phase Tasks** : Planification détaillée avec estimations et dépendances

**Règle Fondamentale** : La progression vers l'étape suivante n'est autorisée QUE après approbation explicite de l'utilisateur sur l'étape courante.

## Templates de Référence

Les fichiers de spécification DOIVENT être générés en utilisant les templates standardisés :

- **Requirements** : `sdd/templates/requirements.template.md`
  - Structure EARS pour les exigences fonctionnelles
  - Catégorisation des exigences non-fonctionnelles
  - Critères d'acceptation mesurables

- **Design** : `sdd/templates/design.template.md`
  - Architecture système avec diagrammes Mermaid
  - Spécifications des interfaces et API
  - Patterns de conception et contraintes techniques

- **Tasks** : `sdd/templates/tasks.template.md`
  - Décomposition en tâches atomiques
  - Estimations et dépendances
  - Jalons et critères de validation

### Cohérence avec la Phase 1
- **Context Instructions** : `.github/instructions/context.instructions.md` (si existant)
- **Context Prompt** : `.github/prompts/context.prompt.md` (si existant)
- **Context Chatmode** : `.github/chatmodes/context.chatmode.md` (si existant)

**Note** : La phase 2 (spécifications) s'appuie sur les patterns établis en phase 1 (contexte) pour maintenir la cohérence du workflow SDD global.

## Processus Obligatoire

### Étape 1 : Vérification des Prérequis
- Confirmer l'existence du répertoire `sdd/specs/`
- Valider le nom de la fonctionnalité (format kebab-case)
- Vérifier la disponibilité des templates de référence

### Étape 2 : Génération Initiale
- Générer le fichier complet sans poser de questions séquentielles
- Utiliser TOUTES les sections du template approprié
- Appliquer les formats spécifiques (EARS, Mermaid, checklist)
- Maintenir la cohérence terminologique avec le projet

### Étape 3 : Utilisation Complète des Templates
- Respecter la structure exacte des templates
- Remplir chaque section avec un contenu substantiel
- Adapter les exemples aux spécificités de la fonctionnalité
- Conserver les "Instructions Intégrées pour l'Agent IA"

### Étape 4 : Validation Utilisateur Obligatoire
- Présenter le document généré avec une checklist détaillée
- Utiliser le format de validation standardisé
- Attendre la réponse explicite de l'utilisateur
- Implémenter les cycles feedback-révision jusqu'à approbation

### Étape 5 : Progression Conditionnelle
- Passer à l'étape suivante UNIQUEMENT après approbation explicite
- Maintenir la traçabilité entre les phases
- Documenter les décisions et modifications

## Contraintes Techniques

### Structure des Fichiers
- **Chemin** : `sdd/specs/{feature_name}/`
- **Nommage** : kebab-case pour les noms de fonctionnalités
- **Extensions** : `.md` pour tous les fichiers de spécification

### Workflow Séquentiel Obligatoire
1. Requirements → Validation → Approbation
2. Design → Validation → Approbation  
3. Tasks → Validation → Approbation

### Formats Spécifiques
- **EARS** : "The system SHALL/SHOULD/MAY..." pour les requirements
- **Mermaid** : Diagrammes intégrés pour l'architecture et les flux
- **Checklist** : Format checkbox pour les tasks et validations

## Validation et Révision

### Format de Validation Standardisé

**Pour Requirements :**
```
**Validation Requirements :** J'ai terminé le document requirements.md avec :
- ✅ Exigences fonctionnelles au format EARS
- ✅ Exigences non-fonctionnelles catégorisées
- ✅ Critères d'acceptation mesurables
- ✅ Contraintes et hypothèses documentées
- ✅ Traçabilité avec les objectifs métier

Cette documentation des exigences vous convient-elle ?
```

**Pour Design :**
```
**Validation Design :** J'ai terminé le document design.md avec :
- ✅ Architecture système avec diagrammes Mermaid
- ✅ Spécifications des interfaces et API
- ✅ Modèles de données et schémas
- ✅ Patterns de conception appliqués
- ✅ Contraintes techniques documentées

Cette documentation de design vous convient-elle ?
```

**Pour Tasks :**
```
**Validation Tasks :** J'ai terminé le document tasks.md avec :
- ✅ Décomposition en tâches atomiques
- ✅ Estimations de charge et complexité
- ✅ Dépendances et ordre d'exécution
- ✅ Jalons et critères de validation
- ✅ Ressources et responsabilités

Cette documentation des tâches vous convient-elle ?
```

### Questions Suggérées pour la Révision
- "Souhaitez-vous modifier certaines sections ?"
- "Y a-t-il des aspects manquants ou à préciser ?"
- "Les estimations vous paraissent-elles réalistes ?"
- "La structure proposée répond-elle à vos attentes ?"

### Cycle Feedback-Révision
1. Présentation de la validation avec checklist
2. Attente de la réponse utilisateur
3. Si modifications demandées → révision → nouvelle validation
4. Si approbation → progression à l'étape suivante
5. Répétition jusqu'à satisfaction complète

## Intégration GitHub Copilot

### Limites de Contexte
- **Seuil Critique** : 80% du contexte token maximum autorisé
- **Monitoring Continu** : Vérification automatique du taux d'utilisation contexte
- **Alertes Préventives** : Signalement à 70% et arrêt forcé à 85%
- **Stratégies de Fallback** : Génération par sections si limite atteinte

### Stratégies de Chunking
- **Chunking Sémantique** : Découpage par sections logiques des templates
- **Chunking Hiérarchique** : Priorisation requirements > design > tasks
- **Chunking Adaptatif** : Ajustement dynamique selon la complexité
- **Chunking par Phases** : Génération séquentielle pour préserver le contexte

### Optimisations pour l'IA
- **Cache Intelligent** : Mémorisation des éléments validés précédemment
- **Références Compressées** : Liens symboliques vers templates plutôt que contenu complet
- **Extraction Ciblée** : Focus sur les sections en cours de modification
- **Synthèse Progressive** : Accumulation incrémentale des décisions validées

## Gestion des Erreurs

### Stratégies de Fallback
- **Niveau 1** : Utilisation des templates par défaut si templates spécialisés indisponibles
- **Niveau 2** : Génération basique sans template avec structure minimale garantie
- **Niveau 3** : Escalade vers validation manuelle avec assistance utilisateur

### Gestion des Conflits
- **Conflit de Templates** : Priorisation des templates les plus récents
- **Conflit de Versions** : Utilisation de la version stable de référence
- **Conflit de Contexte** : Réduction automatique du scope fonctionnel

### Récupération d'Erreurs
- **Sauvegarde Progressive** : Préservation des sections validées
- **Restauration Partielle** : Reprise au dernier point de validation
- **Régénération Ciblée** : Nouvelle génération des sections défaillantes uniquement

## Maintenance

### Mise à Jour des Templates
- Synchroniser les modifications des templates avec ces instructions
- Maintenir la cohérence des formats de validation
- Documenter les évolutions de la méthodologie SDD

### Évolution des Processus
- Intégrer les retours d'expérience utilisateur
- Optimiser les cycles de validation
- Améliorer la qualité des documents générés

### Traçabilité
- Conserver l'historique des modifications
- Documenter les décisions d'architecture
- Maintenir les liens entre requirements, design et tasks

---

**Rappel Important** : Ces instructions DOIVENT être suivies strictement. Toute dérogation au processus de validation explicite est interdite. La progression incrémentale avec approbation utilisateur est la règle fondamentale de la méthodologie SDD.
