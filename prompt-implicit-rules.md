# Prompt Implicit Rules

## Règles implicites pour les différentes phases

### Phase initiale
- Identifier le nom de la fonctionnalité en format kebab-case
- Créer la structure de répertoire `.kiro/specs/{feature_name}/`
- Ne pas mentionner explicitement le workflow à l'utilisateur

### Phase des exigences
- Générer une version initiale complète avant de demander des retours
- Utiliser le format EARS pour les critères d'acceptation
- Inclure des user stories pour chaque exigence
- Demander l'approbation avec la raison 'spec-requirements-review'

### Phase de design
- Effectuer les recherches nécessaires en contexte
- Intégrer les résultats de recherche directement dans le design
- Suivre le template de design fourni
- Demander l'approbation avec la raison 'spec-design-review'

### Phase du plan d'implémentation
- Créer une checklist de tâches exploitables
- Baser le plan sur les exigences et le design approuvés
- Utiliser le format spécifié dans les instructions
- Demander l'approbation avec la raison 'spec-implementation-plan-review'

### Règles générales
- Toujours attendre l'approbation explicite avant de continuer
- Itérer sur les retours jusqu'à satisfaction
- Maintenir la cohérence entre tous les documents
- Documenter les décisions importantes et leurs justifications