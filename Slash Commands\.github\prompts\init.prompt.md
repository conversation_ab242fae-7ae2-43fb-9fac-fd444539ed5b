Veuillez analyser cette base de code et créer un fichier **CRUSH.md** contenant :

- Les commandes de build/lint/test – en particulier pour exécuter un seul test
- Les règles de style de code, y compris les imports, le formatage, les types, les conventions de nommage, la gestion des erreurs, etc.

Le fichier que vous créez sera utilisé par des agents de codage autonomes (comme vous) qui opèrent dans ce dépôt. Il doit comporter environ 20 à 30 lignes.
S'il existe déjà un **CRUSH.md**, améliorez-le.

Si des règles Cursor (dans `.cursor/rules/` ou `.cursorrules`) ou des règles Copilot (dans `.github/copilot-instructions.md`) sont présentes, veillez à les inclure.
Ajoutez le répertoire `.crush` au fichier `.gitignore` s'il n'y figure pas déjà.
