# Stack Technique

## Technologies Principales

- **Markdown** : Format principal pour la documentation et les spécifications
- **Syntaxe EARS** : Framework structuré pour les exigences (Easy Approach to Requirements Syntax)
- **Workflows Git** : Gestion de versions et collaboration
- **Assistants IA** : <PERSON>, <PERSON><PERSON><PERSON><PERSON> Copilot, <PERSON><PERSON><PERSON>, Gemini CLI

## Outils de Développement

### Assistants IA Supportés
- **Claude** : Workflows spec avec commandes personnalisées
- **GitHub Copilot** : Intégration IDE avec contexte workspace
- **Cursor** : IDE conscient du codebase avec indexation sémantique
- **Gemini CLI** : Interface en ligne de commande pour l'IA Google

### Structure des Projets
```
.spec/
├── features/           # Spécifications par fonctionnalité
├── context/           # Contexte global du projet
└── reports/           # Rapports de traçabilité

.claude/
├── specs/             # Spécifications Claude
├── commands/          # Commandes personnalisées
└── scripts/           # Scripts d'automatisation
```

## Commandes Courantes

### Workflow SPEC
```bash
# Créer une nouvelle spécification
/spec-create <nom-fonctionnalité>

# Générer les exigences
/spec-requirements

# Créer la conception
/spec-design

# Planifier les tâches
/spec-tasks

# Exécuter une tâche
/spec-execute <id-tâche>
```

### Gestion des Fichiers
- Utiliser les références `#file:` pour inclure des fichiers spécifiques
- Utiliser `@workspace` pour le contexte global
- Structurer les documents avec des balises XML pour la clarté

## Standards de Qualité

- **Syntaxe EARS** obligatoire pour les exigences
- **Traçabilité** : Toutes les tâches doivent référencer des exigences
- **Documentation vivante** : Synchronisation specs-implémentation
- **Validation** : Tests et vérifications à chaque étape