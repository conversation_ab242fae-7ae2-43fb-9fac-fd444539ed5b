# Stack Technique et Outils

## Stack Technologique

### Langages de Programmation
- **JavaScript/TypeScript** : Langage principal pour le développement
- **Python** : Scripts d'automatisation et traitement de données
- **Markdown** : Documentation et spécifications
- **Mermaid** : Diagrammes et visualisations

### Frameworks et Bibliothèques
- **Node.js** : Runtime JavaScript côté serveur
- **React** : Interface utilisateur (si applicable)
- **Express.js** : Framework web léger
- **Jest** : Framework de tests

### Outils de Développement
- **Git** : Contrôle de version
- **VS Code** : Éditeur de code recommandé
- **ESLint** : Linting JavaScript/TypeScript
- **Prettier** : Formatage de code

### Agents IA Supportés
- **Claude (Anthropic)** : Agent principal recommandé
- **GPT-4 (OpenAI)** : Alternative compatible
- **Cursor** : IDE avec IA intégrée
- **GitHub Copilot** : Assistant de code

## Contraintes Techniques

### Limites des Modèles IA
- **Contexte maximum** : Optimiser pour rester < 80% de la limite
- **Tokens de sortie** : Limiter les réponses pour éviter la troncature
- **Mémoire de session** : Utiliser la structure SDD comme mémoire externe

### Performance
- **Temps de réponse** : < 30 secondes par phase
- **Taille des documents** : < 5000 mots par fichier
- **Complexité** : Max 3 niveaux de hiérarchie

### Compatibilité
- **Cross-platform** : Windows, macOS, Linux
- **Encodage** : UTF-8 obligatoire
- **Formats** : Markdown standard (CommonMark)

## Standards de Développement

### Conventions de Code
- **Indentation** : 2 espaces (JavaScript/TypeScript)
- **Longueur de ligne** : 80 caractères max
- **Nommage** : camelCase pour variables, PascalCase pour classes
- **Comments** : JSDoc pour les fonctions publiques

### Structure des Fichiers
```
project-root/
├── sdd/                    # Système SDD
├── src/                    # Code source
├── tests/                  # Tests unitaires
├── docs/                   # Documentation
├── package.json           # Dépendances Node.js
├── tsconfig.json          # Configuration TypeScript
└── README.md              # Documentation projet
```

### Gestion des Dépendances
- **Package Manager** : npm ou yarn
- **Versions** : Semantic versioning (semver)
- **Lock files** : Toujours commiter package-lock.json
- **Audit** : Vérifier régulièrement les vulnérabilités

## Outils et Commandes

### Commandes de Base
```bash
# Installation des dépendances
npm install

# Démarrage du développement
npm run dev

# Tests
npm test
npm run test:watch
npm run test:coverage

# Build de production
npm run build

# Linting et formatage
npm run lint
npm run lint:fix
npm run format
```

### Scripts NPM Recommandés
```json
{
  "scripts": {
    "dev": "node --watch src/index.js",
    "build": "tsc",
    "test": "jest",
    "test:watch": "jest --watch",
    "test:coverage": "jest --coverage",
    "lint": "eslint src/**/*.{js,ts}",
    "lint:fix": "eslint src/**/*.{js,ts} --fix",
    "format": "prettier --write src/**/*.{js,ts,md}",
    "sdd:validate": "node scripts/validate-sdd.js",
    "sdd:generate": "node scripts/generate-docs.js"
  }
}
```

### Configuration ESLint
```json
{
  "extends": [
    "eslint:recommended",
    "@typescript-eslint/recommended"
  ],
  "rules": {
    "max-len": ["error", { "code": 80 }],
    "no-console": "warn",
    "prefer-const": "error"
  }
}
```

### Configuration Prettier
```json
{
  "semi": true,
  "trailingComma": "es5",
  "singleQuote": true,
  "printWidth": 80,
  "tabWidth": 2
}
```

## Intégration Continue

### GitHub Actions
```yaml
name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      - run: npm ci
      - run: npm run lint
      - run: npm test
      - run: npm run sdd:validate
```

### Hooks Git
```json
{
  "husky": {
    "hooks": {
      "pre-commit": "lint-staged",
      "commit-msg": "commitlint -E HUSKY_GIT_PARAMS"
    }
  },
  "lint-staged": {
    "*.{js,ts}": ["eslint --fix", "prettier --write"],
    "*.md": ["prettier --write"]
  }
}
```

## Sécurité

### Bonnes Pratiques
- **Secrets** : Utiliser des variables d'environnement
- **Validation** : Valider toutes les entrées utilisateur
- **Audit** : Scanner régulièrement les dépendances
- **HTTPS** : Toujours utiliser des connexions sécurisées

### Variables d'Environnement
```bash
# .env.example
NODE_ENV=development
PORT=3000
API_KEY=your_api_key_here
DATABASE_URL=your_database_url_here
```

### Gestion des Secrets
- **Développement** : Fichiers .env (non versionnés)
- **Production** : Variables d'environnement du système
- **CI/CD** : Secrets GitHub/GitLab
- **Rotation** : Changer régulièrement les clés

## Monitoring et Logging

### Logging
```javascript
// Configuration Winston
const winston = require('winston');

const logger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  ),
  transports: [
    new winston.transports.File({ filename: 'error.log', level: 'error' }),
    new winston.transports.File({ filename: 'combined.log' })
  ]
});
```

### Métriques
- **Performance** : Temps de réponse des APIs
- **Erreurs** : Taux d'erreur et types d'exceptions
- **Usage** : Utilisation des fonctionnalités
- **Ressources** : CPU, mémoire, stockage

## Base de Données

### Recommandations
- **Développement** : SQLite ou PostgreSQL local
- **Production** : PostgreSQL ou MySQL
- **NoSQL** : MongoDB si besoin de flexibilité
- **Cache** : Redis pour les données temporaires

### Migrations
```javascript
// Exemple avec Knex.js
exports.up = function(knex) {
  return knex.schema.createTable('users', function(table) {
    table.increments('id');
    table.string('email').unique().notNullable();
    table.string('password').notNullable();
    table.timestamps(true, true);
  });
};

exports.down = function(knex) {
  return knex.schema.dropTable('users');
};
```

## API Design

### REST Conventions
- **URLs** : Noms au pluriel (/users, /products)
- **Méthodes HTTP** : GET, POST, PUT, DELETE
- **Status Codes** : 200, 201, 400, 401, 404, 500
- **Content-Type** : application/json

### Exemple d'API
```javascript
// Routes utilisateurs
app.get('/api/users', getAllUsers);
app.get('/api/users/:id', getUserById);
app.post('/api/users', createUser);
app.put('/api/users/:id', updateUser);
app.delete('/api/users/:id', deleteUser);
```

### Documentation API
- **OpenAPI/Swagger** : Spécification standard
- **Postman** : Collections de tests
- **Insomnia** : Alternative à Postman

## Tests

### Stratégie de Tests
- **Unitaires** : 70% de couverture minimum
- **Intégration** : Tests des APIs
- **E2E** : Parcours utilisateur critiques
- **Performance** : Tests de charge

### Configuration Jest
```json
{
  "testEnvironment": "node",
  "collectCoverageFrom": [
    "src/**/*.{js,ts}",
    "!src/**/*.test.{js,ts}"
  ],
  "coverageThreshold": {
    "global": {
      "branches": 70,
      "functions": 70,
      "lines": 70,
      "statements": 70
    }
  }
}
```

## Déploiement

### Environnements
- **Development** : Local avec hot-reload
- **Staging** : Environnement de test
- **Production** : Environnement live

### Stratégies de Déploiement
- **Blue-Green** : Déploiement sans interruption
- **Rolling** : Mise à jour progressive
- **Canary** : Test sur un sous-ensemble d'utilisateurs

### Conteneurisation
```dockerfile
# Dockerfile
FROM node:18-alpine

WORKDIR /app

COPY package*.json ./
RUN npm ci --only=production

COPY . .

EXPOSE 3000

CMD ["npm", "start"]
```

## Documentation

### Structure Recommandée
```
docs/
├── README.md              # Vue d'ensemble
├── CONTRIBUTING.md        # Guide de contribution
├── DEPLOYMENT.md          # Guide de déploiement
├── API.md                 # Documentation API
├── ARCHITECTURE.md        # Architecture technique
└── TROUBLESHOOTING.md     # Guide de dépannage
```

### Outils de Documentation
- **GitBook** : Documentation interactive
- **Docusaurus** : Site de documentation
- **JSDoc** : Documentation du code
- **Mermaid** : Diagrammes techniques