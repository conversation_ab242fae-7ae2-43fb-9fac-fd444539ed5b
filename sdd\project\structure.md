# Structure du Projet SDD

## Vue d'ensemble

Ce document définit la structure standardisée des projets utilisant le système SDD (Spec-Driven Development). Cette structure garantit une organisation cohérente et facilite la navigation pour les agents IA et les développeurs.

## Architecture des Dossiers

```
project-root/
├── sdd/                          # Système SDD complet
│   ├── project/                  # Contexte du projet actuel
│   │   ├── structure.md          # Ce fichier - structure projet
│   │   ├── context.md            # Contexte technique et métier
│   │   └── guidelines.md         # Directives spécifiques au projet
│   ├── templates/                # Templates pour agents IA
│   │   ├── requirements.template.md
│   │   ├── design.template.md
│   │   ├── tasks.template.md
│   │   ├── task-execution.template.md
│   │   └── system-prompt.template.md
│   └── specs/                    # Spécifications par fonctionnalité
│       └── [feature-name]/       # Une fonctionnalité
│           ├── requirements.md   # Exigences EARS
│           ├── design.md         # Design technique
│           └── tasks.md          # Plan d'implémentation
├── src/                          # Code source principal
├── tests/                        # Tests automatisés
├── docs/                         # Documentation projet
└── [autres dossiers projet]      # Structure spécifique au projet
```

## Dossier sdd/project/

### Objectif
Contient le contexte global du projet pour guider les agents IA dans leurs décisions d'implémentation.

### Fichiers Obligatoires

#### structure.md (ce fichier)
- **Rôle** : Définit l'organisation des dossiers et fichiers
- **Contenu** : Architecture, conventions de nommage, emplacements
- **Mise à jour** : À chaque modification structurelle majeure

#### context.md
- **Rôle** : Contexte technique et métier du projet
- **Contenu** : Stack technique, contraintes, objectifs business
- **Mise à jour** : Lors de changements d'architecture ou d'objectifs

#### guidelines.md
- **Rôle** : Directives spécifiques au projet
- **Contenu** : Standards de code, patterns, bonnes pratiques
- **Mise à jour** : Évolution des standards et retours d'expérience

### Fichiers Optionnels

#### dependencies.md
- **Rôle** : Gestion des dépendances et versions
- **Contenu** : Librairies approuvées, versions, justifications

#### deployment.md
- **Rôle** : Stratégie et configuration de déploiement
- **Contenu** : Environnements, CI/CD, monitoring

#### security.md
- **Rôle** : Directives de sécurité spécifiques
- **Contenu** : Authentification, autorisation, chiffrement

## Dossier sdd/specs/

### Organisation par Fonctionnalité
Chaque fonctionnalité majeure dispose de son propre sous-dossier avec la triade complète :
- `requirements.md` : Exigences au format EARS
- `design.md` : Architecture et design technique
- `tasks.md` : Plan d'implémentation détaillé

### Conventions de Nommage
- **Dossiers** : kebab-case (ex: `user-authentication`, `payment-processing`)
- **Fichiers** : Noms fixes (`requirements.md`, `design.md`, `tasks.md`)
- **Cohérence** : Même nom de dossier dans toute la documentation

### Exemples de Structure
```
sdd/specs/
├── user-authentication/
│   ├── requirements.md
│   ├── design.md
│   └── tasks.md
├── payment-processing/
│   ├── requirements.md
│   ├── design.md
│   └── tasks.md
└── notification-system/
    ├── requirements.md
    ├── design.md
    └── tasks.md
```

## Dossier sdd/templates/

### Templates pour Agents IA
Contient les templates utilisés par les agents IA pour générer les documents de spécification.

### Fichiers Templates
- **requirements.template.md** : Guide pour créer requirements.md
- **design.template.md** : Guide pour créer design.md
- **tasks.template.md** : Guide pour créer tasks.md
- **task-execution.template.md** : Guide pour exécuter les tâches
- **system-prompt.template.md** : Prompt système pour agents IA

## Intégration avec le Code Source

### Liens avec src/
- **Traçabilité** : Chaque composant dans `src/` doit référencer sa spec
- **Tests** : Tests dans `tests/` suivent la structure des specs
- **Documentation** : `docs/` peut référencer les specs pour détails techniques

### Conventions de Référencement
```typescript
// Exemple dans le code source
/**
 * Service d'authentification
 * @spec sdd/specs/user-authentication/
 * @requirements REQ-1, REQ-2
 * @design AuthService component
 */
export class AuthService {
  // Implementation
}
```

## Workflow de Développement

### Cycle de Vie d'une Fonctionnalité
1. **Création** : Nouveau dossier dans `sdd/specs/[feature]/`
2. **Requirements** : Rédaction `requirements.md` avec format EARS
3. **Design** : Création `design.md` basé sur requirements
4. **Planning** : Génération `tasks.md` depuis design
5. **Implémentation** : Exécution des tâches avec traçabilité
6. **Validation** : Tests et validation contre requirements

### Maintenance
- **Évolution** : Mise à jour des 3 fichiers en cascade
- **Refactoring** : Révision design → tasks → implémentation
- **Bugs** : Analyse requirements → design pour corrections

## Bonnes Pratiques

### Organisation
- **Atomicité** : Une fonctionnalité = un dossier spec
- **Cohérence** : Même terminologie dans tous les documents
- **Traçabilité** : Références croisées entre documents

### Maintenance
- **Versioning** : Git pour historique des changements
- **Reviews** : Validation des specs avant implémentation
- **Documentation** : Mise à jour synchrone code/specs

### Collaboration
- **Standards** : Respect des templates et formats
- **Communication** : Specs comme référence commune
- **Onboarding** : Structure claire pour nouveaux développeurs

## Outils et Automatisation

### Scripts Utiles
```bash
# Créer une nouvelle fonctionnalité
npm run sdd:create-feature [feature-name]

# Valider la structure SDD
npm run sdd:validate

# Générer documentation depuis specs
npm run sdd:docs
```

### Intégration CI/CD
- **Validation** : Vérification structure SDD dans pipeline
- **Tests** : Exécution tests basés sur requirements
- **Documentation** : Génération automatique docs depuis specs

## Migration vers SDD

### Projets Existants
1. **Audit** : Analyse structure actuelle
2. **Mapping** : Identification des fonctionnalités existantes
3. **Extraction** : Création specs depuis code existant
4. **Validation** : Tests de cohérence specs/code
5. **Adoption** : Formation équipe et processus

### Nouveaux Projets
1. **Setup** : Création structure SDD complète
2. **Context** : Rédaction fichiers project/
3. **Templates** : Personnalisation si nécessaire
4. **Formation** : Équipe sur processus SDD

---

*Cette structure garantit une approche systématique et traçable du développement, facilitant la collaboration entre agents IA et développeurs humains.*