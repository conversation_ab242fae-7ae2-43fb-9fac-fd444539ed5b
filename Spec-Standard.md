# Spec-Standard : Système d'Instructions SDD (SpecDrivenDevelopment) - Version Standard

## Vue d'Ensemble

Ce document présente le système d'instructions d'agent IA qui combine les concepts de **persistance de contexte** et **SpecDrivenDevelopment** (workflow structuré de spécification) dans sa **Version Standard** utilisant une structure de dossiers `sdd/` avec `project/` et `specs/`.

### Problématique

- Les agents IA sont facturés par requête
- Il faut maximiser la production de documents .md en une seule session
- Besoin de validation utilisateur sans déclencher de nouvelles requêtes
- Maintenir la cohérence et la qualité du workflow de spécification

### Solution Proposée

Utilisation d'un système de validation intégré permettant à l'agent de poser des questions et obtenir des validations via des interactions directes dans la conversation sans stopper l'exécution ni déclencher de nouvelles requêtes.

---

## Templates de Documents

### Structure des Templates

La structure SDD utilise des templates standardisés basés sur les fichiers `prompt-spec-*.md` existants pour garantir la cohérence et la qualité des documents générés :

#### Templates Contexte Projet
- **product.template.md** : Basé sur `steering/product.md`
  - Vision et objectifs du produit
  - Public cible et personas
  - Proposition de valeur unique
  - Métriques de succès

- **structure.template.md** : Basé sur `steering/structure.md`
  - Architecture générale du projet
  - Organisation des modules
  - Conventions de nommage
  - Structure des dossiers

- **tech.template.md** : Basé sur `steering/tech.md`
  - Stack technologique
  - Contraintes techniques
  - Standards de développement
  - Outils et frameworks

#### Templates Spécifications avec Instructions Précises

##### **requirements.template.md**
Basé sur `prompt-spec-requirements-example.md` et `prompt-spec-workflow-requirement-clarification.md`

**Instructions intégrées :**
- Génération initiale basée sur l'idée utilisateur SANS questions séquentielles
- Format EARS (Easy Approach to Requirements Syntax) obligatoire
- Structure hiérarchique numérotée avec :
  - User stories : "En tant que [rôle], je veux [fonctionnalité], afin que [bénéfice]"
  - Critères d'acceptation au format EARS
- Prise en compte des cas limites, UX, contraintes techniques
- Cycle feedback-révision jusqu'à approbation explicite
- Validation obligatoire avant passage au design

##### **design.template.md**
Basé sur `prompt-spec-workflow-design-template.md` et `prompt-spec-workflow-research-design.md`

**Instructions intégrées :**
- Recherche contextuelle intégrée (pas de fichiers séparés)
- Sections obligatoires du template design
- Diagrammes Mermaid si approprié
- Justification des décisions de design
- Réponse à toutes les exigences identifiées
- Cycle feedback-révision jusqu'à approbation explicite
- Validation obligatoire avant passage aux tâches

##### **tasks.template.md**
Basé sur `prompt-spec-workflow-example-plan.md` et `prompt-spec-workflow-implementation-plan.md`

**Instructions intégrées :**
- Conversion du design en prompts LLM orientés test
- Format liste numérotée à cocher (max 2 niveaux)
- Notation décimale pour sous-tâches (1.1, 1.2, 2.1)
- Chaque tâche doit inclure :
  - Objectif clair (écriture/modification/test de code)
  - Informations supplémentaires en sous-puces
  - Références spécifiques aux exigences
- Progression incrémentale sans code orphelin
- Une tâche à la fois, arrêt pour révision utilisateur

#### Instructions Workflow Complètes

**Basées sur `prompt-spec-workflow-overview.md` :**
- Méthodologie développement piloté par spécifications
- Validation utilisateur obligatoire à chaque étape
- Nom de fonctionnalité en kebab-case
- Processus itératif requirements → design → tasks

**Dépannage (`prompt-spec-workflow-troubleshooting.md`) :**
- Gestion des blocages lors de clarification
- Approches alternatives si informations manquantes
- Découpage en composants si complexité excessive

**Exécution des tâches (`prompt-spec-task-background.md`) :**
- Lecture obligatoire requirements.md, design.md, tasks.md
- Une tâche à la fois, arrêt pour révision
- Vérification contre toutes les exigences spécifiées

### Utilisation des Templates

**Version Standard :**
```
Agent: "Je vais créer requirements.md en utilisant le template sdd/templates/requirements.template.md"
```

---

## Architecture "Session Unifiée avec Validation Intégrée"

### Principe Fondamental

Une seule "macro-tâche" lancée à l'agent IA qui suit le processus SpecDrivenDevelopment de bout en bout, utilisant la structure SDD comme mémoire de travail et des validations intégrées dans la conversation.

### Structure SDD

```
sdd/
├── project/                 # Contexte global du projet
│   ├── product.md          # Vision produit et objectifs
│   ├── structure.md        # Architecture et organisation
│   └── tech.md             # Stack technique et outils
├── templates/              # Templates pour tous les documents
│   ├── product.template.md    # Template basé sur steering/product.md
│   ├── structure.template.md  # Template basé sur steering/structure.md
│   ├── tech.template.md       # Template basé sur steering/tech.md
│   ├── requirements.template.md # Template EARS (prompt-spec-requirements-example.md)
│   ├── design.template.md     # Template conception (prompt-spec-workflow-design-template.md)
│   └── tasks.template.md      # Template plan d'implémentation (prompt-spec-workflow-example-plan.md)
└── specs/                  # Spécifications par fonctionnalité
    └── {feature_name}/
        ├── requirements.md # Exigences EARS
        ├── design.md      # Design détaillé
        └── tasks.md       # Plan d'implémentation
```

### Architecture Technique

```mermaid
flowchart TD
    A[Utilisateur : Idée de fonctionnalité] --> B[Agent : Chargement contexte SDD complet]
    B --> C[Phase 1: Clarification des exigences]
    C --> D{Besoin de validation?}
    D -->|Oui| E[Agent: Pose question dans conversation]
    E --> F[Utilisateur répond directement]
    F --> G[Agent reçoit réponse et continue]
    G --> C
    D -->|Non| H[Phase 2: Recherche & Design]
    H --> I{Besoin de validation?}
    I -->|Oui| E
    I -->|Non| J[Phase 3: Plan d'implémentation]
    J --> K{Besoin de validation?}
    K -->|Oui| E
    K -->|Non| L[Génération documents .md finaux]
    L --> M[Session terminée - Tous documents produits]
```

### Composants Clés

#### Prompt Système Hybride

```markdown
# INSTRUCTIONS SYSTÈME HYBRIDE SDD

Tu es un ingénieur senior spécialisé dans la transformation d'idées en spécifications détaillées.

## RÔLE ET MISSION
- Transformer une idée de fonctionnalité en plan d'implémentation complet
- Suivre rigoureusement les 3 phases du SpecDrivenDevelopment
- Utiliser la structure SDD comme source de vérité et mémoire de travail
- Maximiser la production de documents .md en une seule session

## PROCESSUS OBLIGATOIRE (basé sur prompt-spec-workflow-overview.md)
1. **Nom de fonctionnalité** : Format kebab-case obligatoire
2. **Phase 1 - Requirements** : Créer requirements.md avec méthodologie EARS
3. **Phase 2 - Design** : Créer design.md avec recherche intégrée
4. **Phase 3 - Tasks** : Créer tasks.md avec prompts LLM orientés test

### Phase 1: Requirements (prompt-spec-workflow-requirement-clarification.md)
- Génération initiale basée sur l'idée utilisateur SANS questions séquentielles
- Format EARS (Easy Approach to Requirements Syntax) obligatoire
- Structure hiérarchique numérotée avec :
  - User stories : "En tant que [rôle], je veux [fonctionnalité], afin que [bénéfice]"
  - Critères d'acceptation au format EARS
- Prise en compte des cas limites, UX, contraintes techniques
- Cycle feedback-révision jusqu'à approbation explicite
- Validation obligatoire avant passage au design

### Phase 2: Design (prompt-spec-workflow-research-design.md)
- Recherche contextuelle intégrée (pas de fichiers séparés)
- Sections obligatoires du template design
- Diagrammes Mermaid si approprié
- Justification des décisions de design
- Réponse à toutes les exigences identifiées
- Cycle feedback-révision jusqu'à approbation explicite
- Validation obligatoire avant passage aux tâches

### Phase 3: Tasks (prompt-spec-workflow-implementation-plan.md)
- Conversion du design en prompts LLM orientés test
- Format liste numérotée à cocher (max 2 niveaux)
- Notation décimale pour sous-tâches (1.1, 1.2, 2.1)
- Chaque tâche doit inclure :
  - Objectif clair (écriture/modification/test de code)
  - Informations supplémentaires en sous-puces
  - Références spécifiques aux exigences
- Progression incrémentale sans code orphelin
- Une tâche à la fois, arrêt pour révision utilisateur

## CONTEXTE PROJET (sdd/project/)
Le contexte projet fourni est ta source de vérité. Tu DOIS :
- Lire product.md, structure.md, tech.md au début
- Respecter les contraintes et conventions définies
- Maintenir la cohérence avec l'architecture existante

## EXÉCUTION DES TÂCHES (prompt-spec-task-background.md)
- Lecture obligatoire requirements.md, design.md, tasks.md
- Une tâche à la fois, arrêt pour révision
- Vérification contre toutes les exigences spécifiées

## DÉPANNAGE (prompt-spec-workflow-troubleshooting.md)
- Gestion des blocages lors de clarification
- Approches alternatives si informations manquantes
- Découpage en composants si complexité excessive

## VALIDATION CONTINUE
Lorsque tu as besoin d'une clarification ou validation :
- Pose tes questions directement dans la conversation
- Utilise des formats clairs avec options multiples quand approprié
- Attends la réponse de l'utilisateur avant de continuer
- Continue jusqu'à completion complète des 3 phases
- Demande confirmation explicite avant de passer à la phase suivante

## RÈGLES IMPORTANTES
- Validation utilisateur obligatoire à chaque étape
- Ne mentionnez pas explicitement le workflow à l'utilisateur
- Informez simplement quand vous terminez un document
- Processus itératif avec retours possibles aux étapes précédentes
- Posez des questions claires et précises pour éviter les ambiguïtés

## SORTIE ATTENDUE
À la fin de la session, tu DOIS fournir :
- Les fichiers de spécification dans sdd/specs/{feature_name}/
- Un résumé des décisions prises
```

#### Structure SDD Complète

```
sdd/
├── project/                  # Contexte global du projet
│   ├── product.md           # Vision produit, objectifs, composants
│   ├── structure.md         # Architecture, organisation, conventions
│   └── tech.md              # Stack technique, outils, commandes
└── specs/                   # Spécifications par fonctionnalité
    └── {feature_name}/
        ├── requirements.md  # Exigences EARS
        ├── design.md       # Design détaillé
        └── tasks.md        # Plan d'implémentation
```

---

## Architectures Alternatives

### Architecture "Pipeline Intelligent avec Checkpoints"

#### Principe

Diviser le processus en micro-sessions avec des checkpoints automatiques, permettant une reprise intelligente en cas d'interruption.

#### Fonctionnement

1. **Initialisation** : L'agent analyse le contexte et détermine l'état actuel
2. **Planification** : Création d'un plan de session avec checkpoints
3. **Exécution par blocs** : Traitement par phases avec validation directe
4. **Sauvegarde continue** : Mise à jour des fichiers à chaque checkpoint
5. **Reprise intelligente** : Capacité à reprendre depuis le dernier checkpoint

#### Avantages

- Résilience aux interruptions
- Progression visible pour l'utilisateur
- Optimisation fine du contexte
- Gestion intelligente de la taille du contexte

### Architecture "Agent Orchestrateur Multi-Spécialisé"

#### Principe

Un agent orchestrateur qui coordonne des agents spécialisés pour chaque phase, tous partageant la même structure de fichiers.

#### Composants

1. **Agent Orchestrateur** : Gère le workflow global et les transitions
2. **Agent Requirements** : Spécialisé dans la clarification des exigences
3. **Agent Designer** : Expert en architecture et design
4. **Agent Planner** : Spécialisé dans la planification d'implémentation

#### Workflow

```mermaid
sequenceDiagram
    participant U as Utilisateur
    participant O as Orchestrateur
    participant R as Agent Requirements
    participant D as Agent Designer
    participant P as Agent Planner
    participant S as Structure SDD
    
    U->>O: Idée de fonctionnalité
    O->>S: Chargement contexte projet
    O->>R: Phase requirements
    R->>U: Validation directe
    R->>S: Création requirements.md
    O->>D: Phase design
    D->>U: Validation directe
    D->>S: Création design.md
    O->>P: Phase planning
    P->>U: Validation directe
    P->>S: Création tasks.md
    O->>U: Livraison documents finaux
```

### Architecture "Session Continue avec IA Conversationnelle"

#### Principe

Utiliser les capacités conversationnelles avancées pour maintenir un dialogue continu avec validation en temps réel.

#### Caractéristiques

- **Dialogue naturel** : L'agent pose des questions de clarification au fur et à mesure
- **Validation incrémentale** : Chaque section est validée avant de passer à la suivante
- **Adaptation dynamique** : Le workflow s'adapte selon les réponses utilisateur
- **Mémoire contextuelle** : Utilisation optimale de la structure SDD pour le contexte

#### Exemple de Flux

```
Agent: "J'ai analysé le contexte projet (sdd/project/). Basé sur votre stack technique 
et votre idée d'authentification, dois-je inclure l'authentification sociale ?

Options :
A) Oui, avec Google et GitHub
B) Oui, avec d'autres providers
C) Non, authentification locale uniquement
D) À définir plus tard"

Utilisateur: "A"

Agent: "Parfait. Je crée requirements.md dans sdd/specs/auth/... 
Pour la gestion des sessions, préférez-vous JWT ou sessions serveur ?

Options :
A) JWT (stateless)
B) Sessions serveur (stateful)
C) Hybride selon le contexte"

Utilisateur: "A"

Agent: "Excellent. Je finalise les exigences et passe au design..."
```

---

## Implémentation Recommandée

### Phase 1 : Prototype Minimal

1. **Créer le prompt système hybride**
2. **Implémenter la validation conversationnelle directe**
3. **Tester avec un cas simple**
4. **Itérer sur la qualité des questions/validations**

### Phase 2 : Optimisation

1. **Ajouter la gestion intelligente du contexte**
2. **Implémenter les checkpoints**
3. **Optimiser les prompts selon les retours**

### Phase 3 : Évolution

1. **Évaluer l'approche multi-agents**
2. **Intégrer les capacités conversationnelles avancées**
3. **Automatiser la gestion de la structure SDD**

---

## Stratégies de Validation

### 1. Questions Structurées

```markdown
**Question de validation :**
Pour la fonctionnalité d'authentification, quelle approche préférez-vous ?

A) Authentification locale avec email/mot de passe
B) Authentification sociale (Google, GitHub)
C) Authentification hybride (locale + sociale)
D) Authentification par token/API uniquement

Répondez par la lettre correspondante (A, B, C, ou D).
```

### 2. Validation Progressive

```markdown
**Validation étape par étape :**

1. **Contexte analysé** ✓
   - Stack technique : React + Node.js
   - Base de données : PostgreSQL
   - Authentification actuelle : Aucune

2. **Exigences identifiées** (à valider)
   - Inscription/connexion utilisateur
   - Gestion des profils
   - Réinitialisation mot de passe
   - Sessions sécurisées

**Confirmez-vous ces exigences ? (Oui/Non/Modifications)**
```

### 3. Checkpoints de Validation

```markdown
**Checkpoint Requirements :**
J'ai terminé le document requirements.md avec :
- 8 user stories principales
- 24 critères d'acceptation EARS
- Gestion des cas d'erreur
- Contraintes de sécurité

**Souhaitez-vous réviser avant de passer au design ? (Oui/Non)**
```

---

## Métriques de Succès

### Efficacité
- **Réduction des requêtes** : Objectif 80% de réduction (de 5-10 requêtes à 1-2)
- **Temps de session** : Maintenir < 30 minutes par fonctionnalité complète
- **Qualité des documents** : Validation par checklist qualité automatisée

### Expérience Utilisateur
- **Fluidité du dialogue** : Questions claires et options structurées
- **Pertinence des questions** : Taux de questions utiles > 90%
- **Complétude des livrables** : 100% des documents requis générés
- **Validation efficace** : Processus de validation intégré et fluide

### Technique
- **Utilisation du contexte** : Optimisation pour rester < 80% de la limite
- **Cohérence des documents** : Validation croisée automatique
- **Traçabilité** : Historique complet des décisions dans la structure SDD

---

## Conclusion

La Version Standard du système Spec-No-MCP offre :

1. **Structure de dossiers claire et portable** avec `sdd/`
2. **Contexte projet centralisé** (product.md, structure.md, tech.md)
3. **Spécifications organisées par fonctionnalité**
4. **Compatible avec tous les agents IA**
5. **Validation conversationnelle directe** sans dépendance externe

### Avantages Clés

- **Simplicité** : Structure de fichiers intuitive
- **Portabilité** : Fonctionne avec n'importe quel agent IA
- **Transparence** : Validation directe dans la conversation
- **Flexibilité** : Adaptation facile selon les besoins du projet
- **Économique** : Réduction significative du nombre de requêtes

Cette approche transforme l'agent IA d'un simple exécutant en un véritable partenaire de conception, capable de mener un projet de spécification de bout en bout de manière autonome et économique.