# Directives du Projet SDD

## Vue d'ensemble

Ce document établit les directives spécifiques au projet SDD pour garantir la cohérence, la qualité et l'efficacité du système. Ces directives complètent les templates et doivent être respectées par tous les contributeurs, qu'ils soient humains ou agents IA.

## Standards de Documentation

### Format Markdown

#### Structure des Documents
```markdown
# Titre Principal (H1) - Un seul par document

## Section Majeure (H2)

### Sous-section (H3)

#### Détail (H4)

##### Sous-détail (H5)

###### Note (H6)
```

#### Conventions de Style
- **Gras** : Concepts clés, termes importants
- *Italique* : Références, citations, emphasis
- `Code inline` : Noms de fichiers, variables, commandes
- **Listes** : Tirets (-) pour listes non ordonnées, numéros (1.) pour ordonnées
- **Liens** : Format [Texte](URL) ou [Référence][ref-id]

#### Blocs de Code
```markdown
```language
code content
```
```

**Langages supportés** : markdown, javascript, typescript, python, sql, bash, json, yaml, mermaid

### Diagrammes Mermaid

#### Types Autorisés
- **Flowchart** : Flux de processus, workflows
- **Sequence** : Interactions entre composants
- **Class** : Modèles de données, architecture
- **ER** : Relations base de données
- **Gantt** : Planification temporelle

#### Standards de Qualité
```mermaid
%%{init: {'theme':'base', 'themeVariables': {'primaryColor': '#ff0000'}}}%%
graph TD
    A[Début] --> B{Condition}
    B -->|Oui| C[Action 1]
    B -->|Non| D[Action 2]
    C --> E[Fin]
    D --> E
```

**Règles** :
- Toujours inclure direction (TD, LR, etc.)
- Noms de nœuds explicites et courts
- Couleurs cohérentes dans le projet
- Validation syntaxe avant commit

## Standards de Contenu

### Format EARS pour Requirements

#### Structure Obligatoire
```markdown
**REQ-X** : [Titre court et descriptif]

**WHEN** [situation ou trigger spécifique]
**THE SYSTEM** [action ou comportement attendu]
**SHALL** [résultat ou état final mesurable]

**Critères d'acceptation** :
- [ ] Critère 1 mesurable
- [ ] Critère 2 testable
- [ ] Critère 3 vérifiable

**Cas d'erreur** :
- Erreur 1 : Comportement attendu
- Erreur 2 : Comportement attendu
```

#### Qualité des Requirements
- **Spécifique** : Pas d'ambiguïté possible
- **Mesurable** : Critères quantifiables
- **Atteignable** : Techniquement réalisable
- **Pertinent** : Aligné avec objectifs business
- **Temporel** : Contraintes de temps si applicable

### Structure Design Documents

#### Sections Obligatoires
1. **Vue d'ensemble** : Résumé exécutif
2. **Architecture** : Diagrammes et patterns
3. **Composants** : Détail des modules
4. **Modèles de données** : Schémas et relations
5. **Flux de données** : Interactions et processus
6. **Gestion des erreurs** : Stratégies et patterns
7. **Sécurité** : Authentification, autorisation, protection
8. **Performance** : Objectifs et optimisations
9. **Stratégie de test** : Approche et couverture
10. **Plan d'implémentation** : Phases et dépendances

#### Qualité du Design
- **Cohérence** : Patterns uniformes
- **Scalabilité** : Croissance anticipée
- **Maintenabilité** : Code facile à modifier
- **Testabilité** : Facilité de test
- **Sécurité** : Protection par design

### Structure Tasks Documents

#### Format des Tâches
```markdown
### X. Nom de la Phase

- [ ] **X.Y Nom de la tâche principale**
  - Description claire de l'objectif
  - Informations techniques nécessaires
  - Critères de validation
  - *Référence : [REQ-X], [DESIGN-Y]*

  - [ ] **X.Y.Z Sous-tâche spécifique**
    - Détail d'implémentation
    - Tests associés
    - Validation attendue
```

#### Qualité des Tâches
- **Atomique** : Une responsabilité par tâche
- **Testable** : Validation claire possible
- **Ordonnée** : Dépendances respectées
- **Traçable** : Liens vers requirements/design
- **Estimable** : Effort évaluable

## Standards de Code

### Conventions de Nommage

#### Fichiers et Dossiers
- **Dossiers** : kebab-case (ex: `user-authentication`)
- **Fichiers Markdown** : kebab-case.md (ex: `api-design.md`)
- **Fichiers Code** : Selon langage (camelCase JS, snake_case Python)
- **Assets** : kebab-case avec extension (ex: `user-flow.png`)

#### Identifiants dans Documentation
- **Requirements** : REQ-[numéro] (ex: REQ-1, REQ-2)
- **Design Components** : DESIGN-[nom] (ex: DESIGN-AuthService)
- **Tasks** : TASK-[phase].[numéro] (ex: TASK-1.1, TASK-2.3)
- **Tests** : TEST-[type]-[numéro] (ex: TEST-UNIT-1, TEST-E2E-5)

### Commentaires et Documentation

#### Dans le Code
```typescript
/**
 * Service d'authentification utilisateur
 * 
 * @spec sdd/specs/user-authentication/
 * @requirements REQ-1, REQ-2, REQ-3
 * @design AuthService component
 * <AUTHOR> IA / Développeur
 * @version 1.0.0
 */
export class AuthService {
  /**
   * Authentifie un utilisateur
   * @param credentials - Email et mot de passe
   * @returns Token JWT si succès
   * @throws AuthError si échec
   * @spec REQ-1 - Connexion utilisateur
   */
  async authenticate(credentials: LoginCredentials): Promise<AuthToken> {
    // Implémentation
  }
}
```

#### Règles de Commentaires
- **JSDoc/PyDoc** : Documentation API complète
- **Inline** : Logique complexe uniquement
- **TODO** : Format `TODO: [TASK-X.Y] Description`
- **FIXME** : Format `FIXME: [REQ-X] Issue description`

## Processus de Validation

### Validation Automatique

#### Structure des Documents
```bash
# Validation structure SDD
npm run sdd:validate

# Validation format Markdown
npm run lint:markdown

# Validation diagrammes Mermaid
npm run validate:mermaid

# Validation références croisées
npm run validate:references
```

#### Critères de Validation
- **Complétude** : Toutes sections obligatoires présentes
- **Format** : Respect syntaxe Markdown et Mermaid
- **Références** : Liens valides entre documents
- **Cohérence** : Terminologie uniforme
- **Qualité** : Respect standards EARS, etc.

### Validation Manuelle

#### Checklist Requirements
- [ ] Format EARS respecté pour chaque requirement
- [ ] Critères d'acceptation mesurables
- [ ] Cas d'erreur documentés
- [ ] Références business claires
- [ ] Contraintes techniques identifiées

#### Checklist Design
- [ ] Architecture cohérente avec requirements
- [ ] Diagrammes Mermaid valides et lisibles
- [ ] Modèles de données complets
- [ ] Stratégie de test définie
- [ ] Considérations sécurité et performance

#### Checklist Tasks
- [ ] Couverture complète du design
- [ ] Tâches atomiques et testables
- [ ] Dépendances identifiées
- [ ] Références aux requirements/design
- [ ] Critères de validation clairs

## Gestion des Erreurs et Exceptions

### Types d'Erreurs

#### Erreurs de Structure
- **Fichiers manquants** : requirements.md, design.md, tasks.md
- **Sections manquantes** : Sections obligatoires des templates
- **Format invalide** : Syntaxe Markdown ou Mermaid incorrecte

#### Erreurs de Contenu
- **Références cassées** : Liens vers REQ-X, DESIGN-Y inexistants
- **Incohérences** : Contradictions entre documents
- **Incomplétude** : Informations manquantes critiques

#### Erreurs de Processus
- **Ordre incorrect** : Tasks avant design, design avant requirements
- **Validation manquée** : Implémentation sans validation specs
- **Traçabilité perdue** : Code sans référence aux specs

### Gestion des Exceptions

#### Cas Particuliers
- **Legacy Code** : Migration progressive vers SDD
- **Prototypes** : Specs allégées pour POC
- **Hotfixes** : Specs a posteriori pour corrections urgentes
- **Refactoring** : Mise à jour specs avant code

#### Procédures d'Exception
1. **Documentation** : Justification de l'exception
2. **Approbation** : Validation tech lead/architect
3. **Planification** : Plan de retour à la conformité
4. **Suivi** : Monitoring et résolution

## Collaboration et Communication

### Rôles et Responsabilités

#### Agent IA
- **Génération** : Création documents depuis templates
- **Validation** : Vérification format et cohérence
- **Implémentation** : Code guidé par specs
- **Tests** : Validation contre critères d'acceptation

#### Développeur Senior
- **Architecture** : Design et patterns techniques
- **Review** : Validation qualité specs et code
- **Mentoring** : Guide développeurs junior
- **Standards** : Évolution guidelines et templates

#### Développeur Junior
- **Implémentation** : Code selon tasks définies
- **Tests** : Tests unitaires et validation
- **Documentation** : Mise à jour inline documentation
- **Feedback** : Retours sur clarté des specs

#### Product Manager
- **Requirements** : Validation besoins business
- **Acceptance** : Validation critères d'acceptation
- **Priorisation** : Ordre des fonctionnalités
- **Communication** : Interface avec stakeholders

### Processus de Review

#### Review des Specs
1. **Auto-validation** : Outils automatiques
2. **Peer review** : Validation par pairs
3. **Tech review** : Validation architecture
4. **Business review** : Validation product manager
5. **Approbation** : Go/No-go pour implémentation

#### Review du Code
1. **Conformité specs** : Respect des requirements
2. **Qualité code** : Standards et patterns
3. **Tests** : Couverture et qualité
4. **Documentation** : Commentaires et références
5. **Performance** : Respect objectifs

## Métriques et Amélioration Continue

### KPIs de Qualité
- **Couverture specs** : % fonctionnalités avec specs complètes
- **Traçabilité** : % code avec références specs
- **Conformité** : Score validation automatique
- **Complétude** : % sections remplies dans specs

### KPIs de Performance
- **Time to spec** : Délai création specs complètes
- **Time to code** : Délai specs → code fonctionnel
- **Bug rate** : Bugs post-implémentation
- **Rework rate** : % tâches nécessitant modification

### Amélioration Continue
- **Retrospectives** : Feedback régulier équipe
- **Métriques** : Analyse tendances et patterns
- **Templates** : Évolution basée sur usage
- **Formation** : Mise à niveau compétences

## Outils et Automatisation

### Scripts de Validation
```bash
# Validation complète
npm run sdd:validate-all

# Génération documentation
npm run sdd:generate-docs

# Métriques qualité
npm run sdd:metrics

# Migration legacy
npm run sdd:migrate
```

### Intégrations IDE
- **VSCode** : Extension SDD avec snippets
- **Cursor** : Templates intégrés
- **Validation** : Linting en temps réel
- **Navigation** : Liens entre specs et code

### CI/CD Integration
- **Pre-commit** : Validation structure et format
- **PR checks** : Validation complétude specs
- **Deployment** : Génération documentation
- **Monitoring** : Métriques qualité continues

---

*Ces directives évoluent avec le projet et l'expérience de l'équipe. Elles constituent la référence pour maintenir la qualité et la cohérence du système SDD.*