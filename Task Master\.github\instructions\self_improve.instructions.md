---
description: Guide pour améliorer continuellement les règles VS Code en fonction des nouveaux schémas de code et des meilleures pratiques émergentes.
applyTo: "**/*"
---

- **Déclencheurs d'amélioration des règles :**
  - Nouveaux schémas de code non couverts par les règles existantes
  - Implémentations similaires répétées dans plusieurs fichiers
  - Schémas d'erreurs courants pouvant être évités par une règle
  - Nouvelles bibliothèques ou outils utilisés de façon récurrente
  - Meilleures pratiques émergentes dans la base de code

- **Processus d'analyse :**
  - Comparer le nouveau code avec les règles existantes
  - Identifier les schémas à standardiser
  - Rechercher des références à la documentation externe
  - Vérifier la cohérence de la gestion des erreurs
  - Surveiller les schémas de tests et leur couverture

- **Mises à jour des règles :**
  - **Ajouter de nouvelles règles lorsque :**
    - Une nouvelle technologie ou un nouveau schéma est utilisé dans 3 fichiers ou plus
    - Des bugs courants pourraient être évités par une règle
    - Les revues de code mentionnent fréquemment le même retour
    - De nouveaux schémas de sécurité ou de performance apparaissent

  - **Modifier les règles existantes lorsque :**
    - De meilleurs exemples existent dans la base de code
    - De nouveaux cas limites sont découverts
    - Des règles liées ont été mises à jour
    - Les détails d'implémentation ont changé

- **Exemple de reconnaissance de schéma :**
  ```typescript
  // Si vous voyez des schémas répétés comme :
  const data = await prisma.user.findMany({
    select: { id: true, email: true },
    where: { status: 'ACTIVE' }
  });
  
  // Pensez à ajouter dans [prisma.instructions.md](.github/instructions/prisma.instructions.md) :
  // - Champs standard à sélectionner
  // - Conditions where courantes
  // - Schémas d'optimisation des performances
  ```

- **Vérifications de qualité des règles :**
  - Les règles doivent être actionnables et spécifiques
  - Les exemples doivent provenir du code réel
  - Les références doivent être à jour
  - Les schémas doivent être appliqués de façon cohérente

- **Amélioration continue :**
  - Surveiller les commentaires de revue de code
  - Suivre les questions fréquentes des développeurs
  - Mettre à jour les règles après des refontes majeures
  - Ajouter des liens vers la documentation pertinente
  - Croiser les références entre règles liées

- **Dépréciation des règles :**
  - Marquer les schémas obsolètes comme dépréciés
  - Supprimer les règles qui ne sont plus applicables
  - Mettre à jour les références vers les règles dépréciées
  - Documenter les chemins de migration pour les anciens schémas

- **Mises à jour de la documentation :**
  - Garder les exemples synchronisés avec le code
  - Mettre à jour les références vers la documentation externe
  - Maintenir les liens entre les règles liées
  - Documenter les changements majeurs
Suivre [vscode_rules.instructions.md](.github/instructions/vscode_rules.instructions.md) pour le format et la structure correcte des règles.
