# Template Design - Architecture Technique

## Instructions Intégrées pour l'Agent IA

### Objectif
Transformer le document requirements.md en design technique détaillé avec architecture, composants et plan d'implémentation.

### Processus Obligatoire
1. **Vérification prérequis** : S'assurer que requirements.md existe et est validé
2. **Recherche contextuelle** : Effectuer des recherches nécessaires selon les exigences
3. **Design complet** : Créer design.md avec toutes les sections requises
4. **Intégration recherche** : Intégrer les résultats directement dans le design
5. **Validation utilisateur** : Cycle feedback-révision jusqu'à approbation explicite
6. **Passage aux tâches** : Seulement après validation explicite

### Contraintes Techniques
- Créer le fichier dans `sdd/specs/{feature_name}/design.md`
- Effectuer recherches nécessaires PENDANT le processus (pas de fichiers séparés)
- Utiliser Mermaid pour les diagrammes si applicable
- Justifier toutes les décisions de design
- Demander validation avec l'outil approprié
- Continuer les révisions jusqu'à approbation explicite

---

# Document de Design - {FEATURE_NAME}

## Vue d'ensemble

### Résumé Exécutif
[Description concise de la solution technique proposée]

### Objectifs du Design
- **Objectif principal** : [Objectif technique principal]
- **Objectifs secondaires** : [Objectifs techniques secondaires]
- **Contraintes respectées** : [Contraintes du requirements.md]

### Décisions de Design Clés
1. **[Décision 1]** : [Description] - *Justification : [Raison]*
2. **[Décision 2]** : [Description] - *Justification : [Raison]*
3. **[Décision 3]** : [Description] - *Justification : [Raison]*

### Recherches Effectuées
- **[Domaine de recherche 1]** : [Résumé des findings et impact sur le design]
- **[Domaine de recherche 2]** : [Résumé des findings et impact sur le design]
- **Sources principales** : [Liens et références utilisées]

## Architecture

### Architecture Globale

```mermaid
[Diagramme d'architecture générale]
```

### Patterns Architecturaux
- **Pattern principal** : [Ex: MVC, Microservices, Event-Driven]
- **Justification** : [Pourquoi ce pattern]
- **Patterns secondaires** : [Autres patterns utilisés]

### Stack Technique

#### Frontend
- **Framework** : [Framework choisi] - *Justification : [Raison]*
- **Librairies** : [Librairies principales]
- **Outils de build** : [Webpack, Vite, etc.]

#### Backend
- **Runtime** : [Node.js, Python, etc.] - *Justification : [Raison]*
- **Framework** : [Express, FastAPI, etc.]
- **Base de données** : [PostgreSQL, MongoDB, etc.] - *Justification : [Raison]*

#### Infrastructure
- **Hébergement** : [Cloud provider, on-premise]
- **Conteneurisation** : [Docker, Kubernetes]
- **CI/CD** : [GitHub Actions, Jenkins, etc.]

### Diagramme de Déploiement

```mermaid
[Diagramme de déploiement]
```

## Composants et Interfaces

### Composants Principaux

#### 1. [Nom du Composant 1]
- **Responsabilité** : [Rôle du composant]
- **Technologies** : [Technologies utilisées]
- **Dépendances** : [Autres composants requis]
- **Interfaces exposées** : [APIs, événements, etc.]

#### 2. [Nom du Composant 2]
- **Responsabilité** : [Rôle du composant]
- **Technologies** : [Technologies utilisées]
- **Dépendances** : [Autres composants requis]
- **Interfaces exposées** : [APIs, événements, etc.]

### Diagramme de Composants

```mermaid
[Diagramme des composants et leurs interactions]
```

### APIs et Interfaces

#### API REST

```yaml
# Exemple d'endpoints principaux
GET /api/{resource}
POST /api/{resource}
PUT /api/{resource}/{id}
DELETE /api/{resource}/{id}
```

#### Événements (si applicable)

```yaml
# Événements du système
{EventName}:
  payload: {structure}
  triggers: [conditions]
  handlers: [composants qui réagissent]
```

#### Interfaces de Données

```typescript
// Interfaces TypeScript principales
interface {MainEntity} {
  id: string;
  // autres propriétés
}

interface {SecondaryEntity} {
  // structure
}
```

## Modèles de Données

### Schéma de Base de Données

```mermaid
erDiagram
    [Diagramme entité-relation]
```

### Entités Principales

#### {Entity1}
```sql
CREATE TABLE {entity1} (
  id UUID PRIMARY KEY,
  -- autres colonnes
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

#### {Entity2}
```sql
CREATE TABLE {entity2} (
  id UUID PRIMARY KEY,
  -- autres colonnes
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

### Relations
- **{Entity1} → {Entity2}** : [Type de relation et cardinalité]
- **{Entity2} → {Entity3}** : [Type de relation et cardinalité]

### Index et Performance
```sql
-- Index principaux pour la performance
CREATE INDEX idx_{table}_{column} ON {table}({column});
CREATE INDEX idx_{table}_composite ON {table}({col1}, {col2});
```

### Migrations
- **Migration initiale** : Création des tables principales
- **Migrations futures** : [Évolutions prévues]

## Flux de Données

### Flux Utilisateur Principal

```mermaid
sequenceDiagram
    [Diagramme de séquence pour le flux principal]
```

### Flux de Données Critiques
1. **[Flux 1]** : [Description du flux]
2. **[Flux 2]** : [Description du flux]
3. **[Flux 3]** : [Description du flux]

### États et Transitions

```mermaid
stateDiagram-v2
    [Diagramme d'états si applicable]
```

## Gestion des Erreurs

### Stratégie Globale
- **Logging** : [Stratégie de logging]
- **Monitoring** : [Outils de monitoring]
- **Alerting** : [Système d'alertes]

### Types d'Erreurs

#### Erreurs Utilisateur (4xx)
- **400 Bad Request** : [Cas d'usage et gestion]
- **401 Unauthorized** : [Cas d'usage et gestion]
- **404 Not Found** : [Cas d'usage et gestion]

#### Erreurs Système (5xx)
- **500 Internal Server Error** : [Cas d'usage et gestion]
- **503 Service Unavailable** : [Cas d'usage et gestion]

### Récupération et Résilience
- **Retry Logic** : [Stratégie de retry]
- **Circuit Breaker** : [Si applicable]
- **Fallback** : [Mécanismes de fallback]

### Format des Erreurs
```json
{
  "error": {
    "code": "ERROR_CODE",
    "message": "Human readable message",
    "details": {},
    "timestamp": "2024-01-01T00:00:00Z",
    "requestId": "uuid"
  }
}
```

## Sécurité

### Authentification
- **Méthode** : [JWT, OAuth, etc.]
- **Fournisseur** : [Auth0, Firebase, custom]
- **Durée de session** : [Durée des tokens]

### Autorisation
- **Modèle** : [RBAC, ABAC, etc.]
- **Rôles** : [Liste des rôles]
- **Permissions** : [Matrice permissions/rôles]

### Protection des Données
- **Chiffrement en transit** : [TLS/SSL]
- **Chiffrement au repos** : [Méthodes utilisées]
- **Données sensibles** : [Stratégie de protection]

### Validation et Sanitisation
- **Input validation** : [Stratégie de validation]
- **Output encoding** : [Protection XSS]
- **SQL Injection** : [Protection utilisée]

## Performance

### Objectifs de Performance
- **Temps de réponse** : [Objectifs du requirements.md]
- **Débit** : [Objectifs du requirements.md]
- **Concurrence** : [Nombre d'utilisateurs simultanés]

### Stratégies d'Optimisation

#### Frontend
- **Lazy Loading** : [Composants concernés]
- **Code Splitting** : [Stratégie de découpage]
- **Caching** : [Stratégie de cache navigateur]

#### Backend
- **Caching** : [Redis, Memcached, etc.]
- **Database Optimization** : [Index, requêtes optimisées]
- **Connection Pooling** : [Configuration]

#### Infrastructure
- **CDN** : [Si applicable]
- **Load Balancing** : [Stratégie]
- **Auto-scaling** : [Règles de scaling]

### Monitoring Performance
- **Métriques clés** : [Liste des métriques]
- **Outils** : [Outils de monitoring]
- **Alertes** : [Seuils d'alerte]

## Stratégie de Test

### Pyramide de Tests

#### Tests Unitaires (70%)
- **Framework** : [Jest, Pytest, etc.]
- **Couverture cible** : [Pourcentage]
- **Composants testés** : [Liste des composants]

#### Tests d'Intégration (20%)
- **Framework** : [Supertest, etc.]
- **APIs testées** : [Endpoints principaux]
- **Base de données** : [Stratégie de test DB]

#### Tests E2E (10%)
- **Framework** : [Cypress, Playwright, etc.]
- **Scénarios** : [Parcours utilisateur principaux]
- **Environnement** : [Environnement de test]

### Tests de Performance
- **Outils** : [K6, JMeter, etc.]
- **Scénarios** : [Tests de charge, stress, etc.]
- **Critères d'acceptation** : [Seuils de performance]

### Tests de Sécurité
- **OWASP** : [Tests de sécurité OWASP]
- **Penetration Testing** : [Si applicable]
- **Dependency Scanning** : [Outils utilisés]

## Plan d'Implémentation

### Phases de Développement

#### Phase 1 : Foundation (Semaine 1-2)
- [ ] Setup projet et infrastructure
- [ ] Configuration base de données
- [ ] Authentification de base
- [ ] API skeleton

#### Phase 2 : Core Features (Semaine 3-4)
- [ ] [Fonctionnalité principale 1]
- [ ] [Fonctionnalité principale 2]
- [ ] Tests unitaires core

#### Phase 3 : Advanced Features (Semaine 5-6)
- [ ] [Fonctionnalités avancées]
- [ ] Optimisations performance
- [ ] Tests d'intégration

#### Phase 4 : Polish & Deploy (Semaine 7-8)
- [ ] Tests E2E
- [ ] Documentation
- [ ] Déploiement production
- [ ] Monitoring

### Dépendances et Risques

#### Dépendances Critiques
- **[Dépendance 1]** : [Impact et mitigation]
- **[Dépendance 2]** : [Impact et mitigation]

#### Risques Identifiés
- **[Risque 1]** : [Probabilité/Impact] - *Mitigation : [Plan]*
- **[Risque 2]** : [Probabilité/Impact] - *Mitigation : [Plan]*

### Critères de Succès
- [ ] Tous les requirements.md sont implémentés
- [ ] Tests passent avec 90%+ de couverture
- [ ] Performance respecte les objectifs
- [ ] Sécurité validée
- [ ] Documentation complète

## Évolutions Futures

### Roadmap Technique
- **Version 2.0** : [Évolutions prévues]
- **Optimisations** : [Améliorations techniques]
- **Nouvelles fonctionnalités** : [Extensions possibles]

### Scalabilité
- **Limites actuelles** : [Limites du design actuel]
- **Plans de scaling** : [Comment scaler]
- **Refactoring futur** : [Améliorations architecturales]

---

## Instructions de Validation pour l'Agent

### Questions de Validation Suggérées
1. "L'architecture proposée répond-elle à toutes les exigences ?"
2. "Les choix techniques sont-ils justifiés et appropriés ?"
3. "Le plan d'implémentation est-il réaliste ?"
4. "Les aspects sécurité et performance sont-ils suffisamment couverts ?"
5. "Y a-t-il des risques ou dépendances non identifiés ?"

### Format de Validation
```
**Validation Design :**
J'ai terminé le document design.md avec :
- Architecture complète et justifiée
- [X] composants principaux définis
- Modèles de données détaillés
- Stratégies de test et sécurité
- Plan d'implémentation en [Y] phases
- Gestion des erreurs et performance

**Le design vous convient-il ? Si oui, nous pouvons passer au plan d'implémentation.**
```

### Cycle de Révision
1. Présenter le design initial avec recherches intégrées
2. Demander validation explicite
3. Si modifications demandées : réviser et re-valider
4. Répéter jusqu'à approbation explicite
5. Passer aux tâches seulement après validation