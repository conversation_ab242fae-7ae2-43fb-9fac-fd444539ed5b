# Génération Automatique du Contexte Projet SDD

Tu vas maintenant exécuter le processus de génération automatique séquentielle de la documentation contexte projet selon la méthodologie Spec-Driven Development (SDD).

## Objectif

Créer la "banque de mémoire" centrale du projet qui servira de référence unique pour tous les développements ultérieurs en générant automatiquement :

- `sdd/project/product.md` - Documentation produit et vision
- `sdd/project/structure.md` - Architecture et organisation du projet  
- `sdd/project/tech.md` - Spécifications techniques et implémentation

## Processus Obligatoire à Suivre

### Règle Fondamentale
**Génération séquentielle automatique Product → Structure → Tech sans interruption ni validation manuelle.**

### Étapes Séquentielles Automatiques

#### 1. Génération Product (product.md)
- Créer le fichier `sdd/project/product.md`
- Utiliser le template `sdd/templates/product.template.md`
- Analyser le contexte métier et utilisateurs en profondeur
- Définir des métriques mesurables et objectifs SMART
- Générer toutes les sections sans questions séquentielles préalables
- ✅ **Confirmation automatique de progression vers Structure**

#### 2. Génération Structure (structure.md)
- Créer le fichier `sdd/project/structure.md`
- Utiliser le template `sdd/templates/structure.template.md`
- Analyser l'organisation optimale selon le type de projet
- Définir des conventions claires et cohérentes
- Intégrer avec les bonnes pratiques SDD
- ✅ **Confirmation automatique de progression vers Tech**

#### 3. Génération Tech (tech.md)
- Créer le fichier `sdd/project/tech.md`
- Utiliser le template `sdd/templates/tech.template.md`
- Spécifications techniques détaillées
- Contraintes et exigences non-fonctionnelles
- Configuration et architecture technique
- ✅ **Finalisation automatique complète**

## Principes de Génération Automatique

### Méthodologie Banque de Mémoire
- **Centralisation** : Toute l'information critique centralisée automatiquement dans `sdd/project/`
- **Référencement** : Les templates servent de modèles canoniques appliqués automatiquement
- **Cohérence** : Une seule source de vérité maintenue automatiquement
- **Traçabilité** : Liens explicites générés automatiquement
- **Évolutivité** : Structure permettant les mises à jour automatiques continues

### Contraintes Techniques
- **Destination Obligatoire** : `sdd/project/`
- **Nommage Standard** : `product.md`, `structure.md`, `tech.md`
- **Templates** : Lecture complète obligatoire et respect de toutes les sections
- **Analyse Approfondie** : Examiner l'ensemble du contexte projet avant génération

## Critères de Qualité Automatiques

### Validation Automatique
- Conformité automatique aux templates de référence
- Vérification automatique de la cohérence inter-documents
- Contrôle automatique de la complétude des sections obligatoires
- Application automatique des instructions spécifiques de chaque template

### Standards de Qualité
- Respect automatique de la structure et organisation prescrites
- Maintien automatique de la cohérence et traçabilité
- Création automatique de références croisées appropriées
- Finalisation automatique avec documentation des décisions

## Instructions d'Exécution

**COMMENCER MAINTENANT** la génération automatique séquentielle :

1. Vérifier l'existence du répertoire `sdd/project/`
2. Analyser l'ensemble du contexte projet disponible
3. Lire intégralement chaque template de référence
4. Générer product.md → progression automatique
5. Générer structure.md → progression automatique
6. Générer tech.md → finalisation automatique
7. Transition automatique vers phase spécifications

**Rappel Important** : Ces documents constituent la base de référence pour toutes les spécifications futures. La génération séquentielle automatique sans interruption est essentielle pour maintenir la cohérence de la méthodologie SDD.