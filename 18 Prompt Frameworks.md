# Guide Complet des Prompt Frameworks pour l'Entreprise

## Introduction

Les **prompt frameworks** sont des modèles structurés conçus pour optimiser les interactions avec les modèles de langage en entreprise. Ces cadres méthodiques permettent de guider le comportement des LLM pour obtenir des résultats plus pertinents et adaptés à des tâches professionnelles spécifiques.

L'objectif principal est de **créer des prompts personnalisés et cohérents** selon votre contexte d'entreprise, en expliquant précisément au modèle ce que vous attendez de lui.

## Vue d'Ensemble des 18 Frameworks

### Tableau Comparatif des Applications

| Framework | Description | Cas d'Usage Principaux |
|-----------|-------------|----------------------|
| **TAG** | Task Action Goal - Planification stratégique et résolution de problèmes | Formation, marketing, recrutement |
| **APE** | Action Purpose Expectation - Projets détaillés | Emails personnalisés, réseaux sociaux, plans d'action |
| **RTF** | Role Task Format - Réponses claires et directes | Contenu marketing, idées créatives, présentations |
| **CARE** | Context Action Result Example - Conseils détaillés avec exemples | Lancement produit, service client, transformation digitale |
| **SPEAR** | Approche en 5 étapes structurées | Solutions innovantes, contenu engageant, use cases |
| **RODES** | Role Objective Details Examples Sens check - Haute spécificité | Formation, fiches produits, procédures SAV |
| **RISEN** | Role Instructions Steps End goal Narrowing - Gestion de projet | Lancement site web, business plan, cours en ligne |
| **RACE** | Reach Act Convert Engage - Stratégies marketing | Réseaux sociaux, campagnes pub, newsletters |
| **FAB** | Features Advantages Benefits - Présentation produits | Descriptions produits, argumentaires de vente |
| **ROSES** | Planification stratégique complexe | Méthodologie agile, fidélisation, continuité d'activité |
| **FOCUS** | Function Objective Context Utility Specifications | Développement produits, SEO, études de marché |
| **COAST** | Context Objective Actions Scenario Task - Planification détaillée | Cahiers des charges, conseil, communication de crise |
| **SPAR** | Situation Purpose Action Result - Polyvalent | Analyse concurrentielle, pitchs, innovation |
| **BAB** | Before After Bridge - Transformation | Stratégies marketing, engagement blog, lancements |
| **CIDI** | Context Instructions Details Input - Contenu détaillé | Articles de blog, campagnes sociales, formations |
| **COSTAR** | Plans stratégiques complets | Stratégies marketing, planification d'actions |
| **ERA** | Expectation Role Action - Tâches variées | Contenu SEO, formation, créativité |
| **RELIC** | Défis créatifs | Calendriers éditoriaux, marketing créatif, scripts |

# Frameworks Détaillés - Partie 1

## 1. Framework TAG (Task Action Goal)

**Structure :**
- **Task** : Définir la tâche spécifique
- **Action** : Préciser l'action attendue
- **Goal** : Clarifier l'objectif final

**Applications professionnelles :**
- Planification stratégique d'équipe
- Formation et développement des compétences
- Campagnes marketing ciblées
- Processus de recrutement

**Exemple concret :**
```
Task: Créer un plan de formation pour l'équipe commerciale
Action: Développer un programme de 3 mois avec modules progressifs
Goal: Augmenter les conversions de 25% d'ici fin d'année
```

## 2. Framework APE (Action Purpose Expectation)

**Structure :**
- **Action** : L'action précise à réaliser
- **Purpose** : Le but recherché
- **Expectation** : Le résultat attendu

**Applications professionnelles :**
- Rédaction d'emails personnalisés
- Création de contenu pour réseaux sociaux
- Élaboration de plans d'action détaillés
- Communication client stratégique

**Exemple concret :**
```
Action: Rédiger une série d'emails de relance commerciale
Purpose: Réengager les prospects inactifs depuis 3 mois
Expectation: Obtenir un taux d'ouverture de 35% et 15% de réponses positives
```

## 3. Framework RTF (Role Task Format)

**Structure :**
- **Role** : Le rôle que doit adopter l'IA
- **Task** : La tâche à accomplir
- **Format** : Le format de sortie souhaité

**Applications professionnelles :**
- Création de contenu marketing varié
- Génération d'idées créatives structurées
- Préparation de présentations professionnelles
- Développement de supports de communication

**Exemple concret :**
```
Role: Expert en marketing digital avec 10 ans d'expérience
Task: Analyser les tendances 2025 du e-commerce B2B
Format: Rapport exécutif de 2 pages avec 5 recommandations prioritaires
```

## 4. Framework CARE (Context Action Result Example)

**Structure :**
- **Context** : Le contexte détaillé de la situation
- **Action** : L'action spécifique demandée
- **Result** : Le résultat souhaité
- **Example** : Un exemple pour guider la réponse

**Applications professionnelles :**
- Stratégies de lancement produit
- Optimisation du service client
- Accompagnement transformation digitale
- Gestion de crise et communication

**Exemple concret :**
```
Context: Entreprise SaaS B2B, 50 employés, expansion internationale
Action: Développer une stratégie d'entrée sur le marché allemand
Result: Plan opérationnel sur 18 mois avec budget et KPIs
Example: Comme Slack lors de son expansion européenne en 2016
```

## 5. Framework SPEAR

**Structure en 5 étapes :**
- **Situation** : Analyser le contexte actuel
- **Problem** : Identifier le problème principal
- **Emotion** : Comprendre l'impact émotionnel
- **Anticipation** : Prévoir les conséquences
- **Response** : Proposer une solution adaptée

**Applications professionnelles :**
- Développement de solutions innovantes
- Création de contenu engageant
- Élaboration d'use cases clients
- Résolution de problématiques complexes

**Exemple concret :**
```
Situation: Baisse de 20% de l'engagement sur nos réseaux sociaux
Problem: Contenu trop promotionnel, manque d'authenticité
Emotion: Frustration de l'équipe marketing, inquiétude direction
Anticipation: Risque de perte de notoriété et de leads qualifiés
Response: Stratégie de contenu éducatif avec storytelling authentique
```

## 6. Framework RODES (Role Objective Details Examples Sens check)

**Structure :**
- **Role** : Définir le rôle spécialisé de l'IA
- **Objective** : Préciser l'objectif à atteindre
- **Details** : Fournir les détails nécessaires
- **Examples** : Donner des exemples concrets
- **Sens check** : Vérifier la cohérence du résultat

**Applications professionnelles :**
- Création de programmes de formation sur mesure
- Rédaction de fiches produits techniques
- Élaboration de procédures SAV détaillées
- Développement de guides utilisateur

**Exemple concret :**
```
Role: Formateur expert en cybersécurité pour PME
Objective: Créer un module de sensibilisation aux phishing
Details: Public = 25 employés non-techniques, durée 2h, format hybride
Examples: Utiliser des cas réels comme l'attaque de Toyota en 2019
Sens check: Vérifier que le contenu reste accessible sans être technique
```

## 7. Framework RISEN (Role Instructions Steps End goal Narrowing)

**Structure :**
- **Role** : Le rôle expert assigné à l'IA
- **Instructions** : Les instructions précises
- **Steps** : Les étapes méthodiques
- **End goal** : L'objectif final visé
- **Narrowing** : Le ciblage spécifique

**Applications professionnelles :**
- Gestion de projets complexes
- Lancement de sites web ou applications
- Création de business plans détaillés
- Développement de cours en ligne

**Exemple concret :**
```
Role: Chef de projet digital expérimenté
Instructions: Planifier le lancement d'une marketplace B2B
Steps: Audit technique → Développement → Tests → Déploiement → Suivi
End goal: Plateforme opérationnelle avec 100 vendeurs actifs en 6 mois
Narrowing: Focus sur le secteur industriel français, PME 10-250 employés
```

## 8. Framework RACE (Reach Act Convert Engage)

**Structure :**
- **Reach** : Stratégies pour atteindre l'audience
- **Act** : Actions d'interaction avec les prospects
- **Convert** : Techniques de conversion
- **Engage** : Méthodes d'engagement et fidélisation

**Applications professionnelles :**
- Stratégies marketing digital complètes
- Campagnes publicitaires multi-canaux
- Développement de newsletters engageantes
- Optimisation du parcours client

**Exemple concret :**
```
Reach: SEO + LinkedIn Ads ciblant CTOs de scale-ups tech
Act: Webinaires mensuels + contenu éducatif sur l'IA en entreprise
Convert: Trial gratuit 30 jours + demo personnalisée
Engage: Onboarding structuré + success manager dédié + communauté utilisateurs
```

## 9. Framework FAB (Features Advantages Benefits)

**Structure :**
- **Features** : Les caractéristiques du produit/service
- **Advantages** : Les avantages concurrentiels
- **Benefits** : Les bénéfices client concrets

**Applications professionnelles :**
- Descriptions produits percutantes
- Argumentaires de vente structurés
- Présentations commerciales
- Pages de vente optimisées

**Exemple concret :**
```
Features: Plateforme CRM avec IA prédictive intégrée
Advantages: 50% plus rapide que Salesforce pour l'analyse des leads
Benefits: +30% de conversions, -2h de travail admin par jour/commercial, ROI visible en 3 mois
```

## 10. Framework ROSES

**Structure en 5 dimensions :**
- **Resources** : Identifier les ressources disponibles
- **Objectives** : Définir les objectifs stratégiques
- **Strategy** : Élaborer la stratégie globale
- **Execution** : Planifier l'exécution détaillée
- **Success** : Mesurer le succès et l'impact

**Applications professionnelles :**
- Planification stratégique d'entreprise
- Mise en place de méthodologies agiles
- Stratégies de fidélisation client
- Plans de continuité d'activité

**Exemple concret :**
```
Resources: Équipe de 5 développeurs, budget 200k€, 8 mois
Objectives: Digitaliser 80% des processus internes de l'entreprise
Strategy: Approche modulaire avec API-first et microservices
Execution: Sprints de 3 semaines, déploiements progressifs par département
Success: Réduction 40% temps de traitement, satisfaction employés >85%
```

**Points clés à retenir :**
- **RODES** excelle pour les projets nécessitant une expertise technique précise
- **RISEN** est idéal pour la gestion de projets complexes avec de multiples parties prenantes
- **RACE** structure parfaitement les stratégies marketing omnicanales
- **FAB** maximise l'impact des présentations commerciales
- **ROSES** offre une approche holistique pour la planification stratégique

## 11. Framework FOCUS (Function Objective Context Utility Specifications)

**Structure :**
- **Function** : La fonction principale demandée
- **Objective** : L'objectif business visé
- **Context** : Le contexte d'utilisation
- **Utility** : L'utilité pratique attendue
- **Specifications** : Les spécifications techniques/détaillées

**Applications professionnelles :**
- Développement de produits innovants
- Stratégies SEO avancées
- Études de marché approfondies
- Cahiers des charges techniques

**Exemple concret :**
```
Function: Développer une stratégie SEO pour e-commerce B2B
Objective: Multiplier le trafic organique par 3 en 12 mois
Context: Secteur industriel, 500 produits, concurrence élevée
Utility: Générer 200 leads qualifiés supplémentaires/mois
Specifications: Focus longtail, optimisation technique Core Web Vitals, maillage interne automatisé
```

## 12. Framework COAST (Context Objective Actions Scenario Task)

**Structure :**
- **Context** : Le contexte détaillé de la situation
- **Objective** : L'objectif à atteindre
- **Actions** : Les actions concrètes à mener
- **Scenario** : Les scénarios possibles à considérer
- **Task** : La tâche finale à accomplir

**Applications professionnelles :**
- Création de cahiers des charges complets
- Missions de conseil stratégique
- Gestion de communication de crise
- Planification de projets complexes

**Exemple concret :**
```
Context: Startup fintech, levée de fonds série A, équipe 15 personnes
Objective: Lever 5M€ auprès d'investisseurs européens en 4 mois
Actions: Pitch deck, business plan, due diligence, roadshow
Scenario: Marché favorable vs crise économique, concurrence accrue
Task: Créer une stratégie de levée avec plan B à 3M€
```

## 13. Framework SPAR (Situation Purpose Action Result)

**Structure :**
- **Situation** : Description de la situation actuelle
- **Purpose** : Le but à atteindre
- **Action** : L'action spécifique demandée
- **Result** : Le résultat concret attendu

**Applications professionnelles :**
- Analyses concurrentielles détaillées
- Préparation de pitchs investisseurs
- Développement de stratégies d'innovation
- Résolution de problématiques business

**Exemple concret :**
```
Situation: Baisse de 15% des ventes SaaS, churn rate à 8%
Purpose: Identifier les causes racines et solutions prioritaires
Action: Analyser les données clients + feedback + parcours utilisateur
Result: Plan d'action 90 jours pour réduire le churn à 4% maximum
```

## 14. Framework BAB (Before After Bridge)

**Structure :**
- **Before** : La situation actuelle problématique
- **After** : La situation idéale visée
- **Bridge** : Le pont/solution pour passer de l'état actuel à l'état désiré

**Applications professionnelles :**
- Stratégies de transformation business
- Campagnes marketing d'engagement
- Lancements produit impactants
- Articles de blog persuasifs

**Exemple concret :**
```
Before: Processus de recrutement manuel, 6 semaines/embauche, 30% d'échecs
After: Pipeline automatisé, 3 semaines/embauche, 10% d'échecs, expérience candidat excellente
Bridge: Plateforme ATS avec IA + tests techniques automatisés + onboarding digital
```

## 15. Framework CIDI (Context Instructions Details Input)

**Structure :**
- **Context** : Le contexte complet du projet
- **Instructions** : Les instructions précises
- **Details** : Les détails importants à considérer
- **Input** : Les éléments d'entrée disponibles

**Applications professionnelles :**
- Rédaction d'articles de blog expertisés
- Campagnes social media complètes
- Création de modules de formation
- Développement de contenu technique

**Exemple concret :**
```
Context: Blog d'entreprise tech, audience = CTOs et dev leads
Instructions: Créer série de 5 articles sur l'architecture microservices
Details: Ton expert mais accessible, 2000 mots/article, SEO-optimisé
Input: Retours d'expérience internes + études de cas Netflix/Amazon
```

## 16. Framework COSTAR

**Structure en 6 composantes :**
- **Context** : Contexte de l'entreprise
- **Objective** : Objectif stratégique
- **Style** : Style de communication
- **Tone** : Ton à adopter
- **Audience** : Public cible
- **Response** : Type de réponse attendue

**Applications professionnelles :**
- Stratégies marketing intégrées
- Planification d'actions commerciales
- Communications corporate
- Développement de contenu de marque

**Exemple concret :**
```
Context: Scale-up EdTech, expansion B2B, 100 collaborateurs
Objective: Positionner l'entreprise comme leader formation IA
Style: Éducatif et accessible, orienté solutions
Tone: Expert mais humain, inspirant confiance
Audience: DRH et responsables formation en grandes entreprises
Response: Stratégie contenu 12 mois avec calendrier éditorial détaillé
```

## 17. Framework ERA (Expectation Role Action)

**Structure :**
- **Expectation** : Ce qui est attendu précisément
- **Role** : Le rôle que doit jouer l'IA
- **Action** : L'action concrète à réaliser

**Applications professionnelles :**
- Optimisation de contenu SEO
- Création de programmes de formation
- Développement créatif
- Conseil stratégique rapide

**Exemple concret :**
```
Expectation: Audit SEO complet avec recommandations prioritaires
Role: Consultant SEO senior spécialisé SaaS B2B
Action: Analyser site web + 20 mots-clés principaux + proposer roadmap 6 mois
```

## 18. Framework RELIC (Requirements Elements Logic Integration Challenge)

**Structure :**
- **Requirements** : Les exigences du projet
- **Elements** : Les éléments constitutifs
- **Logic** : La logique sous-jacente
- **Integration** : L'intégration avec l'existant
- **Challenge** : Les défis à relever

**Applications professionnelles :**
- Calendriers éditoriaux innovants
- Stratégies marketing créatives
- Scripts pour vidéos/podcasts
- Projets de transformation créative

**Exemple concret :**
```
Requirements: Calendrier éditorial LinkedIn pour CEO, 3 posts/semaine
Elements: Thought leadership + actualité secteur + coulisses entreprise
Logic: Alternance contenu éducatif/personnel pour humaniser la marque
Integration: Synchronisation avec campagnes produit et événements
Challenge: Maintenir authenticité tout en respectant contraintes corporate
```

## Synthèse Stratégique des 18 Frameworks

**Pour la planification stratégique :** TAG, ROSES, COSTAR, COAST
**Pour le marketing et communication :** RACE, BAB, FAB, CIDI, ERA
**Pour la gestion de projet :** RISEN, FOCUS, SPAR
**Pour le contenu créatif :** RTF, RELIC, APE
**Pour l'analyse et conseil :** CARE, SPEAR, RODES

**Conseils d'utilisation :**
- **Combinez plusieurs frameworks** selon la complexité du projet
- **Adaptez la structure** à votre contexte spécifique
- **Testez et itérez** pour optimiser les résultats
- **Documentez vos prompts efficaces** pour créer votre bibliothèque interne
