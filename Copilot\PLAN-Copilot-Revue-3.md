Refactor the validation templates in `spec.chatmode.md` to reference the authoritative templates in `spec.instructions.md` instead of duplicating the content. Replace the duplicated validation sections with references like "Use validation templates from `spec.instructions.md` section X.Y"

Create both concise and detailed validation template variants in `spec.instructions.md`. Add a configuration option to choose between brief validation (2-3 key points) and comprehensive validation (current detailed checklists) based on user preference or project complexity.

Nettoyer les duplications dans `context.chatmode.md` en gardant une seule version de chaque template de validation (Product, Structure, Tech) et supprimer les versions redondantes pour améliorer la clarté.

Considérer une optimisation de la longueur des fichiers `.github/` en extrayant les sections les moins critiques vers des fichiers de référence séparés ou en condensant certaines sections détaillées tout en préservant les informations essentielles.