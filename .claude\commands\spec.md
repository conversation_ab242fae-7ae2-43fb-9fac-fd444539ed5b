# Génération Automatique des Spécifications SDD

Tu vas maintenant exécuter le processus de génération automatique séquentielle des spécifications SDD pour la fonctionnalité **$ARGUMENTS** selon la méthodologie Spec-Driven Development.

## Processus Obligatoire à Suivre

### Règle Fondamentale
**Progression automatique séquentielle Requirements → Design → Tasks sans interruption ni validation manuelle.**

### Étapes Séquentielles Automatiques

#### 1. Génération Requirements (requirements.md)
- <PERSON><PERSON><PERSON> le fichier `sdd/specs/$ARGUMENTS/requirements.md`
- Utiliser le template `sdd/templates/requirements.template.md`
- Appliquer le format EARS strict : "The system SHALL/SHOULD/MAY..."
- G<PERSON><PERSON><PERSON> toutes les sections sans poser de questions séquentielles
- ✅ **Confirmation automatique de progression vers Design**

#### 2. Génération Design (design.md)
- <PERSON><PERSON><PERSON> le fichier `sdd/specs/$ARGUMENTS/design.md`
- Utiliser le template `sdd/templates/design.template.md`
- Inclure diagrammes Mermaid si applicable
- Effectuer recherches nécessaires PENDANT le processus
- ✅ **Confirmation automatique de progression vers Tasks**

#### 3. Génération Tasks (tasks.md)
- Créer le fichier `sdd/specs/$ARGUMENTS/tasks.md`
- Utiliser le template `sdd/templates/tasks.template.md`
- Format checklist avec tâches atomiques de codage uniquement
- Progression incrémentale orientée test
- ✅ **Finalisation automatique complète**

## Contraintes Techniques

### Structure des Fichiers
- **Chemin obligatoire** : `sdd/specs/{feature_name}/`
- **Nommage** : kebab-case pour les noms de fonctionnalités
- **Templates** : Utiliser TOUS les templates de référence intégralement

### Formats Spécifiques
- **EARS** : Format "The system SHALL/SHOULD/MAY..." appliqué automatiquement
- **Mermaid** : Diagrammes intégrés générés automatiquement si applicable
- **Checklist** : Format checkbox créé automatiquement pour tasks

### Critères de Qualité Automatiques
- Conformité automatique aux templates de référence
- Application automatique des formats spécifiques
- Vérification automatique de la cohérence inter-documents
- Maintien automatique de la traçabilité entre phases

## Instructions d'Exécution

**COMMENCER MAINTENANT** la génération automatique séquentielle :

1. Vérifier l'existence du répertoire `sdd/specs/`
2. Valider le nom de fonctionnalité (format kebab-case)
3. Lire les templates de référence
4. Générer requirements.md → progression automatique
5. Générer design.md → progression automatique  
6. Générer tasks.md → finalisation automatique

**Rappel Important** : Génération séquentielle automatique continue sans interruption. C'est la règle fondamentale de la méthodologie SDD automatisée.