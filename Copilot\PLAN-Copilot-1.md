I have created the following plan after thorough exploration and analysis of the codebase. Follow the below plan verbatim. Trust the files and references. Do not re-verify what's written in the plan. Explore only when absolutely necessary. First implement all the proposed file changes and then I'll review all the changes together at the end.

### Observations

J'ai analysé la structure existante du projet SDD et identifié que le dépôt utilise une méthodologie "Spec-Driven Development" avec des templates dans `sdd/templates/` pour générer des documents de contexte dans `sdd/project/`. Le projet n'a actuellement aucun dossier `.github/` et utilise une approche "banque de mémoire" pour structurer l'information. Les templates existants suivent un pattern strict avec instructions intégrées, validation utilisateur obligatoire, et cycles de révision itératifs. Les fichiers de prompts existants à la racine montrent un style formel et directif avec validation explicite requise à chaque étape.

### Approach

L'approche consiste à créer une structure `.github/` avec trois types de contextes pour GitHub Copilot : instructions globales, prompts système, et configuration conversationnelle. Ces fichiers référenceront les templates SDD existants plutôt que de les dupliquer, maintenant ainsi la cohérence et évitant la désynchronisation. La structure suivra les conventions GitHub Copilot tout en respectant la méthodologie SDD établie, avec un focus sur la validation utilisateur explicite et les cycles de révision itératifs. L'implémentation sera progressive : création des dossiers, puis des trois fichiers de contexte avec leurs contenus spécifiques.

### Reasoning

J'ai d'abord exploré la structure du dépôt pour comprendre l'organisation existante et identifier l'absence du dossier `.github/`. J'ai ensuite lu les templates existants dans `sdd/templates/` pour comprendre la méthodologie SDD et les patterns d'instructions intégrées. J'ai également examiné les fichiers de contexte générés dans `sdd/project/` et les fichiers de prompts à la racine pour saisir le style et les conventions établies. Cette analyse m'a permis de comprendre les besoins d'intégration avec GitHub Copilot tout en respectant l'approche SDD existante.

## Mermaid Diagram

```mermaid
sequenceDiagram
    participant User as Utilisateur
    participant Copilot as GitHub Copilot
    participant Instructions as context.instructions.md
    participant Templates as Templates SDD
    participant Project as sdd/project/

    User->>Copilot: Commande /context ou /generate-context
    Copilot->>Instructions: Lecture des instructions globales
    Instructions->>Templates: Référence aux templates (product, structure, tech)
    Copilot->>Templates: Analyse des templates SDD
    Copilot->>Project: Génération product.md (draft)
    Copilot->>User: Validation Documentation Produit
    User->>Copilot: Feedback/Approbation
    alt Si modifications demandées
        Copilot->>Project: Révision product.md
        Copilot->>User: Nouvelle validation
    end
    Copilot->>Project: Génération structure.md (draft)
    Copilot->>User: Validation Documentation Structure
    User->>Copilot: Feedback/Approbation
    alt Si modifications demandées
        Copilot->>Project: Révision structure.md
        Copilot->>User: Nouvelle validation
    end
    Copilot->>Project: Génération tech.md (draft)
    Copilot->>User: Validation Documentation Technique
    User->>Copilot: Feedback/Approbation
    alt Si modifications demandées
        Copilot->>Project: Révision tech.md
        Copilot->>User: Nouvelle validation
    end
    Copilot->>User: Contexte projet complet généré
```

## Proposed File Changes

### .github\instructions\context.instructions.md(NEW)

References: 

- sdd\templates\product.template.md
- sdd\templates\structure.template.md
- sdd\templates\tech.template.md

Créer le fichier d'instructions globales pour la génération des trois fichiers de contexte SDD. Le contenu doit inclure :

**Structure du document :**
- En-tête avec objectif et portée (génération de `sdd/project/product.md`, `structure.md`, `tech.md`)
- Section "Méthodologie SDD" expliquant l'approche "banque de mémoire" pour la structuration de l'information
- Section "Templates de Référence" avec liens vers `sdd/templates/product.template.md`, `sdd/templates/structure.template.md`, `sdd/templates/tech.template.md`
- Section "Processus Obligatoire" détaillant les 5 étapes : génération initiale sans questions séquentielles, utilisation complète des sections template, application de la méthodologie banque de mémoire, validation utilisateur avec cycles feedback-révision, création de documents de référence
- Section "Contraintes Techniques" spécifiant les chemins de fichiers (`sdd/project/`), les conventions de nommage, et l'analyse approfondie requise
- Section "Validation et Révision" avec questions suggérées et format de validation standard
- Section "Maintenance" pour les procédures de mise à jour

**Style et format :**
- Utiliser le français pour la cohérence avec le reste du projet
- Adopter le style formel et directif des prompts existants
- Structurer avec des titres markdown et des listes numérotées
- Inclure des références croisées vers les templates plutôt que de dupliquer le contenu
- Maintenir la longueur sous 5000 mots pour respecter les limites de contexte

### .github\prompts\context.prompt.md(NEW)

References: 

- .github\instructions\context.instructions.md(NEW)
- prompt-system.md

Créer le prompt système spécifique à GitHub Copilot pour déclencher la génération des fichiers de contexte. Le contenu doit inclure :

**Structure du document :**
- En-tête définissant le persona ("Tu es un agent SDD spécialisé dans la génération de documentation de contexte projet")
- Section "Rôle et Responsabilités" précisant la mission de génération des trois fichiers de contexte
- Section "Déclencheurs" avec mots-clés ou commandes que l'utilisateur peut utiliser (ex: `/context`, `/generate-context`, `/sdd-context`)
- Section "Règles de Sortie" spécifiant : pas de divagations, respect des limites de taille, utilisation du contexte < 80%, référence obligatoire à `context.instructions.md`
- Section "Processus" résumant les étapes principales avec renvoi vers les instructions détaillées
- Section "Validation" rappelant l'obligation de validation explicite utilisateur
- Section "Limites" précisant les contraintes de contexte et les cas d'usage supportés

**Style et format :**
- Ton formel et directif aligné avec les prompts existants dans `prompt-system.md` et autres
- Utiliser le français pour la cohérence
- Format markdown avec sections claires
- Inclure des exemples de commandes utilisateur
- Référencer explicitement `context.instructions.md` pour la logique métier détaillée
- Maintenir la concision tout en étant complet

### .github\chatmodes\context.chatmode.md(NEW)

References: 

- sdd\templates\product.template.md
- sdd\templates\structure.template.md
- sdd\templates\tech.template.md

Créer la configuration conversationnelle pour GitHub Copilot Chat lors de la génération de contexte. Le contenu doit inclure :

**Structure du document :**
- En-tête expliquant l'objectif de configuration du mode conversationnel
- Section "Modes Acceptés" définissant les modes supportés : `draft` (génération initiale), `review` (révision), `refine` (affinement), `validate` (validation)
- Section "Templates de Validation" avec les formats exacts de messages de validation :
  - "**Validation Documentation Produit :** J'ai terminé le document product.md avec : [checklist]. Cette documentation produit vous convient-elle ?"
  - "**Validation Documentation Structure :** J'ai terminé le document structure.md avec : [checklist]. Cette documentation de structure vous convient-elle ?"
  - "**Validation Documentation Technique :** J'ai terminé le document tech.md avec : [checklist]. Cette documentation technique vous convient-elle ?"
- Section "Gestion du Contexte" avec rappels des limites (< 80% des tokens), stratégies d'optimisation, et gestion des fichiers volumineux
- Section "Politique d'Approbation" implémentant le principe "stop if no explicit approval" avec cycles de révision obligatoires
- Section "Fonctionnalités Chat" détaillant les capacités spécifiques au mode conversationnel (références de fichiers, sélection de code, etc.)
- Section "Limitations" précisant ce qui ne peut pas être fait en mode chat

**Style et format :**
- Style cohérent avec les patterns de validation existants dans les templates
- Utiliser le français pour la cohérence
- Format markdown structuré
- Inclure des exemples concrets de messages de validation
- Référencer les questions de validation des templates originaux
- Maintenir la clarté et la précision des instructions