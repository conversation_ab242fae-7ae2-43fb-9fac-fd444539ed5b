# Instructions Contexte SDD - Génération de Documentation Projet

## Objectif et Portée

Cette documentation définit les instructions globales pour la génération séquentielle automatique complète des trois fichiers de contexte projet selon la méthodologie Spec-Driven Development (SDD) :

- `sdd/project/product.md` - Documentation produit et vision
- `sdd/project/structure.md` - Architecture et organisation du projet  
- `sdd/project/tech.md` - Spécifications techniques et implémentation

Ces fichiers constituent la "banque de mémoire" centrale du projet, servant de référence unique pour tous les développements ultérieurs générés de manière automatique séquentielle.

## Méthodologie SDD

### Approche "Génération Séquentielle Automatique"

La méthodologie SDD repose sur une approche "génération séquentielle automatique" pour la structuration automatique de l'information :

1. **Centralisation** : Toute l'information critique est centralisée automatiquement dans `sdd/project/`
2. **Référencement** : Les templates dans `sdd/templates/` servent de modèles canoniques appliqués automatiquement
3. **Cohérence** : Une seule source de vérité pour chaque aspect du projet maintenue automatiquement
4. **Traçabilité** : Liens explicites entre besoins, conception et implémentation générés automatiquement
5. **Évolutivité** : Structure permettant les mises à jour automatiques continues

### Principes de Génération Automatique Séquentielle

- **Génération Séquentielle Automatique** : Génération automatique séquentielle product.md → structure.md → tech.md sans interruption
- **Progression Automatique** : Transition fluide entre chaque étape sans validation manuelle
- **Instructions Intégrées** : Chaque template contient ses propres règles appliquées automatiquement
- **Non-Duplication** : Référencement automatique des templates plutôt que copie du contenu

## Templates de Référence

### Templates Canoniques

Les templates suivants dans `sdd/templates/` servent de référence obligatoire :

1. **`sdd/templates/product.template.md`**
   - Structure de la documentation produit
   - Sections obligatoires : Vision, Objectifs, Utilisateurs cibles, Fonctionnalités
   - Questions de validation intégrées

2. **`sdd/templates/structure.template.md`**  
   - Architecture générale du projet
   - Sections obligatoires : Organisation, Modules, Dépendances, Patterns
   - Diagrammes et schémas requis

3. **`sdd/templates/tech.template.md`**
   - Spécifications techniques détaillées
   - Sections obligatoires : Technologies, Configuration, APIs, Données
   - Contraintes et exigences non-fonctionnelles

## Intégration GitHub Copilot

### Configuration Technique
Pour les détails techniques d'intégration GitHub Copilot (limites de contexte, stratégies de chunking, optimisations pour l'IA), consulter :
→ **`.github/chatmodes/context.chatmode.md`** - Section "Gestion du Contexte"

### Principes d'Optimisation
- **Seuil Conservateur** : 70% du contexte token maximum (Phase 1 plus restrictive que Phase 2)
- **Réservation Stratégique** : 30% du contexte préservé pour la Phase 2 (spécifications)
- **Monitoring Préventif** : Alerte automatique dès 60% d'utilisation

### Workflow d'Intégration
- **Phase 1** : Établissement du contexte avec contraintes strictes
- **Transition** : Préparation optimisée du contexte pour Phase 2
- **Phase 2** : Utilisation du contexte validé pour génération spécifications

### Utilisation des Templates

- **Lecture Complète Obligatoire** : Chaque template doit être lu intégralement avant génération
- **Respect des Sections** : Toutes les sections template doivent être traitées
- **Application des Instructions** : Les instructions intégrées sont contraignantes
- **Adaptation Contextuelle** : Personnalisation selon le projet spécifique

## Processus Obligatoire

### Étape 1 : Génération Initiale Sans Questions Séquentielles

- Analyser l'ensemble du contexte disponible avant toute génération
- Ne PAS poser de questions séquentielles pendant la génération
- Utiliser le contexte existant et les templates pour inférer les besoins
- Générer des drafts complets basés sur l'analyse contextuelle

### Étape 2 : Utilisation Complète des Sections Template

- Lire intégralement chaque template de référence
- Implémenter TOUTES les sections obligatoires
- Respecter la structure et l'organisation prescrites
- Appliquer les instructions spécifiques de chaque template

### Étape 3 : Application de la Méthodologie Banque de Mémoire

- Centraliser l'information dans les fichiers de contexte
- Éviter la duplication entre les trois documents
- Maintenir la cohérence et la traçabilité
- Créer des références croisées appropriées

### Étape 4 : Génération Séquentielle Automatique

- Générer automatiquement product.md avec confirmation automatique de progression
- Générer automatiquement structure.md avec confirmation automatique de progression  
- Générer automatiquement tech.md avec finalisation automatique
- Transition automatique vers la phase spécifications sans intervention

### Étape 5 : Finalisation Automatique

- Finaliser automatiquement les documents dans `sdd/project/`
- Vérifier automatiquement la complétude et la cohérence
- Confirmer automatiquement l'alignement avec les templates
- Documenter automatiquement les décisions et rationales

## Contraintes Techniques

### Chemins de Fichiers

- **Destination Obligatoire** : `sdd/project/`
- **Nommage Standard** : `product.md`, `structure.md`, `tech.md`
- **Encodage** : UTF-8 avec BOM si nécessaire
- **Format** : Markdown strict avec métadonnées YAML

### Conventions de Nommage

- Utiliser les noms exacts spécifiés
- Maintenir la cohérence avec la structure existante
- Respecter les conventions de fichiers du projet
- Éviter les caractères spéciaux dans les noms

### Analyse Approfondie Requise

- Examiner l'ensemble du contexte projet avant génération
- Identifier les dépendances et contraintes existantes
- Analyser les patterns et conventions établies
- Intégrer les spécificités du domaine métier

## Génération Automatique Séquentielle

### Workflow Automatique Product → Structure → Tech

**Processus de Génération :**
1. **Product.md** : Génération automatique complète → ✅ Confirmation automatique → Progression
2. **Structure.md** : Génération automatique complète → ✅ Confirmation automatique → Progression
3. **Tech.md** : Génération automatique complète → ✅ Finalisation automatique → Transition spécifications

**Confirmations Automatiques :**
- Notifications automatiques de progression à chaque étape
- Confirmations automatiques de completion sans attente
- Transition automatique vers la phase spécifications

### Critères de Qualité Automatiques

**Validation Automatique :**
- Conformité automatique aux templates de référence
- Vérification automatique de la cohérence inter-documents
- Contrôle automatique de la complétude des sections obligatoires
- Application automatique des instructions spécifiques de chaque template

**Standards de Qualité :**
- Respect automatique de la structure et de l'organisation prescrites
- Maintien automatique de la cohérence et de la traçabilité
- Création automatique de références croisées appropriées
- Finalisation automatique avec documentation des décisions

## Maintenance

### Procédures de Mise à Jour

- Surveiller les évolutions des templates de référence
- Maintenir la synchronisation avec les changements projet
- Mettre à jour les références croisées si nécessaire
- Valider la cohérence après modifications

### Versioning et Historique

- Documenter les versions majeures des contextes générés
- Maintenir un historique des décisions importantes
- Tracer l'évolution des spécifications
- Préserver la compatibilité descendante quand possible

---

*Ces instructions constituent le guide de référence pour la génération automatisée de contexte SDD. Elles doivent être appliquées rigoureusement pour maintenir la qualité et la cohérence de la documentation projet.*
