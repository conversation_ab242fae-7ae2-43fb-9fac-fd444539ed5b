Ajouter une section 'Intégration GitHub Copilot' dans `context.instructions.md` avec des directives spécifiques sur les limites de contexte, stratégies de chunking, et optimisations pour l'IA.

Simplifier les templates de validation dans `context.chatmode.md` en réduisant les messages à 3-4 lignes maximum et en supprimant les options A/B/C pour des réponses plus naturelles.

Ajouter un système de validation de cohérence entre les fichiers `.github/` et les templates SDD, par exemple via un script de vérification ou des checksums.

Ajouter une section 'Exemples d'Usage' dans `context.prompt.md` avec des scénarios concrets d'utilisation des différentes commandes de déclenchement.

Développer la section 'Gestion du Contexte' dans `context.chatmode.md` avec des stratégies concrètes de chunking, priorisation des informations, et techniques d'optimisation pour GitHub Copilot.

---

Ajouter des références explicites aux fichiers `context.instructions.md`, `context.prompt.md`, et `context.chatmode.md` dans les sections appropriées des fichiers `spec.*` pour assurer la cohérence entre les phases 1 et 2 du workflow SDD.

Enrichir la section "Contraintes de Contexte" dans `spec.prompt.md` avec des stratégies concrètes de gestion des tokens : priorisation des sections critiques, utilisation de résumés, extraction ciblée, et références optimisées vers les templates.

Créer des versions "concise" et "détaillée" des templates de validation dans `spec.chatmode.md`, permettant à l'agent de choisir le niveau de détail approprié selon la complexité de la fonctionnalité.

Ajouter une section "Gestion des Erreurs" dans `spec.instructions.md` définissant les stratégies de fallback : utilisation de templates par défaut, génération basique sans template, ou escalade vers validation manuelle.

Réviser les commandes de déclenchement dans `spec.prompt.md` pour utiliser des préfixes plus spécifiques comme `/kiro-spec`, `/sdd-generate`, ou `/kiro-sdd-spec` afin d'éviter les conflits avec d'autres systèmes.