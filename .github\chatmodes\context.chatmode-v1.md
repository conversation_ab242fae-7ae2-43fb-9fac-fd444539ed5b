# Configuration Conversationnelle - Génération Contexte SDD

## Objectif

Cette configuration définit les paramètres conversationnels spécifiques à GitHub Copilot Chat pour la génération optimisée de contexte SDD. Elle standardise les interactions, les formats de validation, et la gestion du contexte pendant les sessions de génération de documentation.

## Modes Acceptés

### Mode `draft` - Génération Initiale

**Objectif :** Création des versions initiales des documents de contexte
**Comportement :**
- Analyse complète du contexte disponible sans questions séquentielles
- Génération de drafts complets basés sur les templates de référence
- Focus sur la complétude plutôt que sur la perfection
- Préparation pour cycle de validation utilisateur

**Activation :** Déclenchement automatique lors des commandes `/context`, `/generate-context`

### Mode `review` - Révision

**Objectif :** Révision de documents existants selon feedback utilisateur
**Comportement :**
- Intégration des commentaires et suggestions utilisateur
- <PERSON><PERSON> de la cohérence avec les autres documents de contexte
- Vérification de l'alignement avec les templates de référence
- Préservation des éléments approuvés précédemment

**Activation :** Déclenchement lors de réponses utilisateur avec demandes de modification

### Mode `refine` - Affinement

**Objectif :** Amélioration et optimisation de documents validés
**Comportement :**
- Enrichissement du contenu existant
- Amélioration de la clarté et de la précision
- Ajout de détails manquants identifiés
- Optimisation de la structure et de la lisibilité

**Activation :** Déclenchement explicite par `/refine-context` ou demandes d'amélioration

### Mode `validate` - Validation

**Objectif :** Vérification finale et préparation à l'approbation
**Comportement :**
- Contrôle de conformité avec les templates
- Vérification de la cohérence inter-documents
- Préparation des messages de validation standardisés
- Génération des checklists de vérification

**Activation :** Déclenchement automatique avant chaque demande d'approbation

## Templates de Validation

### Validation Documentation Produit

```
**Validation Documentation Produit :** J'ai terminé le document product.md avec :
- ✅ Vision produit claire et inspirante définie
- ✅ Objectifs SMART quantifiés et mesurables
- ✅ Utilisateurs cibles identifiés avec personas détaillées
- ✅ Fonctionnalités prioritaires listées par ordre d'importance
- ✅ Métriques de succès définies et trackables
- ✅ Contraintes et risques identifiés
- ✅ Roadmap high-level établie

Cette documentation produit vous convient-elle ?

Options de réponse :
A) ✅ Validé - Passer à la documentation structure
B) 🔄 Révisions nécessaires - [Préciser les modifications]
C) ❓ Questions/Clarifications - [Préciser les points]
```

### Validation Documentation Structure

```
**Validation Documentation Structure :** J'ai terminé le document structure.md avec :
- ✅ Architecture générale du projet définie
- ✅ Organisation des modules et composants clarifiée  
- ✅ Dépendances internes et externes identifiées
- ✅ Patterns architecturaux sélectionnés et justifiés
- ✅ Diagrammes et schémas intégrés
- ✅ Interfaces et contrats définis
- ✅ Stratégie de déploiement esquissée

Cette documentation de structure vous convient-elle ?

Options de réponse :
A) ✅ Validé - Passer à la documentation technique
B) 🔄 Révisions nécessaires - [Préciser les modifications]
C) ❓ Questions/Clarifications - [Préciser les points]
```

### Validation Documentation Technique

```
**Validation Documentation Technique :** J'ai terminé le document tech.md avec :
- ✅ Stack technologique sélectionnée et justifiée
- ✅ Configuration d'environnement détaillée
- ✅ APIs et interfaces techniques spécifiées
- ✅ Modèles de données définis
- ✅ Contraintes non-fonctionnelles identifiées
- ✅ Stratégies de test et qualité définies
- ✅ Considérations de sécurité intégrées
- ✅ Documentation de déploiement préparée

Cette documentation technique vous convient-elle ?

Options de réponse :
A) ✅ Validé - Contexte SDD complet généré
B) 🔄 Révisions nécessaires - [Préciser les modifications]  
C) ❓ Questions/Clarifications - [Préciser les points]
```

### Validation Contexte Complet

```
**🎉 Validation Contexte SDD Complet :** J'ai généré avec succès les trois documents de contexte :

📋 **product.md** - Documentation produit et vision [✅ Validé]
🏗️ **structure.md** - Architecture et organisation [✅ Validé]  
⚙️ **tech.md** - Spécifications techniques [✅ Validé]

La banque de mémoire SDD de votre projet est maintenant complète et opérationnelle !

Prochaines étapes suggérées :
A) 📝 Générer les spécifications détaillées (/specs)
B) 🔄 Affiner un document spécifique
C) 📊 Exporter le contexte pour partage équipe
D) ✨ Autre action - [Préciser]
```

## Gestion du Contexte

### Limites et Optimisation

**Limite de Tokens :** Maintenir l'utilisation < 80% de la capacité totale
**Stratégies d'Optimisation :**
- Prioriser les informations critiques en début de génération
- Utiliser des références aux templates plutôt que duplication complète
- Chunker les analyses de contexte volumineux si nécessaire
- Optimiser la longueur des messages de validation

### Gestion des Fichiers Volumineux

**Stratégie de Lecture :**
- Lecture par chunks des templates de référence
- Analyse contextuelle progressive du projet existant
- Priorisation des sections critiques dans l'ordre de génération
- Référencement intelligent pour éviter la duplication

**Stratégie de Génération :**
- Génération par sections avec validation intermédiaire si nécessaire
- Maintien de la cohérence malgré le chunking
- Assemblage final avec vérification de continuité

### Références de Fichiers

**Templates Primaires :**
- `sdd/templates/product.template.md` - Référence obligatoire pour product.md
- `sdd/templates/structure.template.md` - Référence obligatoire pour structure.md
- `sdd/templates/tech.template.md` - Référence obligatoire pour tech.md

**Instructions et Configuration :**
- `.github/instructions/context.instructions.md` - Logique métier détaillée
- `.github/prompts/context.prompt.md` - Prompts système de référence

## Politique d'Approbation

### Principe "Stop if No Explicit Approval"

**Règle Fondamentale :** Aucune progression n'est autorisée sans approbation explicite utilisateur

**Implémentation :**
1. Présentation du document généré avec message de validation standardisé
2. Attente de la réponse utilisateur (A, B, C, ou personnalisée)
3. Si réponse A (Validé) : Progression vers document suivant autorisée
4. Si réponse B (Révisions) : Implémentation des modifications et re-validation
5. Si réponse C (Questions) : Clarifications puis retour en validation
6. Si réponse ambiguë : Demande de clarification explicite

### Cycles de Révision Obligatoires

**Processus de Révision :**
- Analyse détaillée des demandes de modification
- Implémentation exhaustive des changements demandés
- Vérification de la cohérence avec les autres documents
- Re-génération du message de validation avec changements intégrés
- Nouvelle attente d'approbation explicite

**Limite de Cycles :** Pas de limite - Continuer jusqu'à satisfaction totale utilisateur

## Fonctionnalités Chat

### Capacités Spécifiques au Mode Conversationnel

**Références de Fichiers :**
- Accès direct aux templates via mentions `@sdd/templates/`
- Référencement contextuel des documents projet existants
- Citation précise des sections et paragraphes pertinents

**Sélection de Code :**
- Intégration de snippets de configuration existants
- Référencement de structures de projet établies
- Analyse des patterns de code pour cohérence technique

**Interactions Enrichies :**
- Suggestions proactives d'améliorations
- Détection d'incohérences entre documents
- Propositions de références croisées pertinentes

### Commandes Chat Étendues

**Commandes de Navigation :**
- `/show-context` - Afficher l'état actuel des trois documents
- `/compare-docs` - Comparer la cohérence entre documents
- `/template-diff` - Analyser les écarts avec les templates

**Commandes de Contrôle :**
- `/pause-generation` - Suspendre la génération pour révision manuelle
- `/resume-generation` - Reprendre après pause
- `/reset-context` - Redémarrer la génération depuis le début

## Limitations

### Restrictions du Mode Chat

**Fonctionnalités Non Supportées :**
- Modification directe des templates de référence dans `sdd/templates/`
- Génération de fichiers en dehors de `sdd/project/`
- Bypass des procédures de validation obligatoires
- Génération de code applicatif (hors documentation)

**Contraintes Techniques :**
- Respect des limites de contexte GitHub Copilot Chat
- Impossibilité de persistance d'état entre sessions différentes
- Dépendance aux capacités de lecture de fichiers de l'environnement

### Cas d'Erreur et Récupération

**Gestion des Erreurs :**
- Détection des générations incomplètes par timeout
- Récupération automatique avec reprise au dernier point validé
- Messages d'erreur explicites avec suggestions de résolution

**Procédures de Récupération :**
- Sauvegarde automatique des drafts en cours de génération
- Possibilité de reprise manuelle via commandes spécifiques
- Rollback vers dernière version validée si nécessaire

---

*Cette configuration conversationnelle optimise l'utilisation de GitHub Copilot Chat pour la génération de contexte SDD, garantissant une expérience utilisateur fluide et des résultats conformes à la méthodologie établie.*
