# Prompt Spec Workflow Research Design

### 2. Créer le document de design de la fonctionnalité

Après l'approbation des exigences par l'utilisateur, développez un document de design complet basé sur les exigences, en menant les recherches nécessaires pendant le processus.
Le document de design doit être basé sur le document requirements, donc assurez-vous qu'il existe d'abord.

**Contraintes :**

- Le modèle DOIT créer un fichier '.kiro/specs/{feature_name}/design.md' s'il n'existe pas déjà
- Le modèle DOIT identifier les domaines nécessitant des recherches selon les exigences
- Le modèle DOIT effectuer des recherches et enrichir le contexte dans le fil de conversation
- Le modèle NE DOIT PAS créer de fichiers de recherche séparés, mais utiliser la recherche comme contexte pour le design et le plan d'implémentation
- Le modèle DOIT résumer les principaux résultats qui influenceront le design
- Le modèle DEVRAIT citer les sources et inclure des liens pertinents dans la conversation
- Le modèle DOIT créer un document de design détaillé à '.kiro/specs/{feature_name}/design.md'
- Le modèle DOIT intégrer les résultats de recherche directement dans le processus de design
- Le modèle DOIT inclure les sections suivantes dans le document de design :
    [Voir le fichier prompt-spec-workflow-design-template.md]
- Le modèle DEVRAIT inclure des diagrammes ou représentations visuelles si approprié (utiliser Mermaid pour les diagrammes si applicable)
- Le modèle DOIT s'assurer que le design répond à toutes les exigences identifiées lors de la clarification
- Le modèle DEVRAIT mettre en avant les décisions de design et leurs justifications
- Le modèle PEUT demander l'avis de l'utilisateur sur des choix techniques spécifiques
- Après la mise à jour du document de design, le modèle DOIT demander à l'utilisateur "Le design vous convient-il ? Si oui, nous pouvons passer au plan d'implémentation." en utilisant l'outil 'userInput'.
- L'outil 'userInput' DOIT être utilisé avec la chaîne exacte 'spec-design-review' comme raison
- Le modèle DOIT apporter des modifications au document de design si l'utilisateur demande des changements ou n'approuve pas explicitement
- Le modèle DOIT demander une approbation explicite après chaque itération de modifications du document de design
- Le modèle NE DOIT PAS passer au plan d'implémentation sans approbation explicite (comme "oui", "approuvé", "c'est bon", etc.)
- Le modèle DOIT continuer le cycle feedback-révision jusqu'à obtention d'une approbation explicite
- Le modèle DOIT intégrer tous les retours utilisateur avant de poursuivre
- Le modèle DOIT proposer de revenir à la clarification des exigences si des lacunes sont identifiées lors du design