---
description: Guide pour utiliser Taskmaster afin de gérer des workflows de développement pilotés par les tâches
applyTo: "**/*"
---

# Workflow de développement Taskmaster

Ce guide décrit le processus standard pour utiliser Taskmaster afin de gérer des projets de développement logiciel. Il est rédigé comme un ensemble d'instructions pour vous, l'agent IA.

- **Votre posture par défaut** : Pour la plupart des projets, l'utilisateur peut travailler directement dans le contexte de tâche `master`. Vos actions initiales doivent opérer sur ce contexte par défaut sauf si un schéma multi-contexte clair émerge.
- **Votre objectif** : Votre rôle est d'améliorer le workflow de l'utilisateur en introduisant intelligemment des fonctionnalités avancées comme les **listes de tâches taguées** lorsque vous détectez le contexte approprié. Ne forcez pas l'utilisation des tags ; proposez-les comme une solution utile à un besoin spécifique.

## La boucle de base
Le cycle fondamental de développement que vous facilitez est :
1.  **`list`** : Affichez à l'utilisateur ce qu'il reste à faire.
2.  **`next`** : Aidez l'utilisateur à décider sur quoi travailler.
3.  **`show <id>`** : Fournissez les détails d'une tâche spécifique.
4.  **`expand <id>`** : Décomposez une tâche complexe en sous-tâches plus petites et gérables.
5.  **Implémenter** : L'utilisateur écrit le code et teste.
6.  **`update-subtask`** : Journalisez les progrès et découvertes pour l'utilisateur.
7.  **`set-status`** : Marquez les tâches et sous-tâches comme `done` une fois terminées.
8.  **Répétez**.

Toutes vos exécutions de commandes standards doivent opérer sur le contexte de tâche courant de l'utilisateur, qui est par défaut `master`.

---

## Processus standard du workflow de développement

### Workflow simple (point de départ par défaut)

Pour les nouveaux projets ou lorsque les utilisateurs débutent, travaillez dans le contexte de tag `master` :

-   Démarrez un nouveau projet avec l'outil `initialize_project` / `task-master init` ou `parse_prd` / `task-master parse-prd --input='<prd-file.txt>'` (voir @`taskmaster.instructions.md`) pour générer le tasks.json initial avec structure taguée
-   Configurez les ensembles de règles lors de l'initialisation avec le flag `--rules` (ex : `task-master init --rules vscode,windsurf`) ou gérez-les plus tard avec les commandes `task-master rules add/remove`
-   Commencez les sessions de codage avec `get_tasks` / `task-master list` (voir @`taskmaster.instructions.md`) pour voir les tâches courantes, leur statut et leurs IDs
-   Déterminez la prochaine tâche à traiter avec `next_task` / `task-master next` (voir @`taskmaster.instructions.md`)
-   Analysez la complexité des tâches avec `analyze_project_complexity` / `task-master analyze-complexity --research` (voir @`taskmaster.instructions.md`) avant de décomposer les tâches
-   Consultez le rapport de complexité avec `complexity_report` / `task-master complexity-report` (voir @`taskmaster.instructions.md`)
-   Sélectionnez les tâches selon les dépendances (toutes marquées 'done'), le niveau de priorité et l'ordre des IDs
-   Affichez les détails d'une tâche spécifique avec `get_task` / `task-master show <id>` (voir @`taskmaster.instructions.md`) pour comprendre les exigences d'implémentation
-   Décomposez les tâches complexes avec `expand_task` / `task-master expand --id=<id> --force --research` (voir @`taskmaster.instructions.md`) avec les flags appropriés comme `--force` (pour remplacer les sous-tâches existantes) et `--research`
-   Implémentez le code en suivant les détails de la tâche, les dépendances et les standards du projet
-   Marquez les tâches terminées avec `set_task_status` / `task-master set-status --id=<id> --status=done` (voir @`taskmaster.instructions.md`)
-   Mettez à jour les tâches dépendantes si l'implémentation diffère du plan initial avec `update` / `task-master update --from=<id> --prompt="..."` ou `update_task` / `task-master update-task --id=<id> --prompt="..."` (voir @`taskmaster.instructions.md`)

---

## Monter en niveau : Workflows multi-contexte pilotés par l'agent

Bien que le workflow de base soit puissant, votre principale opportunité de valeur est d'identifier quand introduire les **listes de tâches taguées**. Ces schémas sont vos outils pour créer un environnement de développement plus organisé et efficace, surtout si vous détectez un travail agentique ou parallèle au sein de la même session.

**Principe critique** : La plupart des utilisateurs ne devraient jamais voir de différence dans leur expérience. N'introduisez des workflows avancés que lorsque vous détectez des indicateurs clairs que le projet a évolué au-delà de la gestion simple des tâches.

### Quand introduire les tags : vos schémas de décision

Voici les schémas à surveiller. Lorsque vous en détectez un, proposez le workflow correspondant à l'utilisateur.

#### Schéma 1 : Branches Git de fonctionnalités simples
C'est le cas d'usage le plus courant et direct des tags.

- **Déclencheur** : L'utilisateur crée une nouvelle branche git (ex : `git checkout -b feature/user-auth`).
- **Votre action** : Proposez de créer un nouveau tag qui reflète le nom de la branche pour isoler les tâches de la fonctionnalité du `master`.
- **Prompt suggéré** : *"Je vois que vous avez créé une nouvelle branche nommée 'feature/user-auth'. Pour garder toutes les tâches associées bien organisées et séparées de votre liste principale, je peux créer un tag de tâche correspondant. Cela aide à éviter les conflits de fusion dans votre fichier `tasks.json` plus tard. Dois-je créer le tag 'feature-user-auth' ?"*
- **Outil à utiliser** : `task-master add-tag --from-branch`

#### Schéma 2 : Collaboration en équipe
- **Déclencheur** : L'utilisateur mentionne travailler avec des collègues (ex : "Mon collègue Alice gère le schéma de base de données", ou "Je dois relire le travail de Bob sur l'API.").
- **Votre action** : Suggérez de créer un tag séparé pour le travail de l'utilisateur afin d'éviter les conflits avec le contexte master partagé.
- **Prompt suggéré** : *"Puisque vous travaillez avec Alice, je peux créer un contexte de tâche séparé pour votre travail afin d'éviter les conflits. Ainsi, Alice peut continuer avec la liste master pendant que vous avez votre propre contexte isolé. Quand vous serez prêt à fusionner votre travail, nous pourrons coordonner les tâches vers master. Dois-je créer un tag pour votre travail actuel ?"*
- **Outil à utiliser** : `task-master add-tag my-work --copy-from-current --description="Mes tâches en collaboration avec Alice"`

#### Schéma 3 : Expérimentations ou refactorisations risquées
- **Déclencheur** : L'utilisateur veut essayer quelque chose qui pourrait ne pas être conservé (ex : "Je veux expérimenter le changement de notre bibliothèque de gestion d'état", ou "Refactorisons l'ancien module API, mais je veux garder les tâches actuelles en référence.").
- **Votre action** : Proposez de créer un tag sandbox pour le travail expérimental.
- **Prompt suggéré** : *"Cela semble être une excellente expérimentation. Pour garder ces nouvelles tâches séparées de notre plan principal, je peux créer un tag temporaire 'experiment-zustand' pour ce travail. Si nous décidons de ne pas poursuivre, nous pouvons simplement supprimer le tag sans affecter la liste principale. Ça vous va ?"*
- **Outil à utiliser** : `task-master add-tag experiment-zustand --description="Exploration de la migration vers Zustand"`

#### Schéma 4 : Grandes initiatives fonctionnelles (pilotées par PRD)
Approche structurée pour les nouvelles fonctionnalités ou épopées importantes.

- **Déclencheur** : L'utilisateur décrit une fonctionnalité importante, multi-étapes, qui bénéficierait d'un plan formel.
- **Votre action** : Proposez un workflow complet piloté par PRD.
- **Prompt suggéré** : *"Cela ressemble à une nouvelle fonctionnalité majeure. Pour la gérer efficacement, je propose de créer un contexte de tâche dédié. Voici le plan : je crée un nouveau tag appelé 'feature-xyz', puis nous rédigeons ensemble un document de spécifications produit (PRD) pour cadrer le travail. Une fois le PRD prêt, je générerai automatiquement toutes les tâches nécessaires dans ce nouveau tag. Qu'en pensez-vous ?"*
- **Flux d'implémentation** :
    1.  **Créer un tag vide** : `task-master add-tag feature-xyz --description "Tâches pour la nouvelle fonctionnalité XYZ"`. Vous pouvez aussi commencer par créer une branche git si besoin, puis créer le tag à partir de cette branche.
    2.  **Collaborer & créer le PRD** : Travaillez avec l'utilisateur pour créer un fichier PRD détaillé (ex : `.taskmaster/docs/feature-xyz-prd.txt`).
    3.  **Parser le PRD dans le nouveau tag** : `task-master parse-prd .taskmaster/docs/feature-xyz-prd.txt --tag feature-xyz`
    4.  **Préparer la nouvelle liste de tâches** : Suggérez ensuite `analyze-complexity` et `expand-all` pour les tâches nouvellement créées dans le tag `feature-xyz`.

#### Schéma 5 : Développement basé sur la version
Adaptez votre approche selon la maturité du projet indiquée par les noms de tags.

- **Tags Prototype/MVP** (`prototype`, `mvp`, `poc`, `v0.x`) :
  - **Approche** : Priorisez la rapidité et la fonctionnalité sur la perfection
  - **Génération de tâches** : Créez des tâches qui privilégient "faire fonctionner" plutôt que "faire parfaitement"
  - **Niveau de complexité** : Complexité plus faible, moins de sous-tâches, chemins d'implémentation plus directs
  - **Prompts de recherche** : Ajoutez le contexte "Ceci est un prototype - prioriser la rapidité et la fonctionnalité de base sur l'optimisation"
  - **Exemple d'ajout de prompt** : *"Puisque c'est pour le MVP, je vais me concentrer sur des tâches qui rendent la fonctionnalité principale rapidement opérationnelle plutôt que de sur-ingénier."*

- **Tags Production/Matures** (`v1.0+`, `production`, `stable`) :
  - **Approche** : Mettez l'accent sur la robustesse, les tests et la maintenabilité
  - **Génération de tâches** : Incluez une gestion complète des erreurs, des tests, de la documentation et de l'optimisation
  - **Niveau de complexité** : Complexité plus élevée, sous-tâches plus détaillées, chemins d'implémentation approfondis
  - **Prompts de recherche** : Ajoutez le contexte "Ceci est pour la production - prioriser la fiabilité, la performance et la maintenabilité"
  - **Exemple d'ajout de prompt** : *"Puisque c'est pour la production, je veillerai à ce que les tâches incluent une gestion correcte des erreurs, des tests et de la documentation."*

### Workflow avancé (tag & PRD)

**Quand effectuer la transition** : Reconnaissez quand le projet a évolué (ou a été initialisé sur un code existant) au-delà de la gestion simple des tâches. Surveillez ces indicateurs :
- L'utilisateur mentionne des collègues ou des besoins de collaboration
- Le projet atteint 15+ tâches avec des priorités mixtes
- L'utilisateur crée des branches de fonctionnalités ou mentionne des initiatives majeures
- L'utilisateur initialise Taskmaster sur une base de code existante et complexe
- L'utilisateur décrit de grandes fonctionnalités qui bénéficieraient d'une planification dédiée

**Votre rôle dans la transition** : Guidez l'utilisateur vers un workflow plus sophistiqué qui exploite les tags pour l'organisation et les PRD pour la planification complète.

#### Stratégie de liste master (focus valeur élevée)
Une fois la transition vers les workflows tagués, le tag `master` doit idéalement ne contenir que :
- **Livrables de haut niveau** apportant une valeur métier significative
- **Jalons majeurs** et fonctionnalités de type épopée
- **Travaux d'infrastructure critiques** impactant tout le projet
- **Éléments bloquants pour la release**

**À ne pas mettre dans master** :
- Sous-tâches d'implémentation détaillées (elles vont dans les tags spécifiques aux fonctionnalités)
- Travaux de refactoring (créez des tags dédiés comme `refactor-auth`)
- Fonctionnalités expérimentales (utilisez des tags `experiment-*`)
- Tâches spécifiques à un membre de l'équipe (utilisez des tags personnels)

#### Développement de fonctionnalités piloté par PRD

**Pour les nouvelles fonctionnalités majeures** :
1. **Identifier l'initiative** : Quand l'utilisateur décrit une fonctionnalité importante
2. **Créer un tag dédié** : `add_tag feature-[nom] --description="[Description de la fonctionnalité]"`
3. **Création collaborative du PRD** : Travaillez avec l'utilisateur pour créer un PRD complet dans `.taskmaster/docs/feature-[nom]-prd.txt`
4. **Parser & préparer** : 
   - `parse_prd .taskmaster/docs/feature-[nom]-prd.txt --tag=feature-[nom]`
   - `analyze_project_complexity --tag=feature-[nom] --research`
   - `expand_all --tag=feature-[nom] --research`
5. **Ajouter une référence dans master** : Créez une tâche de haut niveau dans `master` qui référence le tag de la fonctionnalité

**Pour l'analyse d'une base de code existante** :
Quand les utilisateurs initialisent Taskmaster sur des projets existants :
1. **Découverte du code** : Utilisez vos outils natifs pour produire un contexte approfondi sur la base de code. Vous pouvez utiliser l'outil `research` avec `--tree` et `--files` pour collecter des informations à jour en utilisant l'architecture existante comme contexte.
2. **Évaluation collaborative** : Travaillez avec l'utilisateur pour identifier les axes d'amélioration, la dette technique ou les nouvelles fonctionnalités
3. **Création stratégique du PRD** : Co-rédigez des PRD incluant :
   - Analyse de l'état actuel (basée sur votre recherche du code)
   - Améliorations ou nouvelles fonctionnalités proposées
   - Stratégie d'implémentation tenant compte du code existant
4. **Organisation par tags** : Parsez les PRD dans les tags appropriés (`refactor-api`, `feature-dashboard`, `tech-debt`, etc.)
5. **Curage de la liste master** : Ne gardez que les initiatives les plus précieuses dans master

Le flag `--append` de parse-prd permet à l'utilisateur de parser plusieurs PRD dans des tags ou entre tags. Les PRD doivent être ciblés et le nombre de tâches générées doit être choisi stratégiquement selon la complexité et le niveau de détail du PRD.

### Exemples de transition de workflow

**Exemple 1 : Simple → Travail en équipe**
```
Utilisateur : "Alice va aider sur le travail API"
Votre réponse : "Super ! Pour éviter les conflits, je vais créer un contexte de tâche séparé pour votre travail. Alice peut continuer avec la liste master pendant que vous travaillez dans votre propre contexte. Quand vous serez prêt à fusionner, nous pourrons coordonner les tâches."
Action : add_tag my-api-work --copy-from-current --description="Mes tâches API en collaboration avec Alice"
```

**Exemple 2 : Simple → Piloté par PRD**
```
Utilisateur : "Je veux ajouter un tableau de bord utilisateur complet avec analytics, gestion des utilisateurs et reporting"
Votre réponse : "Cela ressemble à une fonctionnalité majeure qui bénéficierait d'une planification détaillée. Je propose de créer un contexte dédié et nous pouvons rédiger un PRD ensemble pour capturer tous les besoins."
Actions : 
1. add_tag feature-dashboard --description="Tableau de bord utilisateur avec analytics et gestion"
2. Collaboration sur la création du PRD
3. parse_prd dashboard-prd.txt --tag=feature-dashboard
4. Ajouter une tâche 'Tableau de bord utilisateur' de haut niveau dans master
```

**Exemple 3 : Projet existant → Planification stratégique**
```
Utilisateur : "Je viens d'initialiser Taskmaster sur mon app React existante. Ça devient confus et je veux l'améliorer."
Votre réponse : "Laissez-moi rechercher votre base de code pour comprendre l'architecture actuelle, puis nous pourrons créer un plan stratégique d'amélioration."
Actions :
1. research "Architecture actuelle de l'app React et opportunités d'amélioration" --tree --files=src/
2. Collaboration sur le PRD d'amélioration basé sur les découvertes
3. Création de tags pour les différents axes d'amélioration (refactor-components, improve-state-management, etc.)
4. Ne garder que les initiatives majeures dans master
```

---

## Interaction principale : Serveur MCP vs. CLI

Taskmaster propose deux modes principaux d'interaction :

1.  **Serveur MCP (recommandé pour les outils intégrés)** :
    - Pour les agents IA et les environnements de développement intégrés (comme VS Code), l'interaction via le **serveur MCP est préférée**.
    - Le serveur MCP expose les fonctionnalités Taskmaster via un ensemble d'outils (ex : `get_tasks`, `add_subtask`).
    - Cette méthode offre de meilleures performances, des échanges structurés et une gestion d'erreur enrichie comparé au parsing CLI.
    - Voir @`mcp.instructions.md` pour les détails sur l'architecture MCP et les outils disponibles.
    - Une liste complète des outils MCP et leurs commandes CLI correspondantes se trouve dans @`taskmaster.instructions.md`.
    - **Redémarrez le serveur MCP** si la logique centrale dans `scripts/modules` ou les définitions d'outils MCP/directes changent.
    - **Note** : Les outils MCP supportent pleinement les listes de tâches taguées avec gestion complète des tags.

2.  **CLI `task-master` (pour les utilisateurs & secours)** :
    - La commande globale `task-master` offre une interface conviviale pour une interaction directe en terminal.
    - Elle sert aussi de solution de repli si le serveur MCP est inaccessible ou si une fonction spécifique n'est pas exposée via MCP.
    - Installez globalement avec `npm install -g task-master-ai` ou utilisez localement via `npx task-master-ai ...`.
    - Les commandes CLI reflètent souvent les outils MCP (ex : `task-master list` correspond à `get_tasks`).
    - Voir @`taskmaster.instructions.md` pour une référence détaillée des commandes.
    - **Listes de tâches taguées** : La CLI supporte pleinement le nouveau système tagué avec migration transparente.

## Fonctionnement du système de tags (pour référence)

- **Structure des données** : Les tâches sont organisées dans des contextes séparés (tags) comme "master", "feature-branch" ou "v2.0".
- **Migration silencieuse** : Les projets existants migrent automatiquement vers un tag "master" sans perturbation.
- **Isolation des contextes** : Les tâches dans différents tags sont totalement séparées. Les changements dans un tag n'affectent aucun autre tag.
- **Contrôle manuel** : L'utilisateur garde toujours le contrôle. Il n'y a pas de basculement automatique. Vous facilitez le changement via `use-tag <nom>`.
- **Support complet CLI & MCP** : Toutes les commandes de gestion des tags sont disponibles via CLI et MCP. Voir @`taskmaster.instructions.md` pour la liste complète.

---

## Analyse de la complexité des tâches

-   Exécutez `analyze_project_complexity` / `task-master analyze-complexity --research` (voir @`taskmaster.instructions.md`) pour une analyse complète
-   Consultez le rapport de complexité via `complexity_report` / `task-master complexity-report` (voir @`taskmaster.instructions.md`) pour une version formatée et lisible.
-   Concentrez-vous sur les tâches avec les scores de complexité les plus élevés (8-10) pour une décomposition détaillée
-   Utilisez les résultats de l'analyse pour déterminer l'allocation appropriée des sous-tâches
-   Notez que les rapports sont automatiquement utilisés par l'outil/commande `expand_task`

## Processus de décomposition des tâches

-   Utilisez `expand_task` / `task-master expand --id=<id>`. Il utilise automatiquement le rapport de complexité si disponible, sinon génère le nombre de sous-tâches par défaut.
-   Utilisez `--num=<nombre>` pour spécifier explicitement le nombre de sous-tâches, en remplaçant les recommandations par défaut ou du rapport de complexité.
-   Ajoutez le flag `--research` pour exploiter Perplexity AI pour une expansion basée sur la recherche.
-   Ajoutez le flag `--force` pour effacer les sous-tâches existantes avant d'en générer de nouvelles (par défaut : ajout).
-   Utilisez `--prompt="<contexte>"` pour fournir un contexte supplémentaire si besoin.
-   Révisez et ajustez les sous-tâches générées si nécessaire.
-   Utilisez l'outil `expand_all` ou `task-master expand --all` pour décomposer plusieurs tâches en attente à la fois, en respectant les flags comme `--force` et `--research`.
-   Si les sous-tâches doivent être complètement remplacées (indépendamment du flag `--force` sur `expand`), effacez-les d'abord avec `clear_subtasks` / `task-master clear-subtasks --id=<id>`.

## Gestion de la dérive d'implémentation

-   Lorsque l'implémentation diffère significativement du plan prévu
-   Lorsque des tâches futures doivent être modifiées suite à des choix d'implémentation actuels
-   Lorsque de nouvelles dépendances ou exigences émergent
-   Utilisez `update` / `task-master update --from=<futureTaskId> --prompt='<explication>\nMise à jour du contexte...' --research` pour mettre à jour plusieurs tâches futures.
-   Utilisez `update_task` / `task-master update-task --id=<taskId> --prompt='<explication>\nMise à jour du contexte...' --research` pour mettre à jour une tâche spécifique.

## Gestion du statut des tâches

-   Utilisez 'pending' pour les tâches prêtes à être traitées
-   Utilisez 'done' pour les tâches terminées et vérifiées
-   Utilisez 'deferred' pour les tâches reportées
-   Ajoutez des valeurs de statut personnalisées selon les workflows du projet

## Champs de structure de tâche

- **id** : Identifiant unique de la tâche (Exemple : `1`, `1.1`)
- **title** : Titre bref et descriptif (Exemple : `"Initialiser le dépôt"`)
- **description** : Résumé concis de la tâche (Exemple : `"Créer un nouveau dépôt, mettre en place la structure initiale."`)
- **status** : État actuel de la tâche (Exemple : `"pending"`, `"done"`, `"deferred"`)
- **dependencies** : IDs des tâches préalables (Exemple : `[1, 2.1]`)
    - Les dépendances sont affichées avec des indicateurs de statut (✅ pour terminé, ⏱️ pour en attente)
    - Cela permet d'identifier rapidement quelles tâches préalables bloquent le travail
- **priority** : Niveau d'importance (Exemple : `"high"`, `"medium"`, `"low"`)
- **details** : Instructions d'implémentation détaillées (Exemple : `"Utiliser l'ID client GitHub, gérer le callback, définir le token de session."`) 
- **testStrategy** : Approche de vérification (Exemple : `"Déployer et appeler l'endpoint pour confirmer la réponse 'Hello World'."`) 
- **subtasks** : Liste de tâches plus petites et spécifiques (Exemple : `[{"id": 1, "title": "Configurer OAuth", ...}]`) 
- Voir les détails de structure de tâche (précédemment liés à `tasks.instructions.md`).

## Gestion de la configuration (mise à jour)

La configuration Taskmaster est gérée via deux mécanismes principaux :

1.  **Fichier `.taskmaster/config.json` (principal)** :
    *   Situé à la racine du projet.
    *   Stocke la plupart des paramètres : sélection des modèles IA (principal, recherche, secours), paramètres (max tokens, température), niveau de log, sous-tâches/priorité par défaut, nom du projet, etc.
    *   **Paramètres du système tagué** : Inclut `global.defaultTag` (par défaut "master") et la section `tags` pour la configuration de gestion des tags.
    *   **Géré via la commande `task-master models --setup`.** Ne modifiez pas manuellement sauf si vous savez ce que vous faites.
    *   **Voir/définir les modèles spécifiques via la commande `task-master models` ou l'outil MCP `models`.**
    *   Créé automatiquement lors de la première exécution de `task-master models --setup` ou lors de la migration vers le système tagué.

2.  **Variables d'environnement (`.env` / `mcp.json`)** :
    *   Utilisées **uniquement** pour les clés API sensibles et certaines URLs d'endpoints.
    *   Placez les clés API (une par fournisseur) dans un fichier `.env` à la racine pour usage CLI.
    *   Pour l'intégration MCP/VS Code, configurez ces clés dans la section `env` de `.vscode/mcp.json`.
    *   Clés/variables disponibles : Voir `assets/env.example` ou la section Configuration dans la référence de commande (précédemment liée à `taskmaster.instructions.md`).

3.  **Fichier `.taskmaster/state.json` (état du système tagué)** :
    *   Suit le contexte de tag courant et l'état de migration.
    *   Créé automatiquement lors de la migration vers le système tagué.
    *   Contient : `currentTag`, `lastSwitched`, `migrationNoticeShown`.

**Important :** Les paramètres non liés aux clés API (comme la sélection des modèles, `MAX_TOKENS`, `TASKMASTER_LOG_LEVEL`) **ne sont plus configurés via les variables d'environnement**. Utilisez la commande `task-master models` (ou `--setup` pour la configuration interactive) ou l'outil MCP `models`.
**Si les commandes IA échouent en MCP** vérifiez que la clé API du fournisseur sélectionné est présente dans la section `env` de `.vscode/mcp.json`.
**Si les commandes IA échouent en CLI** vérifiez que la clé API du fournisseur sélectionné est présente dans le fichier `.env` à la racine du projet.

## Gestion des règles

Taskmaster supporte plusieurs ensembles de règles d'assistant de codage IA qui peuvent être configurés lors de l'initialisation du projet ou gérés ensuite :

- **Profils disponibles** : Claude Code, Cline, Codex, VS Code, Roo Code, Trae, Windsurf (claude, cline, codex, vscode, roo, trae, windsurf)
- **Lors de l'initialisation** : Utilisez `task-master init --rules vscode,windsurf` pour spécifier les ensembles de règles à inclure
- **Après l'initialisation** : Utilisez `task-master rules add <profils>` ou `task-master rules remove <profils>` pour gérer les ensembles de règles
- **Configuration interactive** : Utilisez `task-master rules setup` pour lancer un prompt interactif de sélection des profils de règles
- **Comportement par défaut** : Si aucun flag `--rules` n'est spécifié à l'initialisation, tous les profils disponibles sont inclus
- **Structure des règles** : Chaque profil crée son propre dossier (ex : `.github/instructions`, `.roo/rules`) avec les fichiers de configuration appropriés

## Détermination de la prochaine tâche

- Exécutez `next_task` / `task-master next` pour afficher la prochaine tâche à traiter.
- La commande identifie les tâches dont toutes les dépendances sont satisfaites
- Les tâches sont priorisées par niveau de priorité, nombre de dépendances et ID
- La commande affiche des informations complètes sur la tâche dont :
    - Détails de base et description
    - Instructions d'implémentation
    - Sous-tâches (si présentes)
    - Actions contextuelles suggérées
- Recommandé avant de commencer tout nouveau travail de développement
- Respecte la structure de dépendance du projet
- Garantit que les tâches sont réalisées dans l'ordre approprié
- Fournit des commandes prêtes à l'emploi pour les actions courantes

## Affichage des détails d'une tâche spécifique

- Exécutez `get_task` / `task-master show <id>` pour afficher une tâche spécifique.
- Utilisez la notation pointée pour les sous-tâches : `task-master show 1.2` (affiche la sous-tâche 2 de la tâche 1)
- Affiche des informations complètes similaires à la commande next, mais pour une tâche spécifique
- Pour les tâches parentes, affiche toutes les sous-tâches et leur statut actuel
- Pour les sous-tâches, affiche les informations du parent et la relation
- Fournit des actions contextuelles adaptées à la tâche spécifique
- Utile pour examiner les détails avant implémentation ou vérifier le statut

## Gestion des dépendances de tâches

- Utilisez `add_dependency` / `task-master add-dependency --id=<id> --depends-on=<id>` pour ajouter une dépendance.
- Utilisez `remove_dependency` / `task-master remove-dependency --id=<id> --depends-on=<id>` pour retirer une dépendance.
- Le système empêche les dépendances circulaires et les doublons
- Les dépendances sont vérifiées avant ajout ou suppression
- Les fichiers de tâches sont régénérés automatiquement après modification des dépendances
- Les dépendances sont visualisées avec des indicateurs de statut dans les listes et fichiers de tâches

## Réorganisation des tâches

- Utilisez `move_task` / `task-master move --from=<id> --to=<id>` pour déplacer des tâches ou sous-tâches dans la hiérarchie
- Cette commande supporte plusieurs cas :
  - Déplacer une tâche autonome pour en faire une sous-tâche (ex : `--from=5 --to=7`)
  - Déplacer une sous-tâche pour en faire une tâche autonome (ex : `--from=5.2 --to=7`) 
  - Déplacer une sous-tâche vers un parent différent (ex : `--from=5.2 --to=7.3`)
  - Réordonner les sous-tâches dans le même parent (ex : `--from=5.2 --to=5.4`)
  - Déplacer une tâche vers une nouvelle position d'ID inexistante (ex : `--from=5 --to=25`)
  - Déplacer plusieurs tâches à la fois avec des IDs séparés par des virgules (ex : `--from=10,11,12 --to=16,17,18`)
- Le système inclut des validations pour éviter la perte de données :
  - Permet le déplacement vers des IDs inexistants en créant des tâches placeholders
  - Empêche le déplacement vers des IDs existants qui ont déjà du contenu (pour éviter l'écrasement)
  - Valide l'existence des tâches sources avant déplacement
- Le système maintient les relations parent-enfant et l'intégrité des dépendances
- Les fichiers de tâches sont régénérés automatiquement après l'opération
- Cela offre une grande flexibilité pour organiser et affiner la structure des tâches au fur et à mesure de la compréhension du projet
- Particulièrement utile pour résoudre les conflits de fusion lorsque des équipes créent des tâches sur des branches séparées. Résolvez ces conflits très facilement en déplaçant vos tâches et en gardant celles des autres.

## Implémentation itérative des sous-tâches

Une fois qu'une tâche a été décomposée en sous-tâches via `expand_task` ou méthode similaire, suivez ce processus itératif pour l'implémentation :

1.  **Comprendre l'objectif (préparation)** :
    *   Utilisez `get_task` / `task-master show <subtaskId>` (voir @`taskmaster.instructions.md`) pour bien comprendre les objectifs et exigences spécifiques de la sous-tâche.

2.  **Exploration & planification initiale (itération 1)** :
    *   Première tentative de création d'un plan d'implémentation concret.
    *   Explorez la base de code pour identifier précisément les fichiers, fonctions, voire lignes à modifier.
    *   Déterminez les changements de code (diffs) et leurs emplacements.
    *   Rassemblez *tous* les détails pertinents de cette phase d'exploration.

3.  **Journaliser le plan** :
    *   Exécutez `update_subtask` / `task-master update-subtask --id=<subtaskId> --prompt='<plan détaillé>'`.
    *   Fournissez les *constatations complètes et détaillées* de la phase d'exploration dans le prompt. Incluez chemins de fichiers, numéros de ligne, diffs proposés, raisonnement et éventuels défis identifiés. N'omettez rien. L'objectif est de créer un log riche et horodaté dans le champ `details` de la sous-tâche.

4.  **Vérifier le plan** :
    *   Exécutez `get_task` / `task-master show <subtaskId>` à nouveau pour confirmer que le plan d'implémentation détaillé a bien été ajouté aux détails de la sous-tâche.

5.  **Commencer l'implémentation** :
    *   Définissez le statut de la sous-tâche avec `set_task_status` / `task-master set-status --id=<subtaskId> --status=in-progress`.
    *   Commencez à coder selon le plan journalisé.

6.  **Affiner et journaliser les progrès (itérations 2+)** :
    *   Au fil de l'implémentation, vous rencontrerez des défis, découvrirez des nuances ou confirmerez des approches efficaces.
    *   **Avant d'ajouter de nouvelles informations** : Passez brièvement en revue les détails déjà journalisés dans la sous-tâche (via `get_task` ou mémoire contextuelle) pour garantir que la mise à jour apporte des insights nouveaux et évite la redondance.
    *   **Régulièrement** utilisez `update_subtask` / `task-master update-subtask --id=<subtaskId> --prompt='<détails de mise à jour>\n- Ce qui a fonctionné...\n- Ce qui n'a pas fonctionné...'` pour ajouter de nouvelles constatations.
    *   **Journalisez notamment** :
        *   Ce qui a fonctionné (vérités fondamentales découvertes).
        *   Ce qui n'a pas fonctionné et pourquoi (pour éviter de répéter les erreurs).
        *   Extraits de code ou configurations qui ont réussi.
        *   Décisions prises, surtout si confirmées avec l'utilisateur.
        *   Toute déviation du plan initial et la raison.
    *   L'objectif est d'enrichir continuellement les détails de la sous-tâche, créant un journal du parcours d'implémentation qui aide l'IA (et les développeurs humains) à apprendre, s'adapter et éviter de répéter les erreurs.

7.  **Revue & mise à jour des règles (post-implémentation)** :
    *   Une fois l'implémentation terminée, passez en revue tous les changements de code et l'historique de chat pertinent.
    *   Identifiez tout nouveau schéma, convention ou bonne pratique établi durant l'implémentation.
    *   Créez ou mettez à jour les règles selon les directives internes (précédemment liées à `cursor_rules.instructions.md` et `self_improve.instructions.md`).

8.  **Marquer la tâche comme terminée** :
    *   Après vérification de l'implémentation et mise à jour des règles nécessaires, marquez la sous-tâche comme terminée : `set_task_status` / `task-master set-status --id=<subtaskId> --status=done`.

9.  **Commit des changements (si utilisation de Git)** :
    *   Stagiez les changements de code et tout fichier de règle mis à jour/créé (`git add .`).
    *   Rédigez un message de commit complet résumant le travail réalisé pour la sous-tâche, incluant code et règles.
    *   Exécutez la commande de commit directement dans le terminal (ex : `git commit -m 'feat(module): Implémentation de la fonctionnalité X pour la sous-tâche <subtaskId>\n\n- Détails des changements...\n- Règle Y mise à jour pour le schéma Z'`).
    *   Considérez si un Changeset est nécessaire selon les directives internes de versioning (précédemment liées à `changeset.instructions.md`). Si oui, exécutez `npm run changeset`, stagez le fichier généré et modifiez le commit ou créez-en un nouveau.

10. **Passer à la sous-tâche suivante** :
    *   Identifiez la prochaine sous-tâche (ex : via `next_task` / `task-master next`).

## Techniques d'analyse et de refactoring du code

- **Recherche de fonctions de haut niveau** :
    - Utile pour comprendre la structure d'un module ou planifier des refactorings.
    - Utilisez grep/ripgrep pour trouver les fonctions/constants exportés :
      `rg "export (async function|function|const) \w+"` ou schémas similaires.
    - Peut aider à comparer les fonctions entre fichiers lors de migrations ou identifier des conflits de nommage potentiels.

---
*Ce workflow fournit une ligne directrice générale. Adaptez-le selon les besoins spécifiques de votre projet et pratiques d'équipe.*