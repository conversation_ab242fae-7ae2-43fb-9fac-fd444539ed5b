# Instructions de Génération des Spécifications SDD

## Objectif et Portée

Ce document définit les instructions globales pour la génération séquentielle automatique complète des trois fichiers de spécification SDD dans le répertoire `sdd/specs/{feature_name}/` :
- `requirements.md` - Documentation des exigences fonctionnelles et non-fonctionnelles
- `design.md` - Architecture et conception détaillée du système
- `tasks.md` - Planification des tâches et jalons d'implémentation

## Méthodologie SDD

La méthodologie SDD (Specification-Driven Development) impose une approche séquentielle automatique continue :

1. **Phase Requirements** : Analyse et documentation automatique des exigences métier selon le format EARS
2. **Phase Design** : Conception automatique technique avec diagrammes Mermaid et spécifications d'architecture
3. **Phase Tasks** : Planification automatique détaillée avec estimations et dépendances

**Règle Fondamentale** : Progression automatique séquentielle Requirements → Design → Tasks sans interruption ni validation manuelle.

## Templates de Référence

Les fichiers de spécification DOIVENT être générés en utilisant les templates standardisés :

- **Requirements** : `sdd/templates/requirements.template.md`
  - Structure EARS pour les exigences fonctionnelles
  - Catégorisation des exigences non-fonctionnelles
  - Critères d'acceptation mesurables

- **Design** : `sdd/templates/design.template.md`
  - Architecture système avec diagrammes Mermaid
  - Spécifications des interfaces et API
  - Patterns de conception et contraintes techniques

- **Tasks** : `sdd/templates/tasks.template.md`
  - Décomposition en tâches atomiques
  - Estimations et dépendances
  - Jalons et critères de validation

### Cohérence avec la Phase 1
- **Context Instructions** : `.github/instructions/context.instructions.md`
- **Context Prompt** : `.github/prompts/context.prompt.md`
- **Context Chatmode** : `.github/chatmodes/context.chatmode.md`

**Intégration Obligatoire** : La phase 2 (spécifications) s'appuie directement sur les patterns et le contexte établis en phase 1. Les informations contextuelles validées alimentent automatiquement la génération des spécifications :
- **Product Context → Requirements** : Vision métier et personas vers exigences fonctionnelles
- **Structure Context → Design** : Architecture existante vers conception technique détaillée
- **Tech Context → Tasks** : Stack technique et contraintes vers planification réaliste

## Processus Obligatoire

### Étape 1 : Vérification des Prérequis
- Confirmer l'existence du répertoire `sdd/specs/`
- Valider le nom de la fonctionnalité (format kebab-case)
- Vérifier la disponibilité des templates de référence

### Étape 2 : Génération Initiale
- Générer le fichier complet sans poser de questions séquentielles
- Utiliser TOUTES les sections du template approprié
- Appliquer les formats spécifiques (EARS, Mermaid, checklist)
- Maintenir la cohérence terminologique avec le projet

### Étape 3 : Utilisation Complète des Templates
- Respecter la structure exacte des templates
- Remplir chaque section avec un contenu substantiel
- Adapter les exemples aux spécificités de la fonctionnalité
- Conserver les "Instructions Intégrées pour l'Agent IA"

### Étape 4 : Génération Séquentielle Automatique
- Générer automatiquement requirements.md avec confirmation automatique de progression
- Générer automatiquement design.md avec confirmation automatique de progression
- Générer automatiquement tasks.md avec finalisation automatique
- Documenter automatiquement les décisions et la traçabilité

### Étape 5 : Finalisation Automatique
- Passer automatiquement à chaque étape suivante sans approbation manuelle
- Maintenir automatiquement la traçabilité entre les phases
- Finaliser automatiquement avec spécifications SDD complètes

## Contraintes Techniques

### Structure des Fichiers
- **Chemin** : `sdd/specs/{feature_name}/`
- **Nommage** : kebab-case pour les noms de fonctionnalités
- **Extensions** : `.md` pour tous les fichiers de spécification

### Workflow Séquentiel Automatique
1. Requirements → Génération automatique → Progression automatique
2. Design → Génération automatique → Progression automatique
3. Tasks → Génération automatique → Finalisation automatique

### Formats Spécifiques
- **EARS** : "The system SHALL/SHOULD/MAY..." pour les requirements appliqué automatiquement
- **Mermaid** : Diagrammes intégrés pour l'architecture et les flux générés automatiquement
- **Checklist** : Format checkbox pour les tasks et validations créé automatiquement

## Génération Automatique Continue

### Workflow Automatique Requirements → Design → Tasks

**Processus de Génération :**
1. **Requirements.md** : Génération automatique → ✅ Progression automatique → Design
2. **Design.md** : Génération automatique → ✅ Progression automatique → Tasks  
3. **Tasks.md** : Génération automatique → ✅ Finalisation automatique → Spécifications complètes

**Confirmations Automatiques :**
- Notifications automatiques de progression à chaque étape
- Confirmations automatiques de completion sans attente
- Finalisation automatique avec spécifications SDD complètes

### Critères de Qualité Automatiques

**Validation Automatique :**
- Conformité automatique aux templates de référence
- Application automatique des formats spécifiques (EARS, Mermaid, Checklist)
- Vérification automatique de la cohérence inter-documents
- Maintien automatique de la traçabilité entre les phases

**Standards de Qualité :**
- Respect automatique de la structure exacte des templates
- Remplissage automatique de chaque section avec contenu substantiel
- Adaptation automatique des exemples aux spécificités de la fonctionnalité
- Conservation automatique des "Instructions Intégrées pour l'Agent IA"

## Intégration GitHub Copilot

### Limites de Contexte
- **Seuil Critique** : 80% du contexte token maximum autorisé
- **Monitoring Continu** : Vérification automatique du taux d'utilisation contexte
- **Alertes Préventives** : Signalement à 70% et arrêt forcé à 85%
- **Stratégies de Fallback** : Génération par sections si limite atteinte

### Stratégies de Chunking
- **Chunking Sémantique** : Découpage par sections logiques des templates
- **Chunking Hiérarchique** : Priorisation requirements > design > tasks
- **Chunking Adaptatif** : Ajustement dynamique selon la complexité
- **Chunking par Phases** : Génération séquentielle pour préserver le contexte

### Optimisations pour l'IA
- **Cache Intelligent** : Mémorisation des éléments validés précédemment
- **Références Compressées** : Liens symboliques vers templates plutôt que contenu complet
- **Extraction Ciblée** : Focus sur les sections en cours de modification
- **Synthèse Progressive** : Accumulation incrémentale des décisions validées

## Gestion des Erreurs

### Stratégies de Fallback
- **Niveau 1** : Utilisation des templates par défaut si templates spécialisés indisponibles
- **Niveau 2** : Génération basique sans template avec structure minimale garantie
- **Niveau 3** : Escalade vers validation manuelle avec assistance utilisateur

### Gestion des Conflits
- **Conflit de Templates** : Priorisation des templates les plus récents
- **Conflit de Versions** : Utilisation de la version stable de référence
- **Conflit de Contexte** : Réduction automatique du scope fonctionnel

### Récupération d'Erreurs
- **Sauvegarde Progressive** : Préservation des sections validées
- **Restauration Partielle** : Reprise au dernier point de validation
- **Régénération Ciblée** : Nouvelle génération des sections défaillantes uniquement

## Maintenance

### Mise à Jour des Templates
- Synchroniser les modifications des templates avec ces instructions
- Maintenir la cohérence des formats de validation
- Documenter les évolutions de la méthodologie SDD

### Évolution des Processus
- Intégrer les retours d'expérience utilisateur
- Optimiser les cycles de validation
- Améliorer la qualité des documents générés

### Traçabilité
- Conserver l'historique des modifications
- Documenter les décisions d'architecture
- Maintenir les liens entre requirements, design et tasks

---

**Rappel Important** : Ces instructions DOIVENT être suivies strictement. La génération automatique séquentielle sans interruption est la règle fondamentale de la méthodologie SDD automatisée.
