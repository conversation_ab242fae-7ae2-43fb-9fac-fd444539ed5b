# Guide complet des Hooks dans Claude Code

Les **hooks** de Claude Code sont des **commandes shell définies par l'utilisateur** qui s'exécutent automatiquement à des moments spécifiques du cycle de vie de Claude Code. Ils offrent un **contrôle déterministe** sur le comportement de Claude Code, garantissant que certaines actions se produisent toujours sans dépendre des décisions de l'IA.

## Introduction aux Hooks

### Qu'est-ce que les Hooks ?

Les hooks agissent comme des **déclencheurs configurables** qui vous permettent d'injecter votre propre logique personnalisée, scripts et commandes directement dans les opérations de Claude. Ils comblent le fossé entre l'assistance pilotée par l'IA et l'automatisation basée sur des règles.

### Avantages clés

- **Contrôle déterministe** : Les actions se produisent automatiquement sans que Claude ait besoin de les choisir
- **Intégration de workflow** : Connecte Claude Code de manière transparente aux outils de développement existants
- **Qualité du code** : Auto-formatage du code après les modifications ou application de conventions
- **Trail d'audit** : Journalisation de toutes les commandes exécutées pour la sécurité et le débogage
- **Gestion des notifications** : Alertes sur l'utilisation d'outils et les demandes de permissions

## Architecture et événements du cycle de vie

### Événements disponibles

Claude Code propose **8 événements de cycle de vie** où les hooks peuvent être déclenchés :

| Événement | Déclenchement | Utilisation typique |
|-----------|---------------|-------------------|
| `UserPromptSubmit` | Quand l'utilisateur soumet un prompt | Validation, logging, injection de contexte |
| `PreToolUse` | Avant l'exécution de tout outil | Blocage de commandes dangereuses, validation |
| `PostToolUse` | Après l'utilisation réussie d'un outil | Nettoyage, formatage, notifications |
| `Notification` | Quand Claude envoie des notifications | Alertes TTS, logging |
| `Stop` | Quand Claude finit de répondre | Finalisation de workflows, commits automatiques |
| `SubagentStop` | Quand les sous-agents finissent | Notifications de fin de sous-agent |
| `PreCompact` | Avant une opération de compactage | Sauvegarde de transcripts |
| `SessionStart` | Au démarrage d'une nouvelle session | Chargement de contexte de développement |

### Flux de données

Chaque événement de hook passe des **données JSON spécifiques** à votre hook qui peuvent être analysées et utilisées ou entièrement ignorées. Claude Code respecte certains codes de sortie et données reçues sur la sortie standard ou l'erreur standard.

## Configuration et structure des fichiers

### Emplacements de configuration

Les hooks sont configurés dans des fichiers JSON à trois emplacements possibles :

| Fichier | Portée | Priorité |
|---------|---------|----------|
| `.claude/settings.local.json` | Projet (local, non versionné) | Élevée |
| `.claude/settings.json` | Projet (partagé) | Moyenne |
| `~/.claude/settings.json` | Utilisateur (global) | Faible |

### Structure de configuration de base

Format JSON pour définir un hook :

```json
{
  "hooks": {
    "PostToolUse": [
      {
        "matcher": "edit_file",
        "hooks": [
          {
            "type": "command",
            "command": "ruff check --fix $CLAUDE_FILE_PATHS && black $CLAUDE_FILE_PATHS"
          }
        ]
      }
    ]
  }
}
```

### Configuration TOML alternative

Les hooks peuvent également être configurés en TOML :

```toml
[[hooks]]
event = "PostToolUse"

[hooks.matcher]
tool_name = "edit_file"
file_paths = ["*.py", "api/**/*.py"]

command = "ruff check --fix $CLAUDE_FILE_PATHS && black $CLAUDE_FILE_PATHS"
run_in_background = false
```

## Système de matchers et filtrage

### Types de matchers

Les **matchers** permettent de définir précisément quand un hook doit s'exécuter :

| Matcher | Description | Exemple |
|---------|-------------|---------|
| `tool_name` | Nom de l'outil utilisé | `"edit_file"`, `"run_command"` |
| `file_paths` | Patterns de fichiers (glob) | `["*.py"]`, `["src/**/*.jsx"]` |
| `query` | Correspondance dans l'entrée de l'outil | Commandes contenant `npm` |

### Exemples de matchers

**Matcher par outil spécifique :**
```json
{
  "matcher": "Bash",
  "hooks": [...]
}
```

**Matcher par fichiers Python :**
```json
{
  "matcher": {
    "tool_name": "edit_file",
    "file_paths": ["*.py", "tests/**/*.py"]
  },
  "hooks": [...]
}
```

**Matcher universel :**
```json
{
  "matcher": "*",
  "hooks": [...]
}
```

## Guide de création étape par étape

### Méthode 1 : Interface interactive

La méthode **recommandée** pour créer des hooks :

```bash
/hooks
```

Cette commande ouvre une interface interactive permettant de :
- Sélectionner l'événement de hook
- Configurer des matchers
- Définir les commandes à exécuter
- Choisir l'emplacement de stockage

### Méthode 2 : Configuration manuelle

**Étape 1** : Créer le fichier de configuration
```bash
mkdir -p .claude
touch .claude/settings.json
```

**Étape 2** : Ajouter la configuration du hook
```json
{
  "hooks": {
    "PostToolUse": [
      {
        "matcher": "edit_file",
        "hooks": [
          {
            "type": "command",
            "command": "echo 'File edited: $CLAUDE_FILE_PATHS'"
          }
        ]
      }
    ]
  }
}
```

**Étape 3** : Tester le hook
```bash
# Demander à Claude d'éditer un fichier
> Edit the README.md file to add a new section
```

## Variables d'environnement et contexte

### Variables disponibles

Claude Code fournit plusieurs **variables d'environnement** dans les hooks :

| Variable | Description | Disponibilité |
|----------|-------------|---------------|
| `$CLAUDE_FILE_PATHS` | Chemins des fichiers modifiés | Hooks d'édition de fichiers |
| `$CLAUDE_TOOL_NAME` | Nom de l'outil utilisé | Tous les hooks d'outils |
| `$CLAUDE_SESSION_ID` | Identifiant de session | Tous les hooks |

### Données JSON en entrée

Les hooks reçoivent des **données JSON via stdin** avec des informations spécifiques à l'événement :

**Exemple pour PreToolUse :**
```json
{
  "tool_name": "edit_file",
  "tool_input": {
    "path": "src/main.py",
    "content": "...",
    "description": "Add error handling"
  }
}
```

## Exemples pratiques et cas d'usage

### 1. Formatage automatique du code

Hook pour formater automatiquement les fichiers Python après modification :

```json
{
  "hooks": {
    "PostToolUse": [
      {
        "matcher": {
          "tool_name": "edit_file",
          "file_paths": ["*.py"]
        },
        "hooks": [
          {
            "type": "command",
            "command": "echo 'Formatting Python files...' && ruff check --fix $CLAUDE_FILE_PATHS && black $CLAUDE_FILE_PATHS"
          }
        ]
      }
    ]
  }
}
```

### 2. Exécution automatique des tests

Hook pour lancer automatiquement les tests après modification :

```json
{
  "hooks": {
    "PostToolUse": [
      {
        "matcher": {
          "tool_name": "edit_file",
          "file_paths": ["src/**/*.py", "tests/**/*.py"]
        },
        "hooks": [
          {
            "type": "command",
            "command": "pytest",
            "run_in_background": true
          }
        ]
      }
    ]
  }
}
```

### 3. Logging des commandes Bash

Hook pour journaliser toutes les commandes shell exécutées :

```json
{
  "hooks": {
    "PreToolUse": [
      {
        "matcher": "Bash",
        "hooks": [
          {
            "type": "command",
            "command": "jq -r '\"\\(.tool_input.command) - \\(.tool_input.description // \"No description\")\"' >> ~/.claude/bash-command-log.txt"
          }
        ]
      }
    ]
  }
}
```

### 4. Commits automatiques avec Jujutsu

Hook pour créer des commits automatiques après chaque session :

```json
{
  "hooks": {
    "Stop": [
      {
        "matcher": "",
        "hooks": [
          {
            "type": "command",
            "command": "jj commit -m 'automatic commit'"
          }
        ]
      }
    ]
  }
}
```

### 5. Blocage de commandes dangereuses

Hook de sécurité pour empêcher les commandes destructives :

```json
{
  "hooks": {
    "PreToolUse": [
      {
        "matcher": "Bash",
        "hooks": [
          {
            "type": "command",
            "command": "echo \"$input\" | jq -e '.tool_input.command | test(\"rm -rf|sudo|.env\") | not' || (echo 'Dangerous command blocked!' && exit 1)"
          }
        ]
      }
    ]
  }
}
```

## Hooks avancés et contrôle de flux

### Codes de sortie et contrôle

Les hooks peuvent **influencer le comportement de Claude** via les codes de sortie :

| Code de sortie | Effet |
|----------------|-------|
| `0` | Succès, continuer normalement |
| `1` | Erreur, bloquer l'opération (PreToolUse) |
| `2` | Avertissement, continuer avec notification |

### Hook de validation avec feedback

```bash
#!/bin/bash
# Validation script pour PreToolUse
input=$(cat)
command=$(echo "$input" | jq -r '.tool_input.command')

if [[ "$command" =~ rm.*-rf ]]; then
    echo "BLOCKED: Dangerous rm -rf command detected"
    exit 1
fi

echo "Command validated: $command"
exit 0
```

### Notifications audio avec TTS

Hook pour notifications audio lors des événements :

```json
{
  "hooks": {
    "Notification": [
      {
        "hooks": [
          {
            "type": "command",
            "command": "echo \"Claude needs your input\" | espeak"
          }
        ]
      }
    ]
  }
}
```

## Intégration avec les outils de développement

### Git Hooks et versioning

**Hook de commit automatique :**
```json
{
  "hooks": {
    "PostToolUse": [
      {
        "matcher": "edit_file",
        "hooks": [
          {
            "type": "command",
            "command": "git add . && git commit -m 'Auto-commit: $(date)' || true"
          }
        ]
      }
    ]
  }
}
```

### Intégration CI/CD

**Hook de déclenchement de build :**
```json
{
  "hooks": {
    "Stop": [
      {
        "hooks": [
          {
            "type": "command",
            "command": "curl -X POST $CI_WEBHOOK_URL -d '{\"trigger\": \"claude-session-complete\"}'"
          }
        ]
      }
    ]
  }
}
```

### Docker et environnements conteneurisés

**Hook de reconstruction d'image :**
```json
{
  "hooks": {
    "PostToolUse": [
      {
        "matcher": {
          "file_paths": ["Dockerfile", "docker-compose.yml"]
        },
        "hooks": [
          {
            "type": "command",
            "command": "docker-compose build",
            "run_in_background": true
          }
        ]
      }
    ]
  }
}
```

## Gestion des erreurs et débogage

### Logging des hooks

**Script de logging avancé :**
```bash
#!/bin/bash
HOOK_LOG="$HOME/.claude/hooks.log"
TIMESTAMP=$(date '+%Y-%m-%d %H:%M:%S')

# Log de l'événement
echo "[$TIMESTAMP] Hook triggered: $CLAUDE_HOOK_EVENT" >> "$HOOK_LOG"

# Capture de l'entrée JSON
input=$(cat)
echo "[$TIMESTAMP] Input: $input" >> "$HOOK_LOG"

# Votre logique de hook ici
echo "[$TIMESTAMP] Hook completed successfully" >> "$HOOK_LOG"
```

### Gestion des timeouts

Les hooks peuvent être configurés avec des timeouts pour éviter les blocages :

```json
{
  "type": "command",
  "command": "timeout 30s ./long-running-script.sh",
  "run_in_background": false
}
```

## Collections et templates de la communauté

### Hooks prêts à l'emploi

La communauté a développé plusieurs collections de hooks :

- **Hooks de sécurité** : Validation et blocage de commandes dangereuses
- **Hooks de qualité** : Formatage automatique et linting
- **Hooks de productivité** : Automatisation de tâches répétitives
- **Hooks de monitoring** : Surveillance et alertes

### Templates populaires

**Template de hook de développement :**
```json
{
  "hooks": {
    "PostToolUse": [
      {
        "matcher": "edit_file",
        "hooks": [
          {
            "type": "command",
            "command": "./hooks/format-and-test.sh"
          }
        ]
      }
    ],
    "PreToolUse": [
      {
        "matcher": "Bash",
        "hooks": [
          {
            "type": "command",
            "command": "./hooks/security-check.sh"
          }
        ]
      }
    ]
  }
}
```

## Bonnes pratiques et recommandations

### Conception de hooks efficaces

1. **Gardez les hooks rapides** : Évitez les opérations lentes qui bloquent Claude
2. **Utilisez `run_in_background`** pour les tâches longues comme les tests
3. **Gérez les erreurs gracieusement** : Utilisez `|| true` pour les commandes optionnelles
4. **Loggez les activités** : Maintenez des traces d'audit des hooks exécutés

### Sécurité et validation

1. **Validez les entrées** : Toujours vérifier les données JSON reçues
2. **Limitez les permissions** : N'accordez que les permissions nécessaires
3. **Sandbox les hooks** : Isolez les hooks critiques dans des conteneurs
4. **Auditez régulièrement** : Révisez les hooks pour détecter les problèmes de sécurité

### Performance et optimisation

1. **Optimisez les matchers** : Utilisez des patterns spécifiques plutôt que `*`
2. **Évitez la récursion** : Attention aux hooks qui déclenchent d'autres hooks
3. **Monitoring des ressources** : Surveillez l'utilisation CPU/mémoire des hooks
4. **Parallélisation** : Utilisez `run_in_background` pour les tâches indépendantes

## Débogage et maintenance

### Outils de débogage

**Vérification de la configuration :**
```bash
# Afficher la configuration actuelle
/hooks

# Vérifier la syntaxe JSON
jq . ~/.claude/settings.json
```

**Test de hooks :**
```bash
# Tester manuellement un hook
echo '{"tool_name": "edit_file", "tool_input": {"path": "test.py"}}' | ./my-hook.sh
```

### Problèmes courants

| Problème | Cause | Solution |
|----------|-------|----------|
| Hook ne se déclenche pas | Matcher incorrect | Vérifier les patterns et noms d'outils |
| Boucle infinie | Hook récursif | Ajouter des conditions de sortie |
| Performance dégradée | Hook lent | Utiliser `run_in_background` |
| Erreurs JSON | Format invalide | Valider avec `jq` |

Les hooks de Claude Code offrent un **contrôle déterministe et une automatisation puissante** qui transforment votre workflow de développement. En maîtrisant cette fonctionnalité, vous pouvez créer des environnements de développement hautement optimisés qui appliquent automatiquement vos standards de qualité, sécurité et productivité. Commencez par des hooks simples comme le formatage automatique, puis évoluez vers des systèmes plus complexes d'automatisation et de validation.