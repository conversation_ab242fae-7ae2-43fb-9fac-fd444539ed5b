# Spec-Copilot : Système d'Instructions SDD (SpecDrivenDevelopment) - Version GitHub Copilot

## Vue d'Ensemble

Ce document présente le système d'instructions d'agent IA qui combine les concepts de **persistance de contexte** et **SpecDrivenDevelopment** (workflow structuré de spécification) dans sa **Version GitHub Copilot** avec intégration native via `.github/instructions/` et `.github/chatmodes/`.

### Problématique

- Les agents IA sont facturés par requête
- Il faut maximiser la production de documents .md en une seule session
- Besoin de validation utilisateur sans déclencher de nouvelles requêtes
- Maintenir la cohérence et la qualité du workflow de spécification
- Intégration native avec l'écosystème GitHub

### Solution Proposée

Utilisation d'un système de validation intégré permettant à l'agent de poser des questions et obtenir des validations via des interactions directes dans la conversation sans stopper l'exécution ni déclencher de nouvelles requêtes, avec intégration native GitHub Copilot.

---

## Templates de Documents

### Structure des Templates

La structure SDD utilise des templates standardisés basés sur les fichiers `prompt-spec-*.md` existants pour garantir la cohérence et la qualité des documents générés :

#### Templates Contexte Projet
- **product.instructions.md** : Basé sur `steering/product.md`
  - Vision et objectifs du produit
  - Public cible et personas
  - Proposition de valeur unique
  - Métriques de succès

- **structure.instructions.md** : Basé sur `steering/structure.md`
  - Architecture générale du projet
  - Organisation des modules
  - Conventions de nommage
  - Structure des dossiers

- **tech.instructions.md** : Basé sur `steering/tech.md`
  - Stack technologique
  - Contraintes techniques
  - Standards de développement
  - Outils et frameworks

#### Templates Spécifications avec Instructions Précises

##### **requirements.template.md**
Basé sur `prompt-spec-requirements-example.md` et `prompt-spec-workflow-requirement-clarification.md`

**Instructions intégrées :**
- Génération initiale basée sur l'idée utilisateur SANS questions séquentielles
- Format EARS (Easy Approach to Requirements Syntax) obligatoire
- Structure hiérarchique numérotée avec :
  - User stories : "En tant que [rôle], je veux [fonctionnalité], afin que [bénéfice]"
  - Critères d'acceptation au format EARS
- Prise en compte des cas limites, UX, contraintes techniques
- Cycle feedback-révision jusqu'à approbation explicite
- Validation obligatoire avant passage au design

##### **design.template.md**
Basé sur `prompt-spec-workflow-design-template.md` et `prompt-spec-workflow-research-design.md`

**Instructions intégrées :**
- Recherche contextuelle intégrée (pas de fichiers séparés)
- Sections obligatoires du template design
- Diagrammes Mermaid si approprié
- Justification des décisions de design
- Réponse à toutes les exigences identifiées
- Cycle feedback-révision jusqu'à approbation explicite
- Validation obligatoire avant passage aux tâches

##### **tasks.template.md**
Basé sur `prompt-spec-workflow-example-plan.md` et `prompt-spec-workflow-implementation-plan.md`

**Instructions intégrées :**
- Conversion du design en prompts LLM orientés test
- Format liste numérotée à cocher (max 2 niveaux)
- Notation décimale pour sous-tâches (1.1, 1.2, 2.1)
- Chaque tâche doit inclure :
  - Objectif clair (écriture/modification/test de code)
  - Informations supplémentaires en sous-puces
  - Références spécifiques aux exigences
- Progression incrémentale sans code orphelin
- Une tâche à la fois, arrêt pour révision utilisateur

#### Instructions Workflow Complètes

**Basées sur `prompt-spec-workflow-overview.md` :**
- Méthodologie développement piloté par spécifications
- Validation utilisateur obligatoire à chaque étape
- Nom de fonctionnalité en kebab-case
- Processus itératif requirements → design → tasks

**Dépannage (`prompt-spec-workflow-troubleshooting.md`) :**
- Gestion des blocages lors de clarification
- Approches alternatives si informations manquantes
- Découpage en composants si complexité excessive

**Exécution des tâches (`prompt-spec-task-background.md`) :**
- Lecture obligatoire requirements.md, design.md, tasks.md
- Une tâche à la fois, arrêt pour révision
- Vérification contre toutes les exigences spécifiées

### Utilisation des Templates

**Version GitHub Copilot :**
```
Agent: "Je vais créer requirements.md en utilisant le template .github/templates/requirements.template.md"
```

---

## Architecture "Session Unifiée avec Validation Intégrée"

### Principe Fondamental

Une seule "macro-tâche" lancée à l'agent IA qui suit le processus SpecDrivenDevelopment de bout en bout, utilisant la structure GitHub native comme mémoire de travail et des validations intégrées dans la conversation.

### Structure GitHub Copilot : Intégration Native

```
.github/
├── instructions/           # Contexte global (équivalent project/)
│   ├── product.instructions.md     # Vision produit
│   ├── structure.instructions.md   # Architecture
│   └── tech.instructions.md        # Stack technique
├── chatmodes/             # Workflows SDD spécialisés
│   ├── spec-requirements.chatmode.md
│   ├── spec-design.chatmode.md
│   └── spec-implementation.chatmode.md
├── templates/             # Templates pour GitHub Copilot
│   ├── requirements.template.md # Template EARS (prompt-spec-requirements-example.md)
│   ├── design.template.md     # Template conception (prompt-spec-workflow-design-template.md)
│   └── tasks.template.md      # Template plan d'implémentation (prompt-spec-workflow-example-plan.md)
specs/                     # Spécifications (racine du projet)
└── {feature_name}/
    ├── requirements.md
    ├── design.md
    └── tasks.md
```

### Architecture Technique

```mermaid
flowchart TD
    A[Utilisateur : @workspace /sdd-full] --> B[Agent : Auto-chargement instructions globales]
    B --> C[Phase 1: Clarification des exigences]
    C --> D{Besoin de validation?}
    D -->|Oui| E[Agent: Pose question dans conversation]
    E --> F[Utilisateur répond directement]
    F --> G[Agent reçoit réponse et continue]
    G --> C
    D -->|Non| H[Phase 2: Recherche & Design]
    H --> I{Besoin de validation?}
    I -->|Oui| E
    I -->|Non| J[Phase 3: Plan d'implémentation]
    J --> K{Besoin de validation?}
    K -->|Oui| E
    K -->|Non| L[Génération documents .md finaux]
    L --> M[Session terminée - Tous documents produits]
```

### Composants Clés

#### Chatmode SDD Complet

```markdown
# INSTRUCTIONS CHATMODE SDD

Tu es un assistant expert en SpecDrivenDevelopment intégré à GitHub Copilot.

## RÔLE
Transformer une idée de fonctionnalité en spécification complète suivant la méthodologie SDD.

## PROCESSUS OBLIGATOIRE (basé sur prompt-spec-workflow-overview.md)
1. **Analyse du contexte** : Utilise @workspace pour lire les instructions globales
2. **Nom de fonctionnalité** : Format kebab-case obligatoire
3. **Phase Requirements** : Créer requirements.md avec méthodologie EARS
4. **Phase Design** : Créer design.md avec recherche intégrée
5. **Phase Tasks** : Créer tasks.md avec prompts LLM orientés test

### Phase 1: Requirements (prompt-spec-workflow-requirement-clarification.md)
- Génération initiale basée sur l'idée utilisateur SANS questions séquentielles
- Format EARS (Easy Approach to Requirements Syntax) obligatoire
- Structure hiérarchique numérotée avec :
  - User stories : "En tant que [rôle], je veux [fonctionnalité], afin que [bénéfice]"
  - Critères d'acceptation au format EARS
- Prise en compte des cas limites, UX, contraintes techniques
- Cycle feedback-révision jusqu'à approbation explicite
- Validation obligatoire avant passage au design

### Phase 2: Design (prompt-spec-workflow-research-design.md)
- Recherche contextuelle intégrée (pas de fichiers séparés)
- Sections obligatoires du template design
- Diagrammes Mermaid si approprié
- Justification des décisions de design
- Réponse à toutes les exigences identifiées
- Cycle feedback-révision jusqu'à approbation explicite
- Validation obligatoire avant passage aux tâches

### Phase 3: Tasks (prompt-spec-workflow-implementation-plan.md)
- Conversion du design en prompts LLM orientés test
- Format liste numérotée à cocher (max 2 niveaux)
- Notation décimale pour sous-tâches (1.1, 1.2, 2.1)
- Chaque tâche doit inclure :
  - Objectif clair (écriture/modification/test de code)
  - Informations supplémentaires en sous-puces
  - Références spécifiques aux exigences
- Progression incrémentale sans code orphelin
- Une tâche à la fois, arrêt pour révision utilisateur

## EXÉCUTION DES TÂCHES (prompt-spec-task-background.md)
- Lecture obligatoire requirements.md, design.md, tasks.md
- Une tâche à la fois, arrêt pour révision
- Vérification contre toutes les exigences spécifiées

## DÉPANNAGE (prompt-spec-workflow-troubleshooting.md)
- Gestion des blocages lors de clarification
- Approches alternatives si informations manquantes
- Découpage en composants si complexité excessive

## VALIDATION CONTINUE
Lorsque tu as besoin d'une clarification ou validation :
- Pose tes questions directement dans la conversation
- Utilise des formats clairs avec options multiples quand approprié
- Attends la réponse de l'utilisateur avant de continuer
- Demande confirmation explicite avant de passer à la phase suivante

## RÈGLES IMPORTANTES
- Validation utilisateur obligatoire à chaque étape
- Ne mentionnez pas explicitement le workflow à l'utilisateur
- Informez simplement quand vous terminez un document
- Processus itératif avec retours possibles aux étapes précédentes
- Posez des questions claires et précises pour éviter les ambiguïtés

## CONTRAINTES
- Respecter les conventions définies dans les instructions globales
- Utiliser la stack technique spécifiée
- Maintenir la cohérence avec l'architecture existante
- Générer tous les fichiers dans specs/{feature_name}/
```

#### Structure GitHub Complète

```
.github/
├── instructions/            # Contexte global (auto-chargé)
│   ├── product.instructions.md     # Vision et objectifs
│   ├── structure.instructions.md   # Architecture et conventions
│   └── tech.instructions.md        # Stack technique
├── chatmodes/              # Workflows SDD spécialisés
│   ├── sdd-full.chatmode.md        # Workflow complet
│   ├── sdd-requirements.chatmode.md # Phase exigences
│   ├── sdd-design.chatmode.md      # Phase design
│   └── sdd-tasks.chatmode.md       # Phase implémentation
specs/                      # Spécifications (racine projet)
└── {feature_name}/
    ├── requirements.md
    ├── design.md
    └── tasks.md
```

---

## Chatmodes Spécialisés

### Chatmode Requirements

```markdown
# CHATMODE SDD - REQUIREMENTS

Tu es un expert en analyse des exigences intégré à GitHub Copilot.

## MISSION
Transformer une idée de fonctionnalité en document requirements.md complet au format EARS.

## PROCESSUS
1. **Analyse contexte** : Utilise @workspace pour charger les instructions
2. **Clarification** : Pose des questions ciblées pour comprendre les besoins
3. **Génération** : Crée requirements.md avec format EARS strict
4. **Validation** : Demande confirmation avant finalisation

## FORMAT EARS OBLIGATOIRE
- User stories : "En tant que [rôle], je veux [fonctionnalité], afin que [bénéfice]"
- Critères d'acceptation détaillés
- Gestion des cas d'erreur
- Contraintes techniques et UX

## VALIDATION
- Questions directes dans la conversation
- Options multiples quand approprié
- Confirmation explicite avant finalisation
```

### Chatmode Design

```markdown
# CHATMODE SDD - DESIGN

Tu es un expert en architecture logicielle intégré à GitHub Copilot.

## MISSION
Transformer les exigences en design détaillé avec architecture et justifications.

## PROCESSUS
1. **Lecture requirements.md** : Analyse complète des exigences
2. **Recherche contextuelle** : Investigation des solutions techniques
3. **Design architecture** : Création du design avec diagrammes Mermaid
4. **Justification** : Explication des choix techniques
5. **Validation** : Confirmation avant passage aux tâches

## LIVRABLES
- Architecture détaillée
- Diagrammes Mermaid si approprié
- Justification des décisions
- Réponse à toutes les exigences

## VALIDATION
- Vérification contre requirements.md
- Questions de clarification si nécessaire
- Confirmation explicite avant finalisation
```

### Chatmode Tasks

```markdown
# CHATMODE SDD - TASKS

Tu es un expert en planification d'implémentation intégré à GitHub Copilot.

## MISSION
Transformer le design en plan d'implémentation avec prompts LLM orientés test.

## PROCESSUS
1. **Lecture design.md** : Analyse complète du design
2. **Décomposition** : Division en tâches implémentables
3. **Prompts LLM** : Création de prompts orientés test
4. **Organisation** : Structure hiérarchique avec notation décimale
5. **Validation** : Confirmation du plan complet

## FORMAT OBLIGATOIRE
- Liste numérotée à cocher (max 2 niveaux)
- Notation décimale pour sous-tâches (1.1, 1.2, 2.1)
- Objectifs clairs pour chaque tâche
- Références aux exigences
- Progression incrémentale

## VALIDATION
- Vérification contre design.md et requirements.md
- Plan complet avant finalisation
- Confirmation explicite
```

---

## Utilisation avec GitHub Copilot

### Commandes de Base

```bash
# Workflow complet
@workspace /sdd-full "Créer une fonctionnalité d'authentification"

# Phase spécifique - Requirements
@workspace /sdd-requirements "Analyser les besoins d'authentification"

# Phase spécifique - Design
@workspace /sdd-design "Concevoir l'architecture d'authentification"

# Phase spécifique - Tasks
@workspace /sdd-tasks "Planifier l'implémentation d'authentification"
```

### Exemple de Flux Complet

```
Utilisateur: @workspace /sdd-full "Créer une fonctionnalité d'authentification"

Agent: "J'ai analysé les instructions globales. Basé sur votre stack technique, 
dois-je inclure l'authentification sociale ?

Options :
A) Oui, avec Google et GitHub
B) Oui, avec d'autres providers  
C) Non, authentification locale uniquement"

Utilisateur: "A"

Agent: "Parfait. Je crée les fichiers dans specs/auth/..."
```

---

## Intégration Native GitHub

### Auto-chargement du Contexte

- **Instructions globales** : Chargement automatique via @workspace
- **Contexte projet** : Accès direct aux fichiers .github/instructions/
- **Templates** : Utilisation automatique des templates .github/templates/
- **Historique** : Intégration avec l'historique Git

### Avantages de l'Intégration

1. **Contexte automatique** : Plus besoin de charger manuellement le contexte
2. **Workflows natifs** : Chatmodes intégrés à l'IDE
3. **Collaboration** : Partage facile des workflows avec l'équipe
4. **Versioning** : Suivi des modifications via Git
5. **Découvrabilité** : Chatmodes visibles dans l'interface Copilot

---

## Architectures Alternatives

### Architecture "Pipeline Intelligent avec Checkpoints"

#### Principe

Diviser le processus en micro-sessions avec des checkpoints automatiques, permettant une reprise intelligente en cas d'interruption, optimisé pour GitHub Copilot.

#### Fonctionnement

1. **Initialisation** : L'agent analyse le contexte via @workspace
2. **Planification** : Création d'un plan de session avec checkpoints
3. **Exécution par blocs** : Traitement par phases avec validation directe
4. **Sauvegarde continue** : Commits automatiques à chaque checkpoint
5. **Reprise intelligente** : Capacité à reprendre depuis le dernier commit

### Architecture "Agent Orchestrateur Multi-Spécialisé"

#### Principe

Utilisation de chatmodes spécialisés qui s'orchestrent automatiquement via GitHub Copilot.

#### Composants

1. **Chatmode Orchestrateur** : `/sdd-full` - Gère le workflow global
2. **Chatmode Requirements** : `/sdd-requirements` - Spécialisé dans les exigences
3. **Chatmode Designer** : `/sdd-design` - Expert en architecture
4. **Chatmode Planner** : `/sdd-tasks` - Spécialisé dans la planification

---

## Implémentation Recommandée

### Phase 1 : Setup GitHub

1. **Créer la structure .github/**
2. **Configurer les instructions globales**
3. **Implémenter les chatmodes de base**
4. **Tester avec un cas simple**

### Phase 2 : Optimisation

1. **Affiner les chatmodes selon les retours**
2. **Ajouter les templates spécialisés**
3. **Optimiser l'intégration @workspace**

### Phase 3 : Évolution

1. **Ajouter des chatmodes spécialisés**
2. **Intégrer avec les GitHub Actions**
3. **Automatiser la validation des specs**

---

## Stratégies de Validation

### 1. Questions Structurées avec Copilot

```markdown
**Question de validation :**
Pour la fonctionnalité d'authentification, quelle approche préférez-vous ?

A) Authentification locale avec email/mot de passe
B) Authentification sociale (Google, GitHub)
C) Authentification hybride (locale + sociale)
D) Authentification par token/API uniquement

Répondez par la lettre correspondante (A, B, C, ou D).
```

### 2. Validation Progressive

```markdown
**Validation étape par étape :**

1. **Contexte analysé** ✓ (via @workspace)
   - Stack technique : React + Node.js
   - Base de données : PostgreSQL
   - Authentification actuelle : Aucune

2. **Exigences identifiées** (à valider)
   - Inscription/connexion utilisateur
   - Gestion des profils
   - Réinitialisation mot de passe
   - Sessions sécurisées

**Confirmez-vous ces exigences ? (Oui/Non/Modifications)**
```

### 3. Checkpoints de Validation

```markdown
**Checkpoint Requirements :**
J'ai terminé le document requirements.md avec :
- 8 user stories principales
- 24 critères d'acceptation EARS
- Gestion des cas d'erreur
- Contraintes de sécurité

**Souhaitez-vous réviser avant de passer au design ? (Oui/Non)**
```

---

## Métriques de Succès

### Efficacité
- **Réduction des requêtes** : Objectif 80% de réduction (de 5-10 requêtes à 1-2)
- **Temps de session** : Maintenir < 30 minutes par fonctionnalité complète
- **Qualité des documents** : Validation par checklist qualité automatisée

### Expérience Utilisateur
- **Fluidité du dialogue** : Questions claires et options structurées
- **Pertinence des questions** : Taux de questions utiles > 90%
- **Complétude des livrables** : 100% des documents requis générés
- **Validation efficace** : Processus de validation intégré et fluide

### Technique
- **Utilisation du contexte** : Optimisation pour rester < 80% de la limite
- **Cohérence des documents** : Validation croisée automatique
- **Traçabilité** : Historique complet des décisions via Git
- **Intégration GitHub** : Support natif des chatmodes et instructions

---

## Conclusion

La Version GitHub Copilot du système Spec-No-MCP offre :

1. **Intégration native avec l'écosystème GitHub**
2. **Instructions auto-chargées dans le contexte**
3. **Chatmodes spécialisés pour chaque phase SDD**
4. **Optimisé pour les workflows de développement**
5. **Validation conversationnelle directe** sans dépendance externe

### Avantages Clés

- **Intégration native** : Workflows intégrés à l'IDE
- **Contexte automatique** : Plus besoin de charger manuellement
- **Collaboration** : Partage facile avec l'équipe
- **Versioning** : Suivi des modifications via Git
- **Découvrabilité** : Chatmodes visibles dans Copilot
- **Économique** : Réduction significative du nombre de requêtes

### Utilisation Recommandée

1. **Projets GitHub** : Idéal pour les projets hébergés sur GitHub
2. **Équipes développement** : Workflows partagés et standardisés
3. **Intégration CI/CD** : Possibilité d'automatisation via GitHub Actions
4. **Documentation vivante** : Spécifications versionnées avec le code

Cette approche transforme GitHub Copilot en un véritable partenaire de conception, capable de mener un projet de spécification de bout en bout de manière autonome, économique et parfaitement intégrée aux outils de développement GitHub.