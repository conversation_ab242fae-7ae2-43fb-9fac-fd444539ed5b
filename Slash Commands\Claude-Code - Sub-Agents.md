# Guide complet des Sous-agents dans Claude Code

Les **sous-agents** (subagents) de Claude Code représentent une révolution dans l'assistance IA au développement, permettant de créer des **assistants IA spécialisés** qui peuvent être invoqués pour gérer des types spécifiques de tâches. Cette fonctionnalité transforme Claude Code d'un assistant unique en une équipe d'experts virtuels collaborant simultanément.

## Architecture et concepts fondamentaux

### Qu'est-ce que les sous-agents ?

Les sous-agents sont des **personnalités IA pré-configurées** auxquelles Claude Code peut déléguer des tâches. Chaque sous-agent possède :

- **Un objectif spécifique** et un domaine d'expertise
- **Sa propre fenêtre de contexte** séparée de la conversation principale
- **Des outils configurables** qu'il est autorisé à utiliser
- **Un prompt système personnalisé** qui guide son comportement

### Fonctionnement technique

Chaque sous-agent fonctionne comme une **entité indépendante** avec trois composants principaux :

1. **Prompt système** : Définit le rôle du sous-agent (ex: "analyser le code pour les vulnérabilités de sécurité")
2. **Fenêtre de contexte** : Stocke les informations spécifiques à la tâche, séparément des autres agents
3. **Ensemble d'outils** : Équipe le sous-agent d'utilitaires adaptés à son objectif

Les sous-agents **communiquent entre eux** et avec l'agent principal via un système de messagerie partagé, permettant une collaboration transparente dans les projets complexes.

## Avantages clés des sous-agents

### Préservation du contexte

Chaque sous-agent opère dans **son propre contexte**, évitant la pollution de la conversation principale et maintenant le focus sur les objectifs de haut niveau.

### Expertise spécialisée

Les sous-agents peuvent être **affinés avec des instructions détaillées** pour des domaines spécifiques, conduisant à des taux de succès plus élevés sur les tâches désignées.

### Réutilisabilité et scalabilité

Une fois créés, les sous-agents peuvent être **utilisés à travers différents projets** et partagés avec votre équipe pour des workflows cohérents. Ils offrent une **scalabilité** remarquable - ajoutez des sous-agents pour gérer de nouvelles tâches sans surcharger les ressources.

### Traitement parallèle

Les sous-agents supportent l'**exécution parallèle** avec jusqu'à **10 tâches simultanées**, accélérant significativement les workflows de développement.

## Configuration et création des sous-agents

### Démarrage rapide avec /agents

La méthode **recommandée** pour créer un sous-agent :

```bash
/agents
```

Cette commande ouvre une interface interactive permettant de :
- Voir tous les sous-agents disponibles (intégrés, utilisateur et projet)
- Créer de nouveaux sous-agents avec configuration guidée
- Éditer les sous-agents existants, y compris leur accès aux outils
- Supprimer des sous-agents personnalisés

### Structure de fichiers et emplacements

Les sous-agents sont stockés sous forme de **fichiers Markdown avec frontmatter YAML** dans deux emplacements possibles :

| Type | Emplacement | Portée | Priorité |
|------|-------------|---------|----------|
| Sous-agents projet | `.claude/agents/` | Projet courant | Élevée |
| Sous-agents utilisateur | `~/.claude/agents/` | Tous les projets | Faible |

En cas de conflit de noms, les sous-agents projet prennent la **priorité** sur les sous-agents utilisateur.

### Format de fichier et configuration

Structure type d'un fichier de sous-agent :

```markdown
---
name: nom-sous-agent
description: Description de quand ce sous-agent doit être invoqué
tools: outil1, outil2, outil3 # Optionnel - hérite de tous les outils si omis
---

Votre prompt système du sous-agent va ici. Cela peut être plusieurs paragraphes
et doit clairement définir le rôle, les capacités et l'approche du sous-agent
pour résoudre les problèmes.

Incluez des instructions spécifiques, des bonnes pratiques et les contraintes
que le sous-agent doit suivre.
```

### Champs de configuration

| Champ | Requis | Description |
|-------|--------|-------------|
| `name` | Oui | Identifiant unique utilisant lettres minuscules et tirets |
| `description` | Oui | Description en langage naturel de l'objectif du sous-agent |
| `tools` | Non | Liste d'outils spécifiques séparés par virgules |

## Types de sous-agents et spécialisations

### Sous-agents de développement

#### **Code Analyzer Agent**
Spécialisé dans l'analyse statique de code, détection de patterns et identification d'optimisations :
- Analyse de complexité cyclomatique
- Détection de code dupliqué
- Identification de dette technique
- Suggestions d'architecture

#### **Refactoring Agent**
Expert en transformation de code et modernisation :
- Extract method/class automatisé
- Implémentation de design patterns
- Modernisation de code legacy
- Optimisations de performance

#### **Test Generator Agent**
Génération automatique de tests unitaires et d'intégration :
- Tests unitaires automatiques
- Génération d'objets mock
- Identification des cas limites
- Analyse de couverture

### Sous-agents de qualité et sécurité

#### **Security Agent**
Audit de sécurité et détection de vulnérabilités :
- Vérification de conformité OWASP
- Vulnérabilités des dépendances
- Audit de validation d'entrée
- Patterns de codage sécurisé

#### **Debugger Specialist**
Spécialisé dans l'analyse des erreurs et le débogage :
- Capture de messages d'erreur et stack traces
- Identification des étapes de reproduction
- Isolation de l'emplacement de défaillance
- Implémentation de correctifs minimaux

### Sous-agents DevOps et documentation

#### **DevOps Agent**
Automatisation CI/CD et configuration infrastructure :
- Génération de pipelines CI/CD
- Configuration Docker
- Infrastructure as Code
- Configuration de monitoring

#### **Documentation Agent**
Création et maintenance de documentation technique :
- Documentation API automatique
- Génération de README
- Commentaires de code intelligents
- Diagrammes d'architecture

## Gestion et utilisation des sous-agents

### Délégation automatique

Claude Code délègue **proactivement** les tâches basé sur :
- La description de tâche dans votre requête
- Le champ `description` dans les configurations de sous-agent
- Le contexte actuel et les outils disponibles

Pour encourager une utilisation plus proactive, incluez des phrases comme **"use PROACTIVELY"** ou **"MUST BE USED"** dans votre champ `description`.

### Invocation explicite

Demandez un sous-agent spécifique en le mentionnant dans votre commande :

```bash
> Use the test-runner subagent to fix failing tests
> Have the code-reviewer subagent look at my recent changes  
> Ask the debugger subagent to investigate this error
```

### Chaînage de sous-agents

Pour des workflows complexes, vous pouvez **chaîner plusieurs sous-agents** :

```bash
> First use the code-analyzer subagent to find performance issues, then use the optimizer subagent to fix them
```

## Exemples pratiques de sous-agents

### Code Reviewer - Spécialiste révision de code

```markdown
---
name: code-reviewer
description: Expert code review specialist. Proactively reviews code for quality, security, and maintainability. Use immediately after writing or modifying code.
tools: Read, Grep, Glob, Bash
---

You are a senior code reviewer ensuring high standards of code quality and security.

When invoked:
1. Run git diff to see recent changes
2. Focus on modified files  
3. Begin review immediately

Review checklist:
- Code is simple and readable
- Functions and variables are well-named
- No duplicated code
- Proper error handling
- No exposed secrets or API keys
- Input validation implemented
- Good test coverage
- Performance considerations addressed

Provide feedback organized by priority:
- Critical issues (must fix)
- Warnings (should fix)
- Suggestions (consider improving)
```

### Data Scientist - Analyste de données

```markdown
---
name: data-scientist
description: Data analysis expert for SQL queries, BigQuery operations, and data insights. Use proactively for data analysis tasks and queries.
tools: Bash, Read, Write
---

You are a data scientist specializing in SQL and BigQuery analysis.

When invoked:
1. Understand the data analysis requirement
2. Write efficient SQL queries
3. Use BigQuery command line tools (bq) when appropriate
4. Analyze and summarize results
5. Present findings clearly

Key practices:
- Write optimized SQL queries with proper filters
- Use appropriate aggregations and joins
- Include comments explaining complex logic
- Format results for readability
- Provide data-driven recommendations
```

## Gestion avancée et optimisation

### Limitations de parallélisme

Les tests montrent que Claude Code peut supporter **jusqu'à 10 tâches parallèles** simultanément. Pour des workflows avec plus de 10 tâches, Claude Code utilise automatiquement un système de file d'attente, démarrant de nouvelles tâches dès qu'une tâche se termine.

### Configuration des outils

Vous avez deux options pour configurer les outils :
- **Omettre le champ `tools`** pour hériter de tous les outils du thread principal (par défaut)
- **Spécifier des outils individuels** comme liste séparée par virgules pour un contrôle plus granulaire

Les sous-agents peuvent accéder aux **outils MCP** des serveurs MCP configurés.

### Collections prêtes à l'emploi

La communauté a développé plusieurs collections de sous-agents :
- **50 sous-agents spécialisés** couvrant différents domaines d'expertise
- **Sous-agents de production** testés et optimisés
- **Templates réutilisables** pour des cas d'usage communs

## Bonnes pratiques et recommandations

### Conception de sous-agents efficaces

1. **Commencez avec Claude** : Il est **fortement recommandé** de générer votre sous-agent initial avec Claude puis l'itérer pour le personnaliser

2. **Concevez des sous-agents focalisés** : Créez des sous-agents avec des responsabilités uniques et claires plutôt que d'essayer de tout faire avec un seul

3. **Rédigez des prompts détaillés** : Incluez des instructions spécifiques, des exemples et des contraintes dans vos prompts système

4. **Limitez l'accès aux outils** : N'accordez que les outils nécessaires à l'objectif du sous-agent pour améliorer la sécurité et le focus

### Gestion d'équipe

- **Versionnez les sous-agents projet** : Ajoutez les sous-agents projet au contrôle de version pour que l'équipe puisse en bénéficier
- **Standardisez les processus** : Utilisez des sous-agents cohérents à travers l'équipe
- **Partagez les bonnes pratiques** : Documentez l'utilisation des sous-agents

### Considérations de performance

- **Efficacité contextuelle** : Les agents aident à préserver le contexte principal, permettant des sessions plus longues
- **Latence** : Les sous-agents démarrent avec une ardoise propre à chaque invocation et peuvent ajouter de la latence en rassemblant le contexte requis
- **Gestion des ressources** : Surveillez l'utilisation des ressources et les limites de l'API Anthropic

## Cas d'usage avancés et workflows

### Développement guidé par les tests (TDD)

```bash
> Use test-generator subagent to create comprehensive tests for the new feature, then use code-reviewer subagent to validate implementation
```

### Pipeline de déploiement automatisé

```bash  
> Have security-agent audit the codebase, then use devops-agent to prepare deployment configuration
```

### Analyse de performance complète

```bash
> Use code-analyzer subagent to identify performance bottlenecks, then use optimizer subagent to implement improvements
```

Les sous-agents de Claude Code transforment fondamentalement l'approche du développement assisté par IA, passant d'un assistant unique à une **équipe d'experts spécialisés**. Cette architecture modulaire offre une flexibilité, une précision et une efficacité inégalées dans les workflows de développement modernes. En maîtrisant les sous-agents, vous positionnez votre équipe à l'avant-garde du développement efficient et de haute qualité.