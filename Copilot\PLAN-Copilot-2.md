I have created the following plan after thorough exploration and analysis of the codebase. Follow the below plan verbatim. Trust the files and references. Do not re-verify what's written in the plan. Explore only when absolutely necessary. First implement all the proposed file changes and then I'll review all the changes together at the end.

### Observations

J'ai découvert que les fichiers de contexte de phase 1 mentionnés par l'utilisateur n'existent pas actuellement dans le répertoire `.github`. Le projet utilise une méthodologie SDD bien établie avec des templates structurés dans `sdd/templates/` qui contiennent des "Instructions Intégrées pour l'Agent IA" avec des processus obligatoires, validation utilisateur explicite, et cycles de révision itératifs. Les fichiers existants suivent un style français formel avec des patterns spécifiques (DOIT/DEVRAIT/PEUT capitalisés, sections standardisées, validation explicite). Le répertoire `.github` contient déjà `copilot-instructions.md` et un dossier `chatmodes/` avec un exemple de configuration.

### Approach

L'approche consiste à créer trois nouveaux fichiers GitHub Copilot pour la phase 2 (spécifications) en suivant les patterns établis dans le projet. Contrairement à ce que mentionne l'utilisateur, les fichiers de contexte de phase 1 n'existent pas actuellement, donc je baserai la cohérence sur les fichiers existants (`.github/copilot-instructions.md` et les prompts racine). Les nouveaux fichiers `spec.*` suivront la méthodologie SDD établie avec validation utilisateur explicite, références aux templates existants, et intégration harmonieuse avec l'écosystème GitHub Copilot du projet. La structure respectera les conventions françaises formelles et les patterns de validation standardisés identifiés dans les templates SDD.

### Reasoning

J'ai d'abord exploré la structure du répertoire `.github` pour comprendre l'état actuel et découvert que les fichiers de contexte mentionnés par l'utilisateur n'existent pas. J'ai ensuite analysé les fichiers existants (`.github/copilot-instructions.md`, `.github/chatmodes/4.1-Beast.chatmode.md`) pour comprendre le style et les conventions. J'ai également examiné tous les fichiers de prompts à la racine pour identifier les patterns de style et de structure. Enfin, j'ai analysé en détail les trois templates SDD (requirements, design, tasks) pour extraire les patterns d'instructions, formats de validation, et éléments structurels qui doivent être référencés dans les nouveaux fichiers GitHub Copilot.

## Mermaid Diagram

```mermaid
sequenceDiagram
    participant User as Utilisateur
    participant Copilot as GitHub Copilot
    participant Instructions as spec.instructions.md
    participant Templates as Templates SDD
    participant Specs as sdd/specs/

    User->>Copilot: Commande /spec ou /generate-spec
    Copilot->>Instructions: Lecture des instructions globales
    Instructions->>Templates: Référence aux templates (requirements, design, tasks)
    Copilot->>Templates: Analyse du template requirements
    Copilot->>Specs: Génération requirements.md (draft)
    Copilot->>User: Validation Requirements avec checklist
    User->>Copilot: Feedback/Approbation
    alt Si modifications demandées
        Copilot->>Specs: Révision requirements.md
        Copilot->>User: Nouvelle validation
    end
    Note over Copilot,User: Cycle répété jusqu'à approbation explicite
    Copilot->>Templates: Analyse du template design
    Copilot->>Specs: Génération design.md (draft)
    Copilot->>User: Validation Design avec checklist
    User->>Copilot: Feedback/Approbation
    alt Si modifications demandées
        Copilot->>Specs: Révision design.md
        Copilot->>User: Nouvelle validation
    end
    Copilot->>Templates: Analyse du template tasks
    Copilot->>Specs: Génération tasks.md (draft)
    Copilot->>User: Validation Tasks avec checklist
    User->>Copilot: Feedback/Approbation
    alt Si modifications demandées
        Copilot->>Specs: Révision tasks.md
        Copilot->>User: Nouvelle validation
    end
    Copilot->>User: Spécifications complètes générées
```

## Proposed File Changes

### .github\instructions\spec.instructions.md(NEW)

References: 

- sdd\templates\requirements.template.md
- sdd\templates\design.template.md
- sdd\templates\tasks.template.md
- prompt-system.md
- prompt-spec-workflow.md

Créer le fichier d'instructions globales pour la génération des trois fichiers de spécification SDD. Le contenu doit inclure :

**Structure du document :**
- En-tête avec objectif et portée (génération de `sdd/specs/{feature_name}/requirements.md`, `design.md`, `tasks.md`)
- Section "Méthodologie SDD" expliquant l'approche séquentielle requirements → design → tasks avec validation obligatoire à chaque étape
- Section "Templates de Référence" avec liens vers `sdd/templates/requirements.template.md`, `sdd/templates/design.template.md`, `sdd/templates/tasks.template.md`
- Section "Processus Obligatoire" détaillant les 5 étapes : vérification des prérequis, génération initiale sans questions séquentielles, utilisation complète des sections template, validation utilisateur avec cycles feedback-révision, passage à l'étape suivante uniquement après approbation explicite
- Section "Contraintes Techniques" spécifiant les chemins de fichiers (`sdd/specs/{feature_name}/`), les conventions de nommage kebab-case, le workflow séquentiel obligatoire, et l'utilisation des formats spécifiques (EARS pour requirements, Mermaid pour design, checklist pour tasks)
- Section "Validation et Révision" avec questions suggérées standardisées et format de validation cohérent avec les templates
- Section "Maintenance" pour les procédures de mise à jour des templates

**Style et format :**
- Utiliser le français formel avec les patterns DOIT/DEVRAIT/PEUT capitalisés identifiés dans les prompts existants
- Adopter la structure sectionnée avec titres markdown et listes numérotées
- Inclure des références croisées vers les templates plutôt que de dupliquer le contenu
- Maintenir la cohérence avec le style des fichiers `prompt-*.md` existants
- Utiliser la terminologie standardisée : "validation explicite", "cycle feedback-révision", "progression incrémentale"

### .github\prompts\spec.prompt.md(NEW)

References: 

- .github\instructions\spec.instructions.md(NEW)
- .github\copilot-instructions.md
- prompt-system.md
- prompt-spec-workflow.md

Créer le prompt système spécifique à GitHub Copilot pour déclencher la génération des fichiers de spécification. Le contenu doit inclure :

**Structure du document :**
- En-tête définissant le persona ("Tu es un agent SDD spécialisé dans la génération de spécifications techniques")
- Section "Rôle et Responsabilités" précisant la mission de génération des trois fichiers de spécification (requirements.md, design.md, tasks.md) selon la méthodologie SDD
- Section "Déclencheurs" avec mots-clés ou commandes que l'utilisateur peut utiliser (ex: `/spec`, `/generate-spec`, `/sdd-spec`, `/requirements`, `/design`, `/tasks`)
- Section "Règles de Sortie" spécifiant : pas de divagations, respect des limites de taille, utilisation du contexte < 80%, référence obligatoire à `spec.instructions.md` pour la logique métier détaillée
- Section "Processus" résumant les étapes principales du workflow séquentiel avec renvoi vers les instructions détaillées
- Section "Validation" rappelant l'obligation de validation explicite utilisateur à chaque étape et les cycles de révision itératifs
- Section "Limites" précisant les contraintes de contexte, les cas d'usage supportés, et l'intégration avec les templates SDD existants

**Style et format :**
- Ton formel et directif aligné avec les prompts existants dans `prompt-system.md` et autres fichiers de la racine
- Utiliser le français pour la cohérence avec le reste du projet
- Format markdown avec sections claires et hiérarchie logique
- Inclure des exemples de commandes utilisateur et de réponses attendues
- Référencer explicitement `spec.instructions.md` pour éviter la duplication de logique
- Maintenir la concision tout en étant complet et précis
- Utiliser les patterns de validation standardisés identifiés dans les templates

### .github\chatmodes\spec.chatmode.md(NEW)

References: 

- .github\chatmodes\4.1-Beast.chatmode.md
- sdd\templates\requirements.template.md
- sdd\templates\design.template.md
- sdd\templates\tasks.template.md
- .github\copilot-instructions.md

Créer la configuration conversationnelle pour GitHub Copilot Chat lors de la génération de spécifications. Le contenu doit inclure :

**Structure du document :**
- En-tête expliquant l'objectif de configuration du mode conversationnel pour la génération de spécifications SDD
- Section "Modes Acceptés" définissant les modes supportés : `draft` (génération initiale), `review` (révision), `refine` (affinement), `validate` (validation), `sequential` (workflow complet)
- Section "Templates de Validation" avec les formats exacts de messages de validation :
  - "**Validation Requirements :** J'ai terminé le document requirements.md avec : [checklist détaillée]. Cette documentation des exigences vous convient-elle ?"
  - "**Validation Design :** J'ai terminé le document design.md avec : [checklist détaillée]. Cette documentation de design vous convient-elle ?"
  - "**Validation Tasks :** J'ai terminé le document tasks.md avec : [checklist détaillée]. Cette documentation des tâches vous convient-elle ?"
- Section "Gestion du Contexte" avec rappels des limites (< 80% des tokens), stratégies d'optimisation (résumés partiels, extraits ciblés), et gestion des fichiers volumineux
- Section "Politique d'Approbation" implémentant le principe "stop if no explicit approval" avec cycles de révision obligatoires et progression séquentielle conditionnée
- Section "Fonctionnalités Chat" détaillant les capacités spécifiques au mode conversationnel (références de fichiers, sélection de code, intégration avec templates, workflow guidance)
- Section "Workflow Séquentiel" expliquant la progression requirements → design → tasks avec validation à chaque étape
- Section "Limitations" précisant ce qui ne peut pas être fait en mode chat et les cas nécessitant une approche différente

**Style et format :**
- Style cohérent avec les patterns de validation existants dans les templates SDD
- Utiliser le français pour la cohérence avec le projet
- Format markdown structuré avec sections claires
- Inclure des exemples concrets de messages de validation et de cycles de révision
- Référencer les questions de validation des templates originaux pour maintenir la cohérence
- Maintenir la clarté et la précision des instructions conversationnelles
- S'inspirer du style de `4.1-Beast.chatmode.md` pour la structure mais adapter au contexte SDD