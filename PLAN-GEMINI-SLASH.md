I have created the following plan after thorough exploration and analysis of the codebase. Follow the below plan verbatim. Trust the files and references. Do not re-verify what's written in the plan. Explore only when absolutely necessary. First implement all the proposed file changes and then I'll review all the changes together at the end.

### Observations

J'ai analysé la structure existante du projet et identifié que les commandes Claude existantes dans `.claude/commands/` utilisent une méthodologie d'automatisation séquentielle stricte pour générer des documents SDD. Le projet utilise actuellement des fichiers Markdown pour les commandes, mais Gemini CLI nécessite des fichiers TOML avec une structure spécifique. Aucun conflit de nommage n'existe avec les commandes actuelles, et aucune structure `.gemini/commands/` n'est présente dans le projet.

### Approach

L'approche consiste à créer deux commandes slash Gemini en format TOML qui reproduisent fidèlement les fonctionnalités des commandes Claude existantes. Je vais créer une structure `.gemini/commands/sdd/` pour organiser les commandes SDD, puis implémenter `context.toml` et `spec.toml` avec les prompts d'automatisation appropriés. Les commandes utiliseront la syntaxe `{{args}}` pour les arguments et respecteront strictement les règles d'automatisation séquentielle sans validation manuelle. La documentation sera mise à jour pour inclure ces nouvelles commandes.

### Reasoning

J'ai d'abord exploré la structure du projet pour comprendre l'organisation existante et identifier les fichiers de commandes Claude à reproduire. J'ai ensuite analysé le guide Gemini CLI pour comprendre le format TOML requis et les conventions de nommage. J'ai examiné les templates SDD pour comprendre le système de génération de documents et les variables utilisées. Enfin, j'ai vérifié les commandes existantes pour éviter les conflits de nommage et confirmer l'absence de structure `.gemini/commands/`.

## Mermaid Diagram

sequenceDiagram
    participant User as Utilisateur
    participant Gemini as Gemini CLI
    participant Context as /sdd:context
    participant Spec as /sdd:spec
    participant Templates as Templates SDD
    participant Output as Fichiers Générés

    Note over User,Output: Commande Context
    User->>Gemini: /sdd:context
    Gemini->>Context: Exécuter commande
    Context->>Templates: Lire product.template.md
    Context->>Output: Générer sdd/project/product.md
    Context->>Templates: Lire structure.template.md
    Context->>Output: Générer sdd/project/structure.md
    Context->>Templates: Lire tech.template.md
    Context->>Output: Générer sdd/project/tech.md
    Context->>User: Finalisation automatique

    Note over User,Output: Commande Spec
    User->>Gemini: /sdd:spec user-auth
    Gemini->>Spec: Exécuter avec args="user-auth"
    Spec->>Templates: Lire requirements.template.md
    Spec->>Output: Générer sdd/specs/user-auth/requirements.md
    Spec->>Templates: Lire design.template.md
    Spec->>Output: Générer sdd/specs/user-auth/design.md
    Spec->>Templates: Lire tasks.template.md
    Spec->>Output: Générer sdd/specs/user-auth/tasks.md
    Spec->>User: Finalisation automatique

## Proposed File Changes

### .gemini(NEW)

Créer le répertoire racine `.gemini` pour les configurations Gemini CLI du projet. Ce répertoire contiendra les commandes slash personnalisées spécifiques au projet selon les conventions Gemini CLI.

### .gemini\commands(NEW)

Créer le répertoire `commands` sous `.gemini` pour contenir les fichiers TOML des commandes slash. Cette structure suit les conventions project-scoped de Gemini CLI.

### .gemini\commands\sdd(NEW)

Créer le sous-répertoire `sdd` pour organiser les commandes liées à la méthodologie Spec-Driven Development. Cette organisation permettra d'avoir des commandes namespacées `/sdd:context` et `/sdd:spec`.

### .gemini\commands\sdd\context.toml(NEW)

References: 

- .claude\commands\context.md
- sdd\templates\product.template.md
- sdd\templates\structure.template.md
- sdd\templates\tech.template.md

Créer le fichier TOML pour la commande `/sdd:context` qui reproduit exactement la fonctionnalité de `c:/Users/<USER>/Documents/GitHub/Reproduced/Kiro-Spec-SDD/.claude/commands/context.md`. Le fichier doit contenir :

- Section `[command]` avec `name = "context"`
- Prompt complet qui réplique la logique d'automatisation séquentielle
- Instructions pour générer automatiquement `sdd/project/product.md`, `sdd/project/structure.md`, et `sdd/project/tech.md`
- Règles d'automatisation : génération séquentielle Product → Structure → Tech sans interruption ni validation manuelle
- Utilisation des templates `sdd/templates/product.template.md`, `sdd/templates/structure.template.md`, et `sdd/templates/tech.template.md`
- Contraintes techniques : destination obligatoire `sdd/project/`, nommage standard, lecture complète des templates
- Critères de qualité automatiques : conformité aux templates, cohérence inter-documents, complétude des sections
- Instructions d'exécution : vérification du répertoire, analyse du contexte, lecture des templates, génération séquentielle automatique

Le prompt doit être en français et respecter exactement la méthodologie SDD décrite dans le fichier Claude original.

### .gemini\commands\sdd\spec.toml(NEW)

References: 

- .claude\commands\spec.md
- sdd\templates\requirements.template.md
- sdd\templates\design.template.md
- sdd\templates\tasks.template.md

Créer le fichier TOML pour la commande `/sdd:spec` qui reproduit exactement la fonctionnalité de `c:/Users/<USER>/Documents/GitHub/Reproduced/Kiro-Spec-SDD/.claude/commands/spec.md`. Le fichier doit contenir :

- Section `[command]` avec `name = "spec"`
- Prompt complet utilisant `{{args}}` pour le nom de la fonctionnalité (remplace `$ARGUMENTS` du fichier Claude)
- Instructions pour générer automatiquement les trois fichiers dans `sdd/specs/{{args}}/` : `requirements.md`, `design.md`, et `tasks.md`
- Règles d'automatisation : progression automatique séquentielle Requirements → Design → Tasks sans interruption ni validation manuelle
- Utilisation des templates `sdd/templates/requirements.template.md`, `sdd/templates/design.template.md`, et `sdd/templates/tasks.template.md`
- Contraintes techniques : chemin obligatoire `sdd/specs/{feature_name}/`, nommage kebab-case pour les fonctionnalités, utilisation intégrale des templates
- Formats spécifiques : EARS pour requirements, diagrammes Mermaid automatiques pour design, format checklist automatique pour tasks
- Critères de qualité automatiques : conformité aux templates, application des formats spécifiques, cohérence inter-documents
- Instructions d'exécution : vérification du répertoire `sdd/specs/`, validation du nom de fonctionnalité (kebab-case), lecture des templates, génération séquentielle automatique

Le prompt doit être en français et respecter exactement la méthodologie SDD décrite dans le fichier Claude original, avec la substitution appropriée de `$ARGUMENTS` par `{{args}}`.

### Slash Commands\Gemini-CLI - Slash Commands.md(MODIFY)

Ajouter une nouvelle section "Commandes SDD (Spec-Driven Development)" vers la fin du document (après la section "Bonnes pratiques" et avant "Pour commencer"). Cette section doit documenter les deux nouvelles commandes slash :

**Contenu à ajouter :**

```markdown
## Commandes SDD (Spec-Driven Development)

### Automatisation du Contexte Projet

La commande `/sdd:context` génère automatiquement la documentation de contexte projet selon la méthodologie SDD :

```bash
/sdd:context
```

Cette commande crée séquentiellement et automatiquement :
- `sdd/project/product.md` - Documentation produit et vision
- `sdd/project/structure.md` - Architecture et organisation du projet
- `sdd/project/tech.md` - Spécifications techniques et implémentation

### Automatisation des Spécifications

La commande `/sdd:spec` génère automatiquement les spécifications pour une fonctionnalité :

```bash
/sdd:spec user-authentication
/sdd:spec payment-processing
```

Cette commande crée séquentiellement dans `sdd/specs/{feature-name}/` :
- `requirements.md` - Exigences au format EARS
- `design.md` - Design technique avec diagrammes Mermaid
- `tasks.md` - Plan d'implémentation en checklist

### Caractéristiques de l'Automatisation SDD

- **Génération séquentielle automatique** : Aucune validation manuelle entre les étapes
- **Respect strict des templates** : Utilisation complète des templates SDD
- **Nommage kebab-case** : Les noms de fonctionnalités doivent être en kebab-case
- **Traçabilité complète** : Liens entre spécifications et implémentation
- **Méthodologie banque de mémoire** : Centralisation de l'information projet
```

Cette section doit s'intégrer naturellement avec le style et la structure existante du document, en utilisant le même niveau de détail et les mêmes conventions de formatage.