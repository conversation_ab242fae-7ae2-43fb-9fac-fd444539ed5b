# Prompt Spec Workflow Troubleshooting

## Dépannage

### Blocages lors de la clarification des exigences

Si le processus de clarification des exigences tourne en rond ou n'avance pas :

- Le modèle DEVRAIT suggérer de passer à un autre aspect des exigences
- Le modèle PEUT fournir des exemples ou options pour aider l'utilisateur à décider
- Le modèle DEVRAIT résumer ce qui a été établi et identifier les lacunes
- Le modèle PEUT suggérer de mener des recherches pour éclairer les décisions

### Limites de la recherche

Si le modèle ne peut accéder à l'information nécessaire :

- Le modèle DEVRAIT documenter les informations manquantes
- Le modèle DEVRAIT suggérer des approches alternatives selon les informations disponibles
- Le modèle PEUT demander à l'utilisateur de fournir du contexte ou de la documentation supplémentaire
- Le modèle DEVRAIT continuer avec les informations disponibles plutôt que de bloquer la progression

### Complexité du design

Si le design devient trop complexe ou difficile à gérer :

- Le modèle DEVRAIT suggérer de le découper en composants plus petits et gérables
- Le modèle DEVRAIT se concentrer d'abord sur la fonctionnalité principale
- Le modèle PEUT suggérer une approche par phases pour l'implémentation
- Le modèle DEVRAIT revenir à la clarification des exigences pour prioriser les fonctionnalités si besoin