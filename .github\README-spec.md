# Configuration GitHub Copilot - SDD Complète (Phase 1 + Phase 2)

## Vue d'ensemble

Cette configuration permet à GitHub Copilot de gérer l'intégralité du workflow SDD (Specification-Driven Development) en deux phases complémentaires :

### Phase 1 - Contexte (Fondations)
- `sdd/project/product.md` - Vision produit et objectifs métier
- `sdd/project/structure.md` - Architecture et organisation projet
- `sdd/project/tech.md` - Spécifications techniques et contraintes

### Phase 2 - Spécifications (Détails)
- `sdd/specs/{feature_name}/requirements.md` - Exigences fonctionnelles et non-fonctionnelles
- `sdd/specs/{feature_name}/design.md` - Architecture et conception technique détaillée
- `sdd/specs/{feature_name}/tasks.md` - Planification et décomposition des tâches

## Architecture de Configuration

### 📋 Fichiers d'Instructions
- **`.github/instructions/context.instructions.md`** - Instructions Phase 1 (contexte)
- **`.github/instructions/spec.instructions.md`** - Instructions Phase 2 (spécifications)

### 🤖 Prompts Système
- **`.github/prompts/context.prompt.md`** - Prompt système Phase 1
- **`.github/prompts/spec.prompt.md`** - Prompt système Phase 2

### 💬 Modes Conversationnels
- **`.github/chatmodes/context.chatmode.md`** - Configuration chat Phase 1
- **`.github/chatmodes/spec.chatmode.md`** - Configuration chat Phase 2

### 🔍 Validation et Maintenance
- **`scripts/validate-sdd-coherence.js`** - Script validation automatisée complète
- **`.github/README-spec.md`** - Documentation complète du système

## Commandes Disponibles

### Phase 1 - Commandes Contexte
```bash
/kiro-context project-name          # Workflow complet contexte (product → structure → tech)
/sdd-context project-name           # Génération contexte selon méthodologie SDD
/kiro-sdd-context project-name      # Mode contexte SDD spécialisé
```

#### Commandes Contexte Ciblées
```bash
/kiro-product project-name          # Génère uniquement product.md
/kiro-structure project-name        # Génère uniquement structure.md
/kiro-tech project-name             # Génère uniquement tech.md
```

#### Commandes Contexte Utilitaires
```bash
/context-validate                   # Validation du contexte existant
/context-audit                      # Audit complet de l'environnement projet
```

### Phase 2 - Commandes Spécifications
```bash
/kiro-spec feature-name             # Workflow complet requirements → design → tasks
/sdd-generate feature-name          # Génération séquentielle des trois fichiers
/kiro-sdd-spec feature-name         # Mode SDD spécialisé
```

#### Commandes Spécifications Ciblées
```bash
/kiro-requirements feature-name     # Génère uniquement requirements.md
/kiro-design feature-name           # Génère uniquement design.md
/kiro-tasks feature-name            # Génère uniquement tasks.md
```

#### Commandes Spécifications Utilitaires
```bash
/spec-validate path/to/spec         # Validation de conformité SDD
/spec-refine path/to/spec           # Affinement et optimisation
/spec-review path/to/spec           # Révision ciblée
```

## Workflow Complet SDD

### Phase 1 : Établissement du Contexte
```
Utilisateur: /kiro-context e-commerce-platform
Copilot: � Établissement Contexte - E-commerce Platform
         Phase 1/3 : Product Context en cours...
         ✅ product.md généré avec vision métier
         **Validation Product :** Ce contexte produit vous convient-il ?
Utilisateur: Validé
Copilot: 🏗️ Phase 2/3 : Structure Context en cours...
         ✅ structure.md généré avec architecture
         **Validation Structure :** Ce contexte organisationnel vous convient-il ?
Utilisateur: Validé
Copilot: ⚙️ Phase 3/3 : Tech Context en cours...
         ✅ tech.md généré avec stack technique
         **Validation Tech :** Ce contexte technique vous convient-il ?
Utilisateur: Validé - contexte complet
```

### Phase 2 : Génération des Spécifications
```
Utilisateur: /kiro-spec user-authentication
Copilot: 🔄 Génération SDD - User Authentication
         Contexte validé ✅ Transition vers spécifications...
         Phase 1/3 : Requirements en cours...
         ✅ requirements.md généré avec 12 exigences EARS
         **Validation Requirements :** Cette documentation vous convient-elle ?
Utilisateur: Validé
Copilot: 🏗️ Phase 2/3 : Design en cours...
         ✅ design.md généré avec architecture Mermaid
         **Validation Design :** Cette conception vous convient-elle ?
Utilisateur: Validé
Copilot: 📅 Phase 3/3 : Tasks en cours...
         ✅ tasks.md généré avec 23 tâches, estimation 45 jours
         **Validation Tasks :** Cette planification vous convient-elle ?
Utilisateur: Validé - spécifications complètes
```

## Validation et Maintenance

### Vérification de Cohérence
```bash
node scripts/validate-sdd-coherence.js
```

### Checksums des Templates
Le script génère automatiquement les checksums des templates SDD pour détecter les modifications :
- `requirements.template.md`: `10bbf4c3a83502d3bf6c776ff136431d`
- `design.template.md`: `35ca5d284bb080f4a8450bc33284b3ea`  
- `tasks.template.md`: `197bc0846eff5000395917c79200bc1a`

### Résolution de Problèmes
- **Contexte saturé** : Le système utilise le chunking automatique et la priorisation
- **Validation bloquée** : Templates de révision disponibles en versions concise/détaillée
- **Références manquantes** : Validation automatique des liens vers les templates
- **Incohérences** : Script de validation avec rapport détaillé

## Intégration et Cohérence

### Workflow Phase 1 → Phase 2
La transition entre les phases est automatiquement optimisée :
- **Contexte → Requirements** : Vision produit vers exigences fonctionnelles
- **Architecture → Design** : Structure existante vers conception détaillée
- **Tech Stack → Tasks** : Contraintes techniques vers planification réaliste

### Validation Automatisée
```bash
node scripts/validate-sdd-coherence.js
```

**Vérifications Effectuées :**
- ✅ Cohérence terminologique (validation explicite, cycle feedback-révision, etc.)
- ✅ Références croisées entre fichiers Phase 1 et Phase 2
- ✅ Formats de validation standardisés
- ✅ Commandes de déclenchement complètes
- ✅ Checksums des templates SDD

### Checksums Templates SDD
```
requirements.template.md: 10bbf4c3a83502d3bf6c776ff136431d
design.template.md:       35ca5d284bb080f4a8450bc33284b3ea
tasks.template.md:        197bc0846eff5000395917c79200bc1a
```

## Standards Respectés

- ✅ **Méthodologie SDD** : Workflow séquentiel avec validation explicite
- ✅ **Format EARS** : "The system SHALL/SHOULD/MAY..." pour requirements
- ✅ **Diagrammes Mermaid** : Architecture et flux intégrés au design
- ✅ **Checklists** : Format structuré pour tasks et validations
- ✅ **Kebab-case** : Convention de nommage des fonctionnalités
- ✅ **Français formel** : Style et terminologie cohérents
- ✅ **Validation explicite** : Progression conditionnée à l'approbation utilisateur

---

**Configuration SDD Complète** - Les Phases 1 et 2 sont maintenant prêtes pour une utilisation en production avec GitHub Copilot. Le système offre un workflow SDD complet, de l'établissement du contexte projet jusqu'à la génération de spécifications détaillées, avec validation automatisée et cohérence garantie.
