# Prompt Spec Workflow

## Workflow de spécification complet

Ce workflow combine tous les sous-workflows pour créer une spécification complète :

1. **Clarification des exigences** - [Voir prompt-spec-workflow-requirement-clarification.md]
2. **Recherche et design** - [Voir prompt-spec-workflow-research-design.md]
3. **Plan d'implémentation** - [Voir prompt-spec-workflow-implementation-plan.md]
4. **Dépannage** - [Voir prompt-spec-workflow-troubleshooting.md]

Chaque étape doit être validée par l'utilisateur avant de passer à la suivante.